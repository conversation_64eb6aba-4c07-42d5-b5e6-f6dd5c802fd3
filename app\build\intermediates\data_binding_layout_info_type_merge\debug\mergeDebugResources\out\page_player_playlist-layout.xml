<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="page_player_playlist" modulePackage="com.example.aimusicplayer" filePath="app\src\main\res\layout\page_player_playlist.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.LinearLayout"><Targets><Target tag="layout/page_player_playlist_0" view="LinearLayout"><Expressions/><location startLine="1" startOffset="0" endLine="151" endOffset="14"/></Target><Target id="@+id/layout_play_mode" view="LinearLayout"><Expressions/><location startLine="18" startOffset="8" endLine="46" endOffset="22"/></Target><Target id="@+id/image_play_mode" view="ImageView"><Expressions/><location startLine="30" startOffset="12" endLine="35" endOffset="46"/></Target><Target id="@+id/text_play_mode" view="TextView"><Expressions/><location startLine="37" startOffset="12" endLine="44" endOffset="41"/></Target><Target id="@+id/text_song_count" view="TextView"><Expressions/><location startLine="49" startOffset="8" endLine="56" endOffset="37"/></Target><Target id="@+id/btn_shuffle_playlist" view="ImageButton"><Expressions/><location startLine="59" startOffset="8" endLine="70" endOffset="42"/></Target><Target id="@+id/btn_clear_playlist" view="ImageButton"><Expressions/><location startLine="73" startOffset="8" endLine="83" endOffset="42"/></Target><Target id="@+id/recycler_view_playlist" view="androidx.recyclerview.widget.RecyclerView"><Expressions/><location startLine="102" startOffset="8" endLine="112" endOffset="54"/></Target><Target id="@+id/layout_empty_state" view="LinearLayout"><Expressions/><location startLine="115" startOffset="8" endLine="147" endOffset="22"/></Target></Targets></Layout>