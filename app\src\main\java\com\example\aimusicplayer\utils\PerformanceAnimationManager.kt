package com.example.aimusicplayer.utils

import android.animation.*
import android.os.Handler
import android.os.Looper
import android.view.View
import android.view.animation.AccelerateDecelerateInterpolator
import android.view.animation.LinearInterpolator
import androidx.core.animation.doOnEnd
import androidx.core.animation.doOnStart
import java.util.concurrent.ConcurrentHashMap

/**
 * 高性能动画管理器
 * 优化动画性能，减少阻塞，确保流畅体验
 */
object PerformanceAnimationManager {
    private const val TAG = "PerformanceAnimationManager"

    // 动画缓存池，避免重复创建动画对象
    private val animatorPool = ConcurrentHashMap<String, ObjectAnimator>()
    private val animatorSetPool = ConcurrentHashMap<String, AnimatorSet>()

    // 活跃动画跟踪
    private val activeAnimators = ConcurrentHashMap<Int, Animator>()

    // 主线程Handler
    private val mainHandler = Handler(Looper.getMainLooper())

    // 动画配置常量
    private const val DEFAULT_DURATION = 300L
    private const val FAST_DURATION = 150L
    private const val SLOW_DURATION = 500L

    /**
     * 高性能旋转动画 - 针对黑胶唱片优化
     */
    fun startOptimizedRotation(
        view: View,
        duration: Long = 20000L,
        fromAngle: Float = 0f,
    ): ObjectAnimator? {
        val viewId = view.id

        // 停止现有动画
        stopAnimation(viewId)

        // 创建优化的旋转动画
        val animator = ObjectAnimator.ofFloat(view, View.ROTATION, fromAngle, fromAngle + 360f).apply {
            this.duration = duration
            interpolator = LinearInterpolator()
            repeatCount = ValueAnimator.INFINITE
            repeatMode = ValueAnimator.RESTART

            // 优化：减少更新频率，提高性能
            addUpdateListener { animation ->
                // 只在必要时更新UI
                if (animation.animatedFraction % 0.1f < 0.01f) {
                    view.invalidate()
                }
            }

            doOnStart {
                // 启用硬件加速
                view.setLayerType(View.LAYER_TYPE_HARDWARE, null)
            }

            doOnEnd {
                // 恢复软件渲染
                view.setLayerType(View.LAYER_TYPE_NONE, null)
            }
        }

        // 缓存动画
        activeAnimators[viewId] = animator

        // 在主线程启动动画
        mainHandler.post {
            animator.start()
        }

        return animator
    }

    /**
     * 高性能按钮点击动画
     */
    fun performButtonClick(
        view: View,
        scaleRatio: Float = 0.95f,
        duration: Long = FAST_DURATION,
    ) {
        val viewId = view.id
        val cacheKey = "button_click_$viewId"

        // 尝试从缓存获取动画
        var animatorSet = animatorSetPool[cacheKey]

        if (animatorSet == null) {
            // 创建新的动画集合
            val scaleDown = ObjectAnimator.ofPropertyValuesHolder(
                view,
                PropertyValuesHolder.ofFloat(View.SCALE_X, 1f, scaleRatio),
                PropertyValuesHolder.ofFloat(View.SCALE_Y, 1f, scaleRatio),
            ).apply {
                this.duration = duration / 2
                interpolator = AccelerateDecelerateInterpolator()
            }

            val scaleUp = ObjectAnimator.ofPropertyValuesHolder(
                view,
                PropertyValuesHolder.ofFloat(View.SCALE_X, scaleRatio, 1f),
                PropertyValuesHolder.ofFloat(View.SCALE_Y, scaleRatio, 1f),
            ).apply {
                this.duration = duration / 2
                interpolator = AccelerateDecelerateInterpolator()
            }

            animatorSet = AnimatorSet().apply {
                playSequentially(scaleDown, scaleUp)

                doOnStart {
                    view.setLayerType(View.LAYER_TYPE_HARDWARE, null)
                }

                doOnEnd {
                    view.setLayerType(View.LAYER_TYPE_NONE, null)
                }
            }

            // 缓存动画
            animatorSetPool[cacheKey] = animatorSet
        }

        // 停止现有动画
        stopAnimation(viewId)

        // 启动动画
        activeAnimators[viewId] = animatorSet
        mainHandler.post {
            animatorSet.start()
        }
    }

    /**
     * 高性能淡入淡出动画
     */
    fun performFadeAnimation(
        view: View,
        targetAlpha: Float,
        duration: Long = DEFAULT_DURATION,
        onComplete: (() -> Unit)? = null,
    ) {
        val viewId = view.id
        val cacheKey = "fade_${viewId}_$targetAlpha"

        // 停止现有动画
        stopAnimation(viewId)

        // 尝试从缓存获取动画
        var animator = animatorPool[cacheKey]

        if (animator == null) {
            animator = ObjectAnimator.ofFloat(view, View.ALPHA, view.alpha, targetAlpha).apply {
                this.duration = duration
                interpolator = AccelerateDecelerateInterpolator()

                doOnStart {
                    if (targetAlpha > 0f && view.visibility != View.VISIBLE) {
                        view.visibility = View.VISIBLE
                    }
                }

                doOnEnd {
                    if (targetAlpha == 0f) {
                        view.visibility = View.GONE
                    }
                    onComplete?.invoke()
                }
            }

            // 缓存动画
            animatorPool[cacheKey] = animator
        }

        // 更新目标值
        animator?.setFloatValues(view.alpha, targetAlpha)

        // 启动动画
        animator?.let { anim ->
            activeAnimators[viewId] = anim
            mainHandler.post {
                anim.start()
            }
        }
    }

    /**
     * 高性能收藏按钮动画
     */
    fun performCollectAnimation(
        view: View,
        isCollected: Boolean,
        duration: Long = DEFAULT_DURATION,
    ) {
        val viewId = view.id

        // 停止现有动画
        stopAnimation(viewId)

        // 创建优化的收藏动画
        val scaleUp = ObjectAnimator.ofPropertyValuesHolder(
            view,
            PropertyValuesHolder.ofFloat(View.SCALE_X, 1f, 1.2f),
            PropertyValuesHolder.ofFloat(View.SCALE_Y, 1f, 1.2f),
        ).apply {
            this.duration = duration / 3
            interpolator = AccelerateDecelerateInterpolator()
        }

        val scaleDown = ObjectAnimator.ofPropertyValuesHolder(
            view,
            PropertyValuesHolder.ofFloat(View.SCALE_X, 1.2f, 1f),
            PropertyValuesHolder.ofFloat(View.SCALE_Y, 1.2f, 1f),
        ).apply {
            this.duration = duration / 3
            interpolator = AccelerateDecelerateInterpolator()
        }

        val heartbeat = if (isCollected) {
            ObjectAnimator.ofPropertyValuesHolder(
                view,
                PropertyValuesHolder.ofFloat(View.SCALE_X, 1f, 1.1f, 1f),
                PropertyValuesHolder.ofFloat(View.SCALE_Y, 1f, 1.1f, 1f),
            ).apply {
                this.duration = duration / 3
                interpolator = AccelerateDecelerateInterpolator()
            }
        } else {
            null
        }

        val animatorSet = AnimatorSet().apply {
            if (heartbeat != null) {
                playSequentially(scaleUp, scaleDown, heartbeat)
            } else {
                playSequentially(scaleUp, scaleDown)
            }

            doOnStart {
                view.setLayerType(View.LAYER_TYPE_HARDWARE, null)
            }

            doOnEnd {
                view.setLayerType(View.LAYER_TYPE_NONE, null)
                view.isSelected = isCollected
            }
        }

        // 启动动画
        activeAnimators[viewId] = animatorSet
        mainHandler.post {
            animatorSet.start()
        }
    }

    /**
     * 停止指定视图的动画
     */
    fun stopAnimation(viewId: Int) {
        activeAnimators[viewId]?.let { animator ->
            if (animator.isRunning) {
                animator.cancel()
            }
            activeAnimators.remove(viewId)
        }
    }

    /**
     * 停止所有动画
     */
    fun stopAllAnimations() {
        activeAnimators.values.forEach { animator ->
            if (animator.isRunning) {
                animator.cancel()
            }
        }
        activeAnimators.clear()
    }

    /**
     * 清理动画缓存
     */
    fun clearCache() {
        animatorPool.clear()
        animatorSetPool.clear()
    }

    /**
     * 获取活跃动画数量
     */
    fun getActiveAnimationCount(): Int {
        return activeAnimators.size
    }

    /**
     * 检查是否有动画正在运行
     */
    fun hasRunningAnimations(): Boolean {
        return activeAnimators.values.any { it.isRunning }
    }
}
