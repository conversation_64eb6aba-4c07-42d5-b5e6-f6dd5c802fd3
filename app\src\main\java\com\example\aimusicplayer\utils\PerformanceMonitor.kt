package com.example.aimusicplayer.utils

import android.util.Log

/**
 * 性能监控工具类
 * 严格按照ponymusic标准和Android Automotive要求
 */
object PerformanceMonitor {
    private const val TAG = "PerformanceMonitor"

    // Android Automotive性能标准
    private const val MAX_UI_RESPONSE_TIME_MS = 200L // UI响应时间<200ms
    private const val MAX_FRAME_TIME_MS = 16.67f // 60fps = 16.67ms/frame
    private const val MAX_ANIMATION_FRAME_TIME_MS = 33.33f // 30fps = 33.33ms/frame
    private const val MAX_LYRIC_SYNC_ERROR_MS = 100L // 歌词同步误差<100ms
    private const val MAX_NOTIFICATION_UPDATE_TIME_MS = 500L // 通知栏同步<500ms
    private const val MAX_BLUR_GENERATION_TIME_MS = 500L // 模糊背景生成<500ms

    /**
     * 监控UI响应时间
     * @param operation 操作名称
     * @param startTime 开始时间（纳秒）
     * @param endTime 结束时间（纳秒）
     */
    fun monitorUIResponse(operation: String, startTime: Long, endTime: Long = System.nanoTime()) {
        val responseTime = (endTime - startTime) / 1_000_000L // 转换为毫秒

        if (responseTime > MAX_UI_RESPONSE_TIME_MS) {
            Log.w(TAG, "UI响应时间警告: $operation = ${responseTime}ms > ${MAX_UI_RESPONSE_TIME_MS}ms")
        } else {
            Log.d(TAG, "UI响应时间正常: $operation = ${responseTime}ms")
        }
    }

    /**
     * 监控帧率性能
     * @param operation 操作名称
     * @param frameTime 帧时间（毫秒）
     * @param isAnimation 是否为动画（动画要求30fps，UI要求60fps）
     */
    fun monitorFrameRate(operation: String, frameTime: Float, isAnimation: Boolean = false) {
        val maxFrameTime = if (isAnimation) MAX_ANIMATION_FRAME_TIME_MS else MAX_FRAME_TIME_MS
        val targetFps = if (isAnimation) 30 else 60

        if (frameTime > maxFrameTime) {
            Log.w(TAG, "帧率性能警告: $operation = ${frameTime}ms > ${maxFrameTime}ms (目标${targetFps}fps)")
        } else {
            Log.d(TAG, "帧率性能正常: $operation = ${frameTime}ms (目标${targetFps}fps)")
        }
    }

    /**
     * 监控歌词同步性能
     * @param syncError 同步误差（毫秒）
     */
    fun monitorLyricSync(syncError: Long) {
        if (syncError > MAX_LYRIC_SYNC_ERROR_MS) {
            Log.w(TAG, "歌词同步误差警告: ${syncError}ms > ${MAX_LYRIC_SYNC_ERROR_MS}ms")
        } else {
            Log.d(TAG, "歌词同步误差正常: ${syncError}ms")
        }
    }

    /**
     * 监控通知栏更新性能
     * @param updateTime 更新时间（毫秒）
     */
    fun monitorNotificationUpdate(updateTime: Long) {
        if (updateTime > MAX_NOTIFICATION_UPDATE_TIME_MS) {
            Log.w(TAG, "通知栏更新时间警告: ${updateTime}ms > ${MAX_NOTIFICATION_UPDATE_TIME_MS}ms")
        } else {
            Log.d(TAG, "通知栏更新时间正常: ${updateTime}ms")
        }
    }

    /**
     * 监控模糊背景生成性能
     * @param generationTime 生成时间（毫秒）
     */
    fun monitorBlurGeneration(generationTime: Long) {
        if (generationTime > MAX_BLUR_GENERATION_TIME_MS) {
            Log.w(TAG, "模糊背景生成时间警告: ${generationTime}ms > ${MAX_BLUR_GENERATION_TIME_MS}ms")
        } else {
            Log.d(TAG, "模糊背景生成时间正常: ${generationTime}ms")
        }
    }

    /**
     * 监控播放列表操作性能
     * @param operation 操作名称
     * @param operationTime 操作时间（毫秒）
     */
    fun monitorPlaylistOperation(operation: String, operationTime: Long) {
        val maxTime = 100L // 播放列表操作<100ms

        if (operationTime > maxTime) {
            Log.w(TAG, "播放列表操作时间警告: $operation = ${operationTime}ms > ${maxTime}ms")
        } else {
            Log.d(TAG, "播放列表操作时间正常: $operation = ${operationTime}ms")
        }
    }

    /**
     * 监控歌词解析性能
     * @param parseTime 解析时间（毫秒）
     * @param lyricCount 歌词行数
     */
    fun monitorLyricParsing(parseTime: Long, lyricCount: Int) {
        val maxTime = 200L // 歌词解析<200ms

        if (parseTime > maxTime) {
            Log.w(TAG, "歌词解析时间警告: ${parseTime}ms > ${maxTime}ms (${lyricCount}行)")
        } else {
            Log.d(TAG, "歌词解析时间正常: ${parseTime}ms (${lyricCount}行)")
        }
    }

    /**
     * 监控收藏状态检查性能
     * @param checkTime 检查时间（毫秒）
     */
    fun monitorFavoriteCheck(checkTime: Long) {
        val maxTime = 50L // 收藏状态检查<50ms

        if (checkTime > maxTime) {
            Log.w(TAG, "收藏状态检查时间警告: ${checkTime}ms > ${maxTime}ms")
        } else {
            Log.d(TAG, "收藏状态检查时间正常: ${checkTime}ms")
        }
    }

    /**
     * 创建性能监控器
     * @param operation 操作名称
     * @return 性能监控器实例
     */
    fun createMonitor(operation: String): PerformanceTimer {
        return PerformanceTimer(operation)
    }

    /**
     * 性能计时器类
     */
    class PerformanceTimer(private val operation: String) {
        private val startTime = System.nanoTime()

        /**
         * 结束监控并记录UI响应时间
         */
        fun endUIResponse() {
            monitorUIResponse(operation, startTime)
        }

        /**
         * 结束监控并记录帧率性能
         * @param isAnimation 是否为动画
         */
        fun endFrameRate(isAnimation: Boolean = false) {
            val frameTime = (System.nanoTime() - startTime) / 1_000_000f
            monitorFrameRate(operation, frameTime, isAnimation)
        }

        /**
         * 结束监控并记录播放列表操作性能
         */
        fun endPlaylistOperation() {
            val operationTime = (System.nanoTime() - startTime) / 1_000_000L
            monitorPlaylistOperation(operation, operationTime)
        }

        /**
         * 结束监控并记录歌词解析性能
         * @param lyricCount 歌词行数
         */
        fun endLyricParsing(lyricCount: Int) {
            val parseTime = (System.nanoTime() - startTime) / 1_000_000L
            monitorLyricParsing(parseTime, lyricCount)
        }

        /**
         * 结束监控并记录收藏状态检查性能
         */
        fun endFavoriteCheck() {
            val checkTime = (System.nanoTime() - startTime) / 1_000_000L
            monitorFavoriteCheck(checkTime)
        }
    }
}
