package com.example.aimusicplayer.ui.player

import android.util.Log
import androidx.fragment.app.Fragment
import androidx.viewpager2.adapter.FragmentStateAdapter

/**
 * 播放器ViewPager2适配器
 * 用于管理歌词、评论和播放列表页面
 */
class PlayerPagerAdapter(fragment: Fragment) : FragmentStateAdapter(fragment) {

    private val TAG = "PlayerPagerAdapter"
    private var currentFragment: Fragment? = null

    // 页面类型
    companion object {
        const val PAGE_LYRIC = 0
        const val PAGE_COMMENT = 1
        const val PAGE_PLAYLIST = 2
        const val PAGE_COUNT = 3 // 支持歌词、评论、播放列表三个页面
    }

    override fun getItemCount(): Int = PAGE_COUNT

    override fun createFragment(position: Int): Fragment {
        Log.d(TAG, "创建Fragment，位置: $position")

        val fragment = when (position) {
            PAGE_LYRIC -> {
                Log.d(TAG, "创建歌词页面Fragment")
                LyricPageFragment()
            }
            PAGE_COMMENT -> {
                Log.d(TAG, "创建评论页面Fragment")
                CommentPageFragment()
            }
            PAGE_PLAYLIST -> {
                Log.d(TAG, "创建播放列表页面Fragment")
                PlaylistPageFragment()
            }
            else -> {
                Log.w(TAG, "未知页面位置: $position，使用默认歌词页面")
                LyricPageFragment()
            }
        }

        currentFragment = fragment
        return fragment
    }

    /**
     * 获取当前Fragment
     */
    fun getCurrentFragment(): Fragment? {
        return currentFragment
    }

    /**
     * 获取页面标题
     */
    fun getPageTitle(position: Int): String {
        return when (position) {
            PAGE_LYRIC -> "歌词"
            PAGE_COMMENT -> "评论"
            PAGE_PLAYLIST -> "列表"
            else -> "未知"
        }
    }
}
