package com.example.aimusicplayer.utils

import android.util.Log
import kotlinx.coroutines.delay
import kotlin.random.Random

/**
 * 错误恢复工具类
 * 实现网络错误的自动重试机制、降级策略和用户友好的错误提示
 * 严格按照ponymusic-master项目错误处理标准
 */
object ErrorRecoveryUtils {

    private const val TAG = "ErrorRecoveryUtils"

    // 重试配置
    private const val MAX_RETRY_COUNT = 3
    private const val BASE_DELAY_MS = 1000L
    private const val MAX_DELAY_MS = 10000L

    // 错误统计
    private var totalErrors = 0
    private var recoveredErrors = 0
    private var unrecoverableErrors = 0

    /**
     * 带重试的API调用
     * @param maxRetries 最大重试次数
     * @param baseDelayMs 基础延迟时间
     * @param maxDelayMs 最大延迟时间
     * @param operation 要执行的操作
     * @return 操作结果
     */
    suspend fun <T> retryWithBackoff(
        maxRetries: Int = MAX_RETRY_COUNT,
        baseDelayMs: Long = BASE_DELAY_MS,
        maxDelayMs: Long = MAX_DELAY_MS,
        operation: suspend () -> T,
    ): Result<T> {
        totalErrors++

        var lastException: Exception? = null

        repeat(maxRetries + 1) { attempt ->
            try {
                val result = operation()
                if (attempt > 0) {
                    recoveredErrors++
                    Log.i(TAG, "操作在第${attempt + 1}次尝试后成功")
                }
                return Result.success(result)
            } catch (e: Exception) {
                lastException = e
                Log.w(TAG, "操作失败，尝试次数: ${attempt + 1}/${maxRetries + 1}", e)

                if (attempt < maxRetries) {
                    val delayTime = calculateBackoffDelay(attempt, baseDelayMs, maxDelayMs)
                    Log.d(TAG, "等待${delayTime}ms后重试...")
                    delay(delayTime)
                }
            }
        }

        unrecoverableErrors++
        Log.e(TAG, "操作在${maxRetries + 1}次尝试后仍然失败", lastException)
        return Result.failure(lastException ?: Exception("未知错误"))
    }

    /**
     * 计算指数退避延迟时间
     * @param attempt 当前尝试次数
     * @param baseDelayMs 基础延迟时间
     * @param maxDelayMs 最大延迟时间
     * @return 延迟时间
     */
    private fun calculateBackoffDelay(attempt: Int, baseDelayMs: Long, maxDelayMs: Long): Long {
        val exponentialDelay = baseDelayMs * (1L shl attempt) // 2^attempt
        val jitter = Random.nextLong(0, baseDelayMs / 2) // 添加随机抖动
        return minOf(exponentialDelay + jitter, maxDelayMs)
    }

    /**
     * 降级策略执行器
     * @param primaryOperation 主要操作
     * @param fallbackOperation 备用操作
     * @param cacheOperation 缓存操作
     * @param defaultOperation 默认操作
     * @return 操作结果
     */
    suspend fun <T> executeWithFallback(
        primaryOperation: suspend () -> T,
        fallbackOperation: (suspend () -> T)? = null,
        cacheOperation: (suspend () -> T)? = null,
        defaultOperation: (suspend () -> T)? = null,
    ): FallbackResult<T> {
        // 尝试主要操作
        try {
            val result = primaryOperation()
            Log.d(TAG, "主要操作成功")
            return FallbackResult.success(result, FallbackLevel.PRIMARY)
        } catch (e: Exception) {
            Log.w(TAG, "主要操作失败，尝试降级", e)
        }

        // 尝试备用操作
        fallbackOperation?.let { fallback ->
            try {
                val result = fallback()
                Log.i(TAG, "备用操作成功")
                return FallbackResult.success(result, FallbackLevel.FALLBACK)
            } catch (e: Exception) {
                Log.w(TAG, "备用操作失败，尝试缓存", e)
            }
        }

        // 尝试缓存操作
        cacheOperation?.let { cache ->
            try {
                val result = cache()
                Log.i(TAG, "缓存操作成功")
                return FallbackResult.success(result, FallbackLevel.CACHE)
            } catch (e: Exception) {
                Log.w(TAG, "缓存操作失败，尝试默认值", e)
            }
        }

        // 尝试默认操作
        defaultOperation?.let { default ->
            try {
                val result = default()
                Log.i(TAG, "默认操作成功")
                return FallbackResult.success(result, FallbackLevel.DEFAULT)
            } catch (e: Exception) {
                Log.e(TAG, "默认操作也失败", e)
            }
        }

        Log.e(TAG, "所有降级策略都失败")
        return FallbackResult.failure("所有操作都失败")
    }

    /**
     * 网络错误分类和处理
     * @param exception 异常对象
     * @return 错误类型和建议的处理方式
     */
    fun classifyNetworkError(exception: Throwable): NetworkErrorInfo {
        return when {
            exception.message?.contains("timeout", ignoreCase = true) == true -> {
                NetworkErrorInfo(
                    type = NetworkErrorType.TIMEOUT,
                    userMessage = "网络连接超时，请检查网络后重试",
                    shouldRetry = true,
                    retryDelay = 2000L,
                )
            }

            exception.message?.contains("connection", ignoreCase = true) == true -> {
                NetworkErrorInfo(
                    type = NetworkErrorType.CONNECTION_FAILED,
                    userMessage = "网络连接失败，请检查网络设置",
                    shouldRetry = true,
                    retryDelay = 3000L,
                )
            }

            exception.message?.contains("404", ignoreCase = true) == true -> {
                NetworkErrorInfo(
                    type = NetworkErrorType.NOT_FOUND,
                    userMessage = "请求的资源不存在",
                    shouldRetry = false,
                    retryDelay = 0L,
                )
            }

            exception.message?.contains("401", ignoreCase = true) == true ||
                exception.message?.contains("403", ignoreCase = true) == true -> {
                NetworkErrorInfo(
                    type = NetworkErrorType.AUTHENTICATION_FAILED,
                    userMessage = "身份验证失败，请重新登录",
                    shouldRetry = false,
                    retryDelay = 0L,
                )
            }

            exception.message?.contains("500", ignoreCase = true) == true ||
                exception.message?.contains("502", ignoreCase = true) == true ||
                exception.message?.contains("503", ignoreCase = true) == true -> {
                NetworkErrorInfo(
                    type = NetworkErrorType.SERVER_ERROR,
                    userMessage = "服务器暂时不可用，请稍后重试",
                    shouldRetry = true,
                    retryDelay = 5000L,
                )
            }

            exception.message?.contains("json", ignoreCase = true) == true ||
                exception.message?.contains("parse", ignoreCase = true) == true -> {
                NetworkErrorInfo(
                    type = NetworkErrorType.PARSE_ERROR,
                    userMessage = "数据解析失败，请稍后重试",
                    shouldRetry = true,
                    retryDelay = 1000L,
                )
            }

            else -> {
                NetworkErrorInfo(
                    type = NetworkErrorType.UNKNOWN,
                    userMessage = "网络请求失败，请稍后重试",
                    shouldRetry = true,
                    retryDelay = 2000L,
                )
            }
        }
    }

    /**
     * 智能重试策略
     * 根据错误类型和历史记录决定是否重试
     * @param exception 异常对象
     * @param attemptCount 当前尝试次数
     * @return 是否应该重试
     */
    fun shouldRetry(exception: Throwable, attemptCount: Int): Boolean {
        val errorInfo = classifyNetworkError(exception)

        if (!errorInfo.shouldRetry) {
            return false
        }

        if (attemptCount >= MAX_RETRY_COUNT) {
            return false
        }

        // 根据错误类型调整重试策略
        return when (errorInfo.type) {
            NetworkErrorType.TIMEOUT -> attemptCount < 2
            NetworkErrorType.CONNECTION_FAILED -> attemptCount < 3
            NetworkErrorType.SERVER_ERROR -> attemptCount < 2
            NetworkErrorType.PARSE_ERROR -> attemptCount < 1
            else -> attemptCount < MAX_RETRY_COUNT
        }
    }

    /**
     * 获取错误恢复统计
     * @return 错误恢复统计信息
     */
    fun getErrorRecoveryStats(): ErrorRecoveryStats {
        val recoveryRate = if (totalErrors > 0) {
            recoveredErrors.toDouble() / totalErrors
        } else {
            1.0
        }

        return ErrorRecoveryStats(
            totalErrors = totalErrors,
            recoveredErrors = recoveredErrors,
            unrecoverableErrors = unrecoverableErrors,
            recoveryRate = recoveryRate,
        )
    }

    /**
     * 重置错误统计
     */
    fun resetErrorStats() {
        totalErrors = 0
        recoveredErrors = 0
        unrecoverableErrors = 0
    }

    /**
     * 降级结果数据类
     */
    data class FallbackResult<T>(
        val data: T?,
        val level: FallbackLevel,
        val isSuccess: Boolean,
        val errorMessage: String? = null,
    ) {
        companion object {
            fun <T> success(data: T, level: FallbackLevel): FallbackResult<T> {
                return FallbackResult(data, level, true)
            }

            fun <T> failure(errorMessage: String): FallbackResult<T> {
                return FallbackResult(null, FallbackLevel.NONE, false, errorMessage)
            }
        }
    }

    /**
     * 降级级别枚举
     */
    enum class FallbackLevel {
        PRIMARY, // 主要操作
        FALLBACK, // 备用操作
        CACHE, // 缓存操作
        DEFAULT, // 默认操作
        NONE, // 无可用操作
    }

    /**
     * 网络错误类型枚举
     */
    enum class NetworkErrorType {
        TIMEOUT, // 超时
        CONNECTION_FAILED, // 连接失败
        NOT_FOUND, // 资源不存在
        AUTHENTICATION_FAILED, // 身份验证失败
        SERVER_ERROR, // 服务器错误
        PARSE_ERROR, // 解析错误
        UNKNOWN, // 未知错误
    }

    /**
     * 网络错误信息数据类
     */
    data class NetworkErrorInfo(
        val type: NetworkErrorType,
        val userMessage: String,
        val shouldRetry: Boolean,
        val retryDelay: Long,
    )

    /**
     * 错误恢复统计数据类
     */
    data class ErrorRecoveryStats(
        val totalErrors: Int,
        val recoveredErrors: Int,
        val unrecoverableErrors: Int,
        val recoveryRate: Double,
    ) {
        fun getFormattedStats(): String {
            return """
                错误恢复统计:
                - 总错误次数: $totalErrors
                - 恢复成功: $recoveredErrors
                - 无法恢复: $unrecoverableErrors
                - 恢复率: ${String.format("%.2f", recoveryRate * 100)}%
            """.trimIndent()
        }
    }
}

/**
 * 扩展函数：为挂起函数提供重试机制
 */
suspend fun <T> (suspend () -> T).retryOnFailure(
    maxRetries: Int = 3,
    baseDelayMs: Long = 1000L,
): Result<T> {
    return ErrorRecoveryUtils.retryWithBackoff(maxRetries, baseDelayMs, operation = this)
}

/**
 * 扩展函数：为挂起函数提供降级策略
 */
suspend fun <T> (suspend () -> T).withFallback(
    fallback: (suspend () -> T)? = null,
    cache: (suspend () -> T)? = null,
    default: (suspend () -> T)? = null,
): ErrorRecoveryUtils.FallbackResult<T> {
    return ErrorRecoveryUtils.executeWithFallback(this, fallback, cache, default)
}
