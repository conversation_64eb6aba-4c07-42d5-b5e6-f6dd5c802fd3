name: API监控和健康检查

on:
  # 定时监控 - 每小时执行一次
  schedule:
    - cron: '0 * * * *'  # 每小时的第0分钟
  
  # 手动触发
  workflow_dispatch:
    inputs:
      environment:
        description: '监控环境'
        required: true
        default: 'prod'
        type: choice
        options:
          - prod
          - dev
      timeout:
        description: '超时时间(秒)'
        required: false
        default: '30'
      retry:
        description: '重试次数'
        required: false
        default: '3'
  
  # 在构建完成后自动监控
  workflow_run:
    workflows: ["Android CI"]
    types:
      - completed

jobs:
  api-monitor:
    name: API状态监控
    runs-on: ubuntu-latest
    
    steps:
    - name: 检出代码
      uses: actions/checkout@v4
      
    - name: 设置Node.js环境
      uses: actions/setup-node@v4
      with:
        node-version: '18'
        
    - name: 创建脚本目录
      run: mkdir -p scripts
      
    - name: 运行API监控
      id: monitor
      run: |
        ENV="${{ github.event.inputs.environment || 'prod' }}"
        TIMEOUT="${{ github.event.inputs.timeout || '30' }}"
        RETRY="${{ github.event.inputs.retry || '3' }}"
        
        echo "开始API监控..."
        echo "环境: $ENV"
        echo "超时: ${TIMEOUT}s"
        echo "重试: ${RETRY}次"
        
        # 运行监控脚本
        node scripts/ci_api_monitor.js --env=$ENV --timeout=$TIMEOUT --retry=$RETRY --verbose
        
        # 保存退出码
        echo "exit_code=$?" >> $GITHUB_OUTPUT
      continue-on-error: true
      
    - name: 上传监控报告
      uses: actions/upload-artifact@v4
      if: always()
      with:
        name: api-monitor-report-${{ github.run_number }}
        path: ci_api_report.json
        retention-days: 30
        
    - name: 读取监控结果
      id: results
      if: always()
      run: |
        if [ -f "ci_api_report.json" ]; then
          # 提取关键指标
          TOTAL=$(jq -r '.summary.total' ci_api_report.json)
          PASSED=$(jq -r '.summary.passed' ci_api_report.json)
          FAILED=$(jq -r '.summary.failed' ci_api_report.json)
          CRITICAL_FAILED=$(jq -r '.summary.critical_failed' ci_api_report.json)
          PRIMARY_RATE=$(jq -r '.summary.primary_available' ci_api_report.json)
          BACKUP_RATE=$(jq -r '.summary.backup_available' ci_api_report.json)
          
          echo "total=$TOTAL" >> $GITHUB_OUTPUT
          echo "passed=$PASSED" >> $GITHUB_OUTPUT
          echo "failed=$FAILED" >> $GITHUB_OUTPUT
          echo "critical_failed=$CRITICAL_FAILED" >> $GITHUB_OUTPUT
          echo "primary_rate=$PRIMARY_RATE" >> $GITHUB_OUTPUT
          echo "backup_rate=$BACKUP_RATE" >> $GITHUB_OUTPUT
          
          # 计算成功率
          SUCCESS_RATE=$(echo "scale=1; $PASSED * 100 / $TOTAL" | bc)
          echo "success_rate=$SUCCESS_RATE" >> $GITHUB_OUTPUT
        else
          echo "监控报告文件不存在"
          echo "total=0" >> $GITHUB_OUTPUT
          echo "success_rate=0" >> $GITHUB_OUTPUT
        fi
        
    - name: 创建监控摘要
      if: always()
      run: |
        echo "## 🔍 API监控报告" >> $GITHUB_STEP_SUMMARY
        echo "" >> $GITHUB_STEP_SUMMARY
        echo "**监控时间:** $(date -u '+%Y-%m-%d %H:%M:%S UTC')" >> $GITHUB_STEP_SUMMARY
        echo "**监控环境:** ${{ github.event.inputs.environment || 'prod' }}" >> $GITHUB_STEP_SUMMARY
        echo "" >> $GITHUB_STEP_SUMMARY
        
        if [ "${{ steps.results.outputs.total }}" != "0" ]; then
          echo "### 📊 监控结果" >> $GITHUB_STEP_SUMMARY
          echo "- **总接口数:** ${{ steps.results.outputs.total }}" >> $GITHUB_STEP_SUMMARY
          echo "- **成功接口:** ${{ steps.results.outputs.passed }}" >> $GITHUB_STEP_SUMMARY
          echo "- **失败接口:** ${{ steps.results.outputs.failed }}" >> $GITHUB_STEP_SUMMARY
          echo "- **成功率:** ${{ steps.results.outputs.success_rate }}%" >> $GITHUB_STEP_SUMMARY
          echo "- **关键API失败:** ${{ steps.results.outputs.critical_failed }}" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          
          # 状态判断
          if [ "${{ steps.results.outputs.critical_failed }}" != "0" ]; then
            echo "### ❌ 状态: 严重异常" >> $GITHUB_STEP_SUMMARY
            echo "检测到关键API失败，需要立即处理！" >> $GITHUB_STEP_SUMMARY
          elif [ "${{ steps.results.outputs.failed }}" != "0" ]; then
            echo "### ⚠️ 状态: 部分异常" >> $GITHUB_STEP_SUMMARY
            echo "检测到非关键API失败，建议关注。" >> $GITHUB_STEP_SUMMARY
          else
            echo "### ✅ 状态: 正常" >> $GITHUB_STEP_SUMMARY
            echo "所有API运行正常。" >> $GITHUB_STEP_SUMMARY
          fi
        else
          echo "### ❌ 监控失败" >> $GITHUB_STEP_SUMMARY
          echo "无法获取监控数据，请检查脚本配置。" >> $GITHUB_STEP_SUMMARY
        fi
        
    - name: 发送通知 (关键API失败时)
      if: always() && steps.results.outputs.critical_failed != '0'
      uses: actions/github-script@v7
      with:
        script: |
          const { owner, repo } = context.repo;
          const runUrl = `https://github.com/${owner}/${repo}/actions/runs/${context.runId}`;
          
          await github.rest.issues.create({
            owner,
            repo,
            title: `🚨 关键API监控失败 - ${new Date().toISOString().split('T')[0]}`,
            body: `## API监控异常报告
            
            **监控时间:** ${new Date().toISOString()}
            **环境:** ${{ github.event.inputs.environment || 'prod' }}
            **关键API失败数:** ${{ steps.results.outputs.critical_failed }}
            **总失败数:** ${{ steps.results.outputs.failed }}
            
            ### 详细信息
            请查看工作流运行详情: ${runUrl}
            
            ### 建议操作
            1. 检查API服务器状态
            2. 验证网络连接
            3. 查看服务器日志
            4. 必要时切换到备用服务器
            
            ---
            *此问题由API监控自动创建*`,
            labels: ['bug', 'api-monitor', 'critical']
          });
          
    - name: 设置工作流状态
      if: always()
      run: |
        EXIT_CODE="${{ steps.monitor.outputs.exit_code }}"
        if [ "$EXIT_CODE" = "2" ]; then
          echo "关键API失败，工作流失败"
          exit 1
        elif [ "$EXIT_CODE" = "1" ]; then
          echo "非关键API失败，工作流警告但继续"
          exit 0
        else
          echo "所有API正常"
          exit 0
        fi

  # 生成监控趋势报告 (每日)
  trend-report:
    name: 生成监控趋势报告
    runs-on: ubuntu-latest
    needs: api-monitor
    if: github.event.schedule == '0 0 * * *'  # 仅在每日定时任务时运行
    
    steps:
    - name: 检出代码
      uses: actions/checkout@v4
      
    - name: 下载历史监控数据
      uses: actions/download-artifact@v4
      with:
        pattern: api-monitor-report-*
        path: reports/
        
    - name: 生成趋势报告
      run: |
        echo "## 📈 API监控趋势报告 (最近7天)" > trend-report.md
        echo "" >> trend-report.md
        echo "生成时间: $(date -u '+%Y-%m-%d %H:%M:%S UTC')" >> trend-report.md
        echo "" >> trend-report.md
        
        # 这里可以添加更复杂的趋势分析逻辑
        # 例如：成功率变化、响应时间趋势、故障频率等
        
        echo "### 监控数据文件" >> trend-report.md
        find reports/ -name "*.json" | head -10 >> trend-report.md
        
    - name: 上传趋势报告
      uses: actions/upload-artifact@v4
      with:
        name: api-trend-report-${{ github.run_number }}
        path: trend-report.md
        retention-days: 90
