package com.example.aimusicplayer.service

import android.app.Activity
import android.content.Intent
import android.util.Log
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.withContext
import org.json.JSONObject
import com.example.aimusicplayer.utils.toUnMutable
import com.example.aimusicplayer.data.model.User
import com.example.aimusicplayer.data.repository.UserRepository
import com.example.aimusicplayer.data.source.ApiService
import com.example.aimusicplayer.ui.login.LoginActivity
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 用户服务实现类 - 严格对齐ponymusic标准
 * 参考：ponymusic-master/app/src/main/java/me/wcy/music/account/service/UserServiceImpl.kt
 */
@Singleton
class UserServiceImpl @Inject constructor(
    private val userRepository: UserRepository,
    private val apiService: ApiService
) : UserService {

    companion object {
        private const val TAG = "UserServiceImpl"
    }

    // 用户信息StateFlow - 参考ponymusic实现
    private val _profile = MutableStateFlow<User?>(null)
    override val profile = _profile.toUnMutable()

    override fun getCookie(): String {
        return userRepository.getCookie()
    }

    override fun isLogin(): Boolean {
        return profile.value != null && getCookie().isNotEmpty()
    }

    override fun getUserId(): Long {
        return profile.value?.userId?.toLongOrNull() ?: 0L
    }

    /**
     * 登录核心方法 - 严格参考ponymusic实现
     * 参考：UserServiceImpl.login()
     */
    override suspend fun login(cookie: String): Result<User> {
        return withContext(Dispatchers.IO) {
            try {
                Log.d(TAG, "开始登录流程，Cookie长度: ${cookie.length}")

                // 1. 保存Cookie - 参考ponymusic先保存Cookie
                userRepository.saveCookie(cookie)

                // 2. 调用登录状态检查API - 参考ponymusic使用getLoginStatus
                val responseBody = apiService.getLoginStatus()
                val response = responseBody.string()
                Log.d(TAG, "登录状态检查响应: $response")

                // 3. 解析响应 - 参考ponymusic的解析逻辑
                val jsonObject = JSONObject(response)
                val code = jsonObject.optInt("code")

                if (code == 200) {
                    val data = jsonObject.optJSONObject("data")
                    val account = data?.optJSONObject("account")
                    val profileData = data?.optJSONObject("profile")

                    val status = account?.optInt("status", -1) ?: -1

                    if (status == 0 && profileData != null) {
                        // 登录成功，解析用户信息
                        val userId = profileData.optLong("userId", 0).toString()
                        val nickname = profileData.optString("nickname", "")
                        val avatarUrl = profileData.optString("avatarUrl", "")

                        val user = User(
                            userId = userId,
                            username = nickname,
                            token = cookie,
                            avatarUrl = avatarUrl
                        )

                        // 保存用户信息 - 参考ponymusic的保存逻辑
                        _profile.value = user
                        userRepository.saveUserToPrefs(user, true)

                        Log.d(TAG, "登录成功: userId=$userId, nickname=$nickname")
                        Result.success(user)
                    } else {
                        // 登录失败，清除Cookie
                        userRepository.saveCookie("")
                        val errorMsg = "登录失败: status=$status"
                        Log.e(TAG, errorMsg)
                        Result.failure(RuntimeException(errorMsg))
                    }
                } else {
                    // API调用失败，清除Cookie
                    userRepository.saveCookie("")
                    val message = jsonObject.optString("message", "登录状态检查失败")
                    Log.e(TAG, "登录失败: code=$code, message=$message")
                    Result.failure(RuntimeException(message))
                }
            } catch (e: Exception) {
                // 异常处理，清除Cookie
                userRepository.saveCookie("")
                Log.e(TAG, "登录过程异常", e)
                Result.failure(e)
            }
        }
    }

    /**
     * 退出登录 - 参考ponymusic实现
     */
    override suspend fun logout() {
        withContext(Dispatchers.IO) {
            try {
                Log.d(TAG, "开始退出登录")

                // 清除本地状态 - 参考ponymusic的清理逻辑
                userRepository.clearLoginStatus()
                _profile.value = null

                Log.d(TAG, "退出登录完成")
            } catch (e: Exception) {
                Log.e(TAG, "退出登录异常", e)
            }
        }
    }

    /**
     * 检查登录状态 - 参考ponymusic实现
     */
    override fun checkLogin(
        activity: Activity,
        showDialog: Boolean,
        onCancel: (() -> Unit)?,
        onLogin: (() -> Unit)?
    ) {
        if (isLogin()) {
            onLogin?.invoke()
            return
        }

        // 启动登录Activity - 简化版本，不使用路由框架
        val intent = Intent(activity, LoginActivity::class.java)
        activity.startActivity(intent)
    }
}
