<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="fragment_comment" modulePackage="com.example.aimusicplayer" filePath="app\src\main\res\layout\fragment_comment.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.constraintlayout.widget.ConstraintLayout"><Targets><Target tag="layout/fragment_comment_0" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="1" startOffset="0" endLine="149" endOffset="51"/></Target><Target id="@+id/layout_title_bar" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="9" startOffset="4" endLine="48" endOffset="55"/></Target><Target id="@+id/btn_back" view="ImageButton"><Expressions/><location startLine="17" startOffset="8" endLine="29" endOffset="37"/></Target><Target id="@+id/text_comment_title" view="TextView"><Expressions/><location startLine="31" startOffset="8" endLine="46" endOffset="35"/></Target><Target id="@+id/swipe_refresh_layout" view="androidx.swiperefreshlayout.widget.SwipeRefreshLayout"><Expressions/><location startLine="51" startOffset="4" endLine="66" endOffset="59"/></Target><Target id="@+id/recycler_view_comments" view="androidx.recyclerview.widget.RecyclerView"><Expressions/><location startLine="58" startOffset="8" endLine="65" endOffset="51"/></Target><Target id="@+id/load_more_progress" view="ProgressBar"><Expressions/><location startLine="69" startOffset="4" endLine="78" endOffset="36"/></Target><Target id="@+id/text_empty_comment" view="TextView"><Expressions/><location startLine="81" startOffset="4" endLine="93" endOffset="36"/></Target><Target id="@+id/progress_bar" view="ProgressBar"><Expressions/><location startLine="96" startOffset="4" endLine="105" endOffset="36"/></Target><Target id="@+id/layout_comment_input" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="108" startOffset="4" endLine="147" endOffset="55"/></Target><Target id="@+id/edit_comment" view="EditText"><Expressions/><location startLine="116" startOffset="8" endLine="132" endOffset="55"/></Target><Target id="@+id/btn_send_comment" view="Button"><Expressions/><location startLine="134" startOffset="8" endLine="145" endOffset="55"/></Target></Targets></Layout>