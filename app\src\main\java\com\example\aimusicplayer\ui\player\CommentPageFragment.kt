package com.example.aimusicplayer.ui.player

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Toast
import androidx.fragment.app.Fragment
import androidx.fragment.app.activityViewModels
import androidx.fragment.app.viewModels
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.LinearLayoutManager
import com.example.aimusicplayer.databinding.PagePlayerCommentBinding
import com.example.aimusicplayer.ui.adapter.CommentAdapter
import com.example.aimusicplayer.viewmodel.CommentViewModel
import com.example.aimusicplayer.viewmodel.PlayerViewModel
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.launch

/**
 * 评论页面Fragment
 * 用于在ViewPager2中显示评论
 */
@AndroidEntryPoint
class CommentPageFragment : Fragment() {

    private val TAG = "CommentPageFragment"
    private var _binding: PagePlayerCommentBinding? = null
    private val binding get() = _binding!!

    private val playerViewModel: PlayerViewModel by activityViewModels()
    private val commentViewModel: CommentViewModel by viewModels()
    private lateinit var adapter: CommentAdapter

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = PagePlayerCommentBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        setupViews()
        setupObservers()
    }

    private fun setupViews() {
        // 设置返回按钮
        binding.btnBackToLyric?.setOnClickListener {
            // 返回到歌词页面
            (parentFragment as? PlayerFragment)?.switchToLyricPage()
        }

        // 设置RecyclerView
        setupRecyclerView()

        // 设置下拉刷新
        setupSwipeRefresh()

        // 设置发送评论按钮
        binding.btnSendComment?.setOnClickListener {
            sendComment()
        }
    }

    private fun setupRecyclerView() {
        adapter = CommentAdapter(
            onLikeClick = { comment ->
                commentViewModel.likeComment(comment.commentId)
            }
        )

        binding.recyclerViewComments.apply {
            layoutManager = LinearLayoutManager(requireContext())
            adapter = <EMAIL>
            setHasFixedSize(true)
            // 车载优化：增加缓存大小
            setItemViewCacheSize(20)
        }
    }

    private fun setupSwipeRefresh() {
        binding.swipeRefreshLayout.setColorSchemeResources(
            com.example.aimusicplayer.R.color.colorPrimary,
            com.example.aimusicplayer.R.color.colorAccent,
            com.example.aimusicplayer.R.color.colorPrimaryDark
        )

        binding.swipeRefreshLayout.setOnRefreshListener {
            loadComments()
        }
    }

    private fun setupObservers() {
        // 观察当前歌曲变化，加载对应评论
        viewLifecycleOwner.lifecycleScope.launch {
            playerViewModel.currentSong.collect { song ->
                if (song != null) {
                    loadComments()
                } else {
                    // 清空评论列表
                    adapter.submitList(emptyList())
                }
            }
        }

        // 观察评论列表
        viewLifecycleOwner.lifecycleScope.launch {
            commentViewModel.comments.collect { comments ->
                adapter.submitList(comments)
                binding.swipeRefreshLayout.isRefreshing = false

                // 显示/隐藏空状态
                if (comments.isEmpty()) {
                    binding.textEmptyComment.visibility = View.VISIBLE
                    binding.recyclerViewComments.visibility = View.GONE
                } else {
                    binding.textEmptyComment.visibility = View.GONE
                    binding.recyclerViewComments.visibility = View.VISIBLE
                }
            }
        }

        // 观察加载状态
        viewLifecycleOwner.lifecycleScope.launch {
            commentViewModel.loading.collect { isLoading ->
                if (!binding.swipeRefreshLayout.isRefreshing) {
                    binding.progressBar?.visibility = if (isLoading) View.VISIBLE else View.GONE
                }
            }
        }

        // 观察错误信息
        viewLifecycleOwner.lifecycleScope.launch {
            commentViewModel.errorMessage.collect { message ->
                if (!message.isNullOrEmpty()) {
                    Toast.makeText(requireContext(), message, Toast.LENGTH_SHORT).show()
                    binding.swipeRefreshLayout.isRefreshing = false
                }
            }
        }

        // 观察评论发送成功事件
        viewLifecycleOwner.lifecycleScope.launch {
            commentViewModel.commentSent.collect { sent ->
                if (sent) {
                    // 清空输入框
                    binding.editTextComment?.setText("")
                    // 显示成功提示
                    Toast.makeText(requireContext(), "评论发送成功", Toast.LENGTH_SHORT).show()
                    // 重置状态
                    commentViewModel.resetCommentSentState()
                }
            }
        }
    }

    private fun loadComments() {
        val songId = playerViewModel.currentSong.value?.id
        if (songId != null) {
            commentViewModel.loadComments(songId)
        }
    }

    private fun sendComment() {
        val content = binding.editTextComment?.text?.toString()?.trim()
        if (content.isNullOrEmpty()) {
            Toast.makeText(requireContext(), "请输入评论内容", Toast.LENGTH_SHORT).show()
            return
        }

        val songId = playerViewModel.currentSong.value?.id
        if (songId != null) {
            commentViewModel.sendComment(songId, content)
        } else {
            Toast.makeText(requireContext(), "当前没有播放歌曲", Toast.LENGTH_SHORT).show()
        }
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }
}
