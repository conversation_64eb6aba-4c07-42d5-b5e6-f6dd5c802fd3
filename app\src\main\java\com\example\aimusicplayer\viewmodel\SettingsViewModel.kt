package com.example.aimusicplayer.viewmodel

import android.util.Log
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.example.aimusicplayer.utils.toUnMutable
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * 设置界面ViewModel
 * 负责设置界面的数据管理和业务逻辑
 */
@HiltViewModel
class SettingsViewModel @Inject constructor() : ViewModel() {

    companion object {
        private const val TAG = "SettingsViewModel"
    }

    // 音质设置 - 使用懒加载初始化
    private val _audioQuality by lazy { MutableStateFlow("标准") }
    val audioQuality by lazy { _audioQuality.toUnMutable() }

    // 下载设置 - 使用懒加载初始化
    private val _downloadOnlyWifi by lazy { MutableStateFlow(true) }
    val downloadOnlyWifi by lazy { _downloadOnlyWifi.toUnMutable() }

    // 夜间模式 - 使用懒加载初始化
    private val _nightMode by lazy { MutableStateFlow(false) }
    val nightMode by lazy { _nightMode.toUnMutable() }

    // 自动播放 - 使用懒加载初始化
    private val _autoPlay by lazy { MutableStateFlow(true) }
    val autoPlay by lazy { _autoPlay.toUnMutable() }

    /**
     * 设置音质
     */
    fun setAudioQuality(quality: String) {
        _audioQuality.value = quality
        Log.d(TAG, "音质设置为: $quality")
    }

    /**
     * 设置仅WiFi下载
     */
    fun setDownloadOnlyWifi(enabled: Boolean) {
        _downloadOnlyWifi.value = enabled
        Log.d(TAG, "仅WiFi下载: $enabled")
    }

    /**
     * 设置夜间模式
     */
    fun setNightMode(enabled: Boolean) {
        _nightMode.value = enabled
        Log.d(TAG, "夜间模式: $enabled")
    }

    /**
     * 设置自动播放
     */
    fun setAutoPlay(enabled: Boolean) {
        _autoPlay.value = enabled
        Log.d(TAG, "自动播放: $enabled")
    }

    /**
     * 清除缓存
     */
    fun clearCache() {
        viewModelScope.launch {
            try {
                // TODO: 实现清除缓存逻辑
                Log.d(TAG, "缓存清除完成")
            } catch (e: Exception) {
                Log.e(TAG, "清除缓存失败", e)
            }
        }
    }
}
