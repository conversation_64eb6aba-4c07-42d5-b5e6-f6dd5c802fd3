<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:background="@drawable/bg_bottom_sheet"
    android:padding="20dp"
    android:elevation="8dp">

    <!-- 标题区域 - 参考ponymusic样式 -->
    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="20dp"
        android:paddingVertical="8dp">

        <LinearLayout
            android:id="@+id/title_container"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:orientation="horizontal"
            android:gravity="center_vertical">

            <TextView
                android:id="@+id/text_playlist_title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="播放列表"
                android:textSize="20sp"
                android:textStyle="bold"
                android:textColor="@color/text_primary" />

            <TextView
                android:id="@+id/text_playlist_count"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="8dp"
                android:textColor="@color/text_secondary"
                android:textSize="16sp"
                tools:text="(100首)" />
        </LinearLayout>

        <ImageButton
            android:id="@+id/button_playlist_close"
            android:layout_width="52dp"
            android:layout_height="52dp"
            android:layout_alignParentEnd="true"
            android:layout_centerVertical="true"
            android:background="@drawable/control_button_no_ripple"
            android:contentDescription="关闭"
            android:src="@drawable/ic_close"
            android:padding="12dp"
            app:tint="@color/text_light" />
    </RelativeLayout>

    <!-- 播放列表内容区域 - 参考ponymusic样式 -->
    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/recycler_view_playlist"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:maxHeight="450dp"
        android:scrollbars="vertical"
        android:fadeScrollbars="true"
        android:scrollbarStyle="outsideOverlay"
        android:clipToPadding="false"
        android:paddingVertical="8dp"
        tools:listitem="@layout/item_playlist" />

    <!-- 空播放列表提示 - 增强样式 -->
    <LinearLayout
        android:id="@+id/text_empty_playlist"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:gravity="center"
        android:padding="40dp"
        android:visibility="gone">

        <ImageView
            android:layout_width="64dp"
            android:layout_height="64dp"
            android:src="@drawable/ic_playlist"
            android:alpha="0.5"
            app:tint="@color/text_secondary"
            android:layout_marginBottom="16dp" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="播放列表为空"
            android:textSize="18sp"
            android:textColor="@color/text_secondary"
            android:gravity="center" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="添加一些歌曲开始播放吧"
            android:textSize="14sp"
            android:textColor="@color/text_hint"
            android:layout_marginTop="8dp"
            android:gravity="center" />
    </LinearLayout>

    <!-- 操作按钮区域 - 参考ponymusic样式 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="20dp"
        android:orientation="horizontal"
        android:gravity="center">

        <Button
            android:id="@+id/button_clear_playlist"
            android:layout_width="0dp"
            android:layout_height="48dp"
            android:layout_weight="1"
            android:layout_marginEnd="12dp"
            android:text="清空列表"
            android:textColor="@color/text_light"
            android:textSize="16sp"
            android:textStyle="bold"
            android:background="@drawable/button_secondary_background"
            android:elevation="2dp" />

        <Button
            android:id="@+id/button_shuffle_playlist"
            android:layout_width="0dp"
            android:layout_height="48dp"
            android:layout_weight="1"
            android:layout_marginStart="12dp"
            android:text="随机播放"
            android:textColor="@color/text_light"
            android:textSize="16sp"
            android:textStyle="bold"
            android:background="@drawable/button_primary_background"
            android:elevation="2dp" />

    </LinearLayout>

</LinearLayout>
