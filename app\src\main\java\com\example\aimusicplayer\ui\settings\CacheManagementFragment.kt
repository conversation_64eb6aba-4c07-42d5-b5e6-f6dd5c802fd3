package com.example.aimusicplayer.ui.settings

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.Fragment
import androidx.fragment.app.viewModels
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.LinearLayoutManager
import com.example.aimusicplayer.databinding.FragmentCacheManagementBinding
import com.example.aimusicplayer.viewmodel.CacheManagementViewModel
import com.google.android.material.dialog.MaterialAlertDialogBuilder
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.launch

/**
 * 缓存管理界面Fragment
 * 显示缓存统计、缓存列表和缓存设置
 */
@AndroidEntryPoint
class CacheManagementFragment : Fragment() {

    private var _binding: FragmentCacheManagementBinding? = null
    private val binding get() = _binding!!

    private val viewModel: CacheManagementViewModel by viewModels()
    private lateinit var cacheListAdapter: CacheListAdapter

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?,
    ): View {
        _binding = FragmentCacheManagementBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        setupRecyclerView()
        setupClickListeners()
        observeViewModel()
    }

    private fun setupRecyclerView() {
        cacheListAdapter = CacheListAdapter(
            onItemClick = { _ ->
                // TODO: 播放缓存的歌曲
            },
            onDeleteClick = { cacheInfo ->
                showDeleteConfirmDialog(listOf(cacheInfo.songId))
            },
        )

        binding.recyclerViewCachedSongs.apply {
            layoutManager = LinearLayoutManager(requireContext())
            adapter = cacheListAdapter
        }
    }

    private fun setupClickListeners() {
        // 清空所有缓存
        binding.buttonClearAll.setOnClickListener {
            showClearAllConfirmDialog()
        }

        // 缓存设置
        binding.buttonCacheSettings.setOnClickListener {
            showCacheSettingsDialog()
        }

        // 刷新
        binding.swipeRefreshLayout.setOnRefreshListener {
            viewModel.loadCachedSongs()
        }

        // 批量删除
        binding.buttonBatchDelete.setOnClickListener {
            val selectedItems = cacheListAdapter.getSelectedItems()
            if (selectedItems.isNotEmpty()) {
                showDeleteConfirmDialog(selectedItems.map { it.songId })
            }
        }
    }

    private fun observeViewModel() {
        // 缓存统计
        lifecycleScope.launch {
            viewModel.cacheStats.collect { stats ->
                updateCacheStats(stats)
            }
        }

        // 缓存歌曲列表
        lifecycleScope.launch {
            viewModel.cachedSongs.collect { songs ->
                cacheListAdapter.submitList(songs)
                binding.textEmptyState.visibility = if (songs.isEmpty()) View.VISIBLE else View.GONE
                binding.recyclerViewCachedSongs.visibility = if (songs.isEmpty()) View.GONE else View.VISIBLE
            }
        }

        // 加载状态
        lifecycleScope.launch {
            viewModel.isLoading.collect { isLoading ->
                binding.swipeRefreshLayout.isRefreshing = isLoading
                binding.progressBar.visibility = if (isLoading) View.VISIBLE else View.GONE
            }
        }

        // 操作消息
        lifecycleScope.launch {
            viewModel.operationMessage.collect { message ->
                if (message != null) {
                    showSnackbar(message)
                }
            }
        }

        // 缓存设置
        lifecycleScope.launch {
            viewModel.cacheSettings.collect { settings ->
                updateCacheSettingsUI(settings)
            }
        }
    }

    private fun updateCacheStats(stats: com.example.aimusicplayer.data.cache.MusicFileCache.CacheStats) {
        binding.apply {
            textCacheSize.text = "${viewModel.formatFileSize(stats.totalSize)} / ${viewModel.formatFileSize(stats.maxSize)}"
            textCacheCount.text = "${stats.totalFiles}首歌曲"

            progressBarCacheUsage.progress = stats.usagePercentage.toInt()
            textCacheUsage.text = "${stats.usagePercentage.toInt()}%"

            // 根据使用率设置进度条颜色
            val color = viewModel.getCacheUsageColor(stats.usagePercentage)
            progressBarCacheUsage.progressTintList = android.content.res.ColorStateList.valueOf(color)
        }
    }

    private fun updateCacheSettingsUI(settings: CacheManagementViewModel.CacheSettings) {
        binding.apply {
            textMaxCacheSize.text = viewModel.formatFileSize(settings.maxCacheSize)
            switchWifiOnly.isChecked = settings.wifiOnlyCache
            switchAutoCache.isChecked = settings.autoCacheEnabled
        }
    }

    private fun showClearAllConfirmDialog() {
        MaterialAlertDialogBuilder(requireContext())
            .setTitle("清空所有缓存")
            .setMessage("确定要清空所有缓存文件吗？此操作不可撤销。")
            .setPositiveButton("确定") { _, _ ->
                viewModel.clearAllCache()
            }
            .setNegativeButton("取消", null)
            .show()
    }

    private fun showDeleteConfirmDialog(songIds: List<Long>) {
        val message = if (songIds.size == 1) {
            "确定要删除这首歌曲的缓存吗？"
        } else {
            "确定要删除选中的${songIds.size}首歌曲的缓存吗？"
        }

        MaterialAlertDialogBuilder(requireContext())
            .setTitle("删除缓存")
            .setMessage(message)
            .setPositiveButton("确定") { _, _ ->
                if (songIds.size == 1) {
                    viewModel.deleteCachedSong(songIds.first())
                } else {
                    viewModel.deleteCachedSongs(songIds)
                }
            }
            .setNegativeButton("取消", null)
            .show()
    }

    private fun showCacheSettingsDialog() {
        val currentSettings = viewModel.cacheSettings.value
        val sizeOptions = viewModel.getCacheSizeOptions()
        val sizeLabels = sizeOptions.map { it.first }.toTypedArray()
        val currentSizeIndex = sizeOptions.indexOfFirst { it.second == currentSettings.maxCacheSize }

        var selectedSizeIndex = if (currentSizeIndex >= 0) currentSizeIndex else 2 // 默认500MB

        MaterialAlertDialogBuilder(requireContext())
            .setTitle("缓存设置")
            .setView(
                createCacheSettingsView(currentSettings, sizeLabels, selectedSizeIndex) { newIndex ->
                    selectedSizeIndex = newIndex
                },
            )
            .setPositiveButton("保存") { _, _ ->
                val selectedSize = sizeOptions[selectedSizeIndex].second
                val wifiOnly = binding.switchWifiOnly.isChecked
                val autoCache = binding.switchAutoCache.isChecked

                viewModel.updateCacheSettings(
                    maxCacheSize = selectedSize,
                    wifiOnlyCache = wifiOnly,
                    autoCacheEnabled = autoCache,
                )
            }
            .setNegativeButton("取消", null)
            .show()
    }

    private fun createCacheSettingsView(
        @Suppress("UNUSED_PARAMETER") currentSettings: CacheManagementViewModel.CacheSettings,
        @Suppress("UNUSED_PARAMETER") sizeLabels: Array<String>,
        @Suppress("UNUSED_PARAMETER") selectedIndex: Int,
        @Suppress("UNUSED_PARAMETER") onSizeSelected: (Int) -> Unit,
    ): View {
        // TODO: 创建缓存设置对话框的布局
        // 这里应该创建一个包含Spinner和Switch的布局
        return View(requireContext())
    }

    private fun showSnackbar(message: String) {
        com.google.android.material.snackbar.Snackbar.make(
            binding.root,
            message,
            com.google.android.material.snackbar.Snackbar.LENGTH_SHORT,
        ).show()
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }
}
