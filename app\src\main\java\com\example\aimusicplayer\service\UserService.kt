package com.example.aimusicplayer.service

import android.app.Activity
import kotlinx.coroutines.flow.StateFlow
import com.example.aimusicplayer.data.model.User

/**
 * 用户服务接口 - 严格对齐ponymusic标准
 * 参考：ponymusic-master/app/src/main/java/me/wcy/music/account/service/UserService.kt
 */
interface UserService {
    
    /**
     * 用户信息StateFlow
     */
    val profile: StateFlow<User?>

    /**
     * 获取Cookie
     */
    fun getCookie(): String

    /**
     * 是否已登录
     */
    fun isLogin(): Boolean

    /**
     * 获取用户ID
     */
    fun getUserId(): Long

    /**
     * 登录 - 核心方法，参考ponymusic实现
     * @param cookie 登录成功后的Cookie
     * @return 登录结果，包含用户信息
     */
    suspend fun login(cookie: String): Result<User>

    /**
     * 退出登录
     */
    suspend fun logout()

    /**
     * 检查登录状态 - 参考ponymusic实现
     * @param activity 当前Activity
     * @param showDialog 是否显示登录对话框
     * @param onCancel 取消回调
     * @param onLogin 登录成功回调
     */
    fun checkLogin(
        activity: Activity,
        showDialog: Boolean = true,
        onCancel: (() -> Unit)? = null,
        onLogin: (() -> Unit)? = null
    )
}
