package com.example.aimusicplayer.viewmodel

import android.graphics.Bitmap
import android.util.Log
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.example.aimusicplayer.data.model.LoginStatus
import com.example.aimusicplayer.data.repository.UserRepository
import com.example.aimusicplayer.service.UserService
import com.example.aimusicplayer.ui.login.QrCodeProcessor

import com.example.aimusicplayer.utils.toUnMutable
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.launch
import kotlinx.coroutines.delay
import org.json.JSONObject
import javax.inject.Inject

/**
 * 登录ViewModel
 * 严格按照ponymusic项目标准重构
 */
@HiltViewModel
class LoginViewModel @Inject constructor(
    private val userRepository: UserRepository,
    private val userService: UserService,
) : ViewModel() {

    companion object {
        private const val TAG = "LoginViewModel"
    }

    // 严格按照ponymusic项目标准的StateFlow使用
    private val _loginStatusFlow = MutableStateFlow<LoginStatus?>(null)
    val loginStatusFlow = _loginStatusFlow.toUnMutable()

    private val _qrCodeDataFlow = MutableStateFlow<String?>(null)
    val qrCodeDataFlow = _qrCodeDataFlow.toUnMutable()

    // 登录状态枚举
    enum class LoginState {
        IDLE, SUCCESS, FAILED
    }

    // 验证码状态枚举
    enum class CaptchaState {
        IDLE, SENT, ERROR
    }

    // 登录状态
    private val _loginState = MutableStateFlow(LoginState.IDLE)
    val loginState = _loginState.toUnMutable()

    // 加载状态
    private val _loading = MutableStateFlow(false)
    val loading = _loading.toUnMutable()

    // 错误信息
    private val _errorMessage = MutableStateFlow<String?>(null)
    val errorMessage = _errorMessage.toUnMutable()

    // 二维码相关StateFlow
    private val _qrCodeBitmap = MutableStateFlow<android.graphics.Bitmap?>(null)
    val qrCodeBitmap = _qrCodeBitmap.toUnMutable()

    private val _qrStatus = MutableStateFlow<QrStatus?>(null)
    val qrStatus = _qrStatus.toUnMutable()



    // 二维码处理器（临时兼容）
    val qrCodeProcessor = object {
        val qrCodeBitmap = _qrCodeBitmap.toUnMutable()
        val qrStatus = _qrStatus.toUnMutable()

        // 更新二维码Bitmap的方法
        fun updateQrCodeBitmap(bitmap: android.graphics.Bitmap?) {
            _qrCodeBitmap.value = bitmap
        }

        // 更新二维码状态的方法
        fun updateQrStatus(status: QrStatus?) {
            _qrStatus.value = status
        }
    }

    // QrCodeProcessor状态枚举（临时兼容）
    enum class QrStatus {
        LOADING, WAITING, SCANNED, CONFIRMED, EXPIRED, ERROR
    }

    /**
     * 游客登录 - 简化为本地登录，不调用网络接口
     */
    fun loginAsGuest() {
        viewModelScope.launch {
            _loading.value = true
            _loginState.value = LoginState.IDLE
            _errorMessage.value = null

            try {
                Log.d(TAG, "开始游客登录（本地模式）")

                // 直接设置本地登录状态
                userRepository.saveLoginStatus(true, "guest_token", "guest_user", "游客")
                val loginStatus = LoginStatus(true, "guest_user", "游客", "")
                _loginStatusFlow.value = loginStatus

                _loginState.value = LoginState.SUCCESS
                _errorMessage.value = null
                Log.d(TAG, "游客登录成功（本地模式），部分功能需要正式登录")

            } catch (e: Exception) {
                _loginState.value = LoginState.FAILED
                _errorMessage.value = "游客登录失败: ${e.message}"
                Log.e(TAG, "游客登录异常", e)
            } finally {
                _loading.value = false
            }
        }
    }

    /**
     * 生成二维码 - 完整实现三步流程
     */
    fun generateQrCode() {
        viewModelScope.launch {
            _qrStatus.value = QrStatus.LOADING
            _errorMessage.value = null

            try {
                Log.d(TAG, "开始二维码登录流程...")

                // 步骤1: 获取二维码key
                val timestamp = System.currentTimeMillis()
                val keyResponse = userRepository.getQrKey(timestamp)
                val keyJson = JSONObject(keyResponse)
                val keyCode = keyJson.optInt("code")

                if (keyCode != 200) {
                    val message = keyJson.optString("message", "获取二维码key失败")
                    throw Exception("获取二维码key失败: $message (错误码: $keyCode)")
                }

                val data = keyJson.optJSONObject("data")
                val key = data?.optString("unikey", "") ?: ""

                if (key.isEmpty()) {
                    throw Exception("获取到的二维码key为空")
                }

                Log.d(TAG, "获取二维码key成功: $key")

                // 步骤2: 生成二维码
                val qrResponse = userRepository.createQrCode(key, true) // qrimg=true获取base64图片
                val qrJson = JSONObject(qrResponse)
                val qrCode = qrJson.optInt("code")

                if (qrCode != 200) {
                    val message = qrJson.optString("message", "生成二维码失败")
                    throw Exception("生成二维码失败: $message (错误码: $qrCode)")
                }

                val qrData = qrJson.optJSONObject("data")
                val qrUrl = qrData?.optString("qrurl", "") ?: ""

                if (qrUrl.isEmpty()) {
                    throw Exception("获取到的二维码URL为空")
                }

                Log.d(TAG, "生成二维码成功，URL长度: ${qrUrl.length}")

                // 生成二维码Bitmap
                val qrBitmap = generateQrCodeBitmap(qrUrl)
                if (qrBitmap != null) {
                    _qrCodeBitmap.value = qrBitmap
                    _qrCodeDataFlow.value = qrUrl
                    _qrStatus.value = QrStatus.WAITING

                    // 步骤3: 开始轮询扫码状态
                    startQrStatusPolling(key)
                    Log.d(TAG, "二维码生成完成，开始轮询状态")
                } else {
                    throw Exception("二维码Bitmap生成失败")
                }

            } catch (e: Exception) {
                _qrStatus.value = QrStatus.ERROR
                _errorMessage.value = e.message
                Log.e(TAG, "二维码生成失败", e)
            }
        }
    }

    /**
     * 手机号密码登录
     */
    fun loginWithPhone(phone: String, password: String) {
        viewModelScope.launch {
            _loading.value = true
            _loginState.value = LoginState.IDLE
            _errorMessage.value = null

            try {
                val response = userRepository.loginWithPhone(phone, password)
                val jsonObject = JSONObject(response)
                val code = jsonObject.optInt("code")

                if (code == 200) {
                    _loginState.value = LoginState.SUCCESS
                    Log.d(TAG, "手机号登录成功")
                } else {
                    val msg = jsonObject.optString("msg", "手机号登录失败")
                    _loginState.value = LoginState.FAILED
                    _errorMessage.value = msg
                    Log.e(TAG, msg)
                }
            } catch (e: Exception) {
                _loginState.value = LoginState.FAILED
                _errorMessage.value = "手机号登录失败: ${e.message}"
                Log.e(TAG, "手机号登录失败", e)
            } finally {
                _loading.value = false
            }
        }
    }

    /**
     * 验证码登录 - 对齐ponymusic标准流程
     */
    fun loginWithCaptcha(phone: String, captcha: String) {
        viewModelScope.launch {
            _loading.value = true
            _loginState.value = LoginState.IDLE
            _errorMessage.value = null

            try {
                // 1. 调用登录API - 参考ponymusic的phoneLogin
                val response = userRepository.loginWithCaptcha(phone, captcha)
                val jsonObject = JSONObject(response)
                val code = jsonObject.optInt("code")

                if (code == 200) {
                    // 2. 登录成功，获取Cookie - 参考ponymusic流程
                    val cookie = jsonObject.optString("cookie", "")
                    if (cookie.isNotEmpty()) {
                        // 3. 使用UserService.login获取用户信息 - 参考ponymusic
                        val loginResult = userService.login(cookie)
                        if (loginResult.isSuccess) {
                            _loginState.value = LoginState.SUCCESS
                            Log.d(TAG, "验证码登录成功")
                        } else {
                            _loginState.value = LoginState.FAILED
                            _errorMessage.value = loginResult.exceptionOrNull()?.message ?: "获取用户信息失败"
                            Log.e(TAG, "获取用户信息失败", loginResult.exceptionOrNull())
                        }
                    } else {
                        _loginState.value = LoginState.FAILED
                        _errorMessage.value = "登录响应中未包含Cookie"
                        Log.e(TAG, "登录响应中未包含Cookie")
                    }
                } else {
                    val msg = jsonObject.optString("message", jsonObject.optString("msg", "验证码登录失败"))
                    _loginState.value = LoginState.FAILED
                    _errorMessage.value = msg
                    Log.e(TAG, "验证码登录失败: code=$code, msg=$msg")
                }
            } catch (e: Exception) {
                _loginState.value = LoginState.FAILED
                _errorMessage.value = "验证码登录失败: ${e.message}"
                Log.e(TAG, "验证码登录失败", e)
            } finally {
                _loading.value = false
            }
        }
    }

    /**
     * 发送验证码
     */
    fun sendCaptcha(phone: String, captchaState: androidx.lifecycle.MutableLiveData<CaptchaState>) {
        viewModelScope.launch {
            try {
                val response = userRepository.sendCaptcha(phone)
                val jsonObject = JSONObject(response)
                val code = jsonObject.optInt("code")

                if (code == 200) {
                    captchaState.value = CaptchaState.SENT
                    Log.d(TAG, "验证码发送成功")
                } else {
                    captchaState.value = CaptchaState.ERROR
                    Log.e(TAG, "验证码发送失败")
                }
            } catch (e: Exception) {
                captchaState.value = CaptchaState.ERROR
                Log.e(TAG, "验证码发送失败", e)
            }
        }
    }

    /**
     * 开始二维码状态轮询
     */
    private fun startQrStatusPolling(key: String) {
        viewModelScope.launch {
            var retryCount = 0
            val maxRetries = 2 // 统一重试2次

            while (retryCount <= maxRetries) {
                try {
                    val timestamp = System.currentTimeMillis()
                    val response = userRepository.checkQrStatus(key, timestamp)
                    val jsonObject = JSONObject(response)
                    val code = jsonObject.optInt("code")

                    Log.d(TAG, "检查二维码状态，code: $code")

                    when (code) {
                        800 -> {
                            // 二维码过期
                            _qrStatus.value = QrStatus.EXPIRED
                            Log.d(TAG, "二维码已过期")
                            return@launch
                        }
                        801 -> {
                            // 等待扫码
                            _qrStatus.value = QrStatus.WAITING
                            delay(3000) // 3秒后再次检查
                            retryCount = 0 // 重置重试计数
                        }
                        802 -> {
                            // 待确认
                            _qrStatus.value = QrStatus.SCANNED
                            delay(1000) // 1秒后再次检查
                            retryCount = 0 // 重置重试计数
                        }
                        803 -> {
                            // 登录成功
                            _qrStatus.value = QrStatus.CONFIRMED
                            Log.d(TAG, "二维码登录成功")

                            // 获取cookie并保存
                            val cookie = jsonObject.optString("cookie", "")
                            if (cookie.isNotEmpty()) {
                                userRepository.saveCookie(cookie)

                                // 获取用户信息
                                val nickname = jsonObject.optString("nickname", "")
                                val avatarUrl = jsonObject.optString("avatarUrl", "")

                                userRepository.saveLoginStatus(true, cookie, "qr_user", nickname)
                                val loginStatus = LoginStatus(true, "qr_user", nickname, avatarUrl)
                                _loginStatusFlow.value = loginStatus
                                _loginState.value = LoginState.SUCCESS

                                Log.d(TAG, "二维码登录成功，用户: $nickname")
                            } else {
                                Log.w(TAG, "二维码登录成功但未获取到cookie")
                            }
                            return@launch
                        }
                        502 -> {
                            // 502错误，添加noCookie参数重试
                            Log.w(TAG, "检查二维码状态返回502，添加noCookie参数重试")
                            val noCookieResponse = userRepository.checkQrStatusWithNoCookie(key, timestamp)
                            // 递归处理响应
                            continue
                        }
                        else -> {
                            // 其他错误
                            retryCount++
                            if (retryCount > maxRetries) {
                                val message = jsonObject.optString("message", "检查二维码状态失败")
                                _qrStatus.value = QrStatus.ERROR
                                _errorMessage.value = message
                                Log.e(TAG, "检查二维码状态失败: $message")
                                return@launch
                            } else {
                                Log.w(TAG, "检查二维码状态失败，1秒后重试 ($retryCount/$maxRetries)")
                                delay(1000)
                            }
                        }
                    }
                } catch (e: Exception) {
                    retryCount++
                    if (retryCount > maxRetries) {
                        _qrStatus.value = QrStatus.ERROR
                        _errorMessage.value = "检查二维码状态异常: ${e.message}"
                        Log.e(TAG, "检查二维码状态异常", e)
                        return@launch
                    } else {
                        Log.w(TAG, "检查二维码状态异常，1秒后重试 ($retryCount/$maxRetries)", e)
                        delay(1000)
                    }
                }
            }
        }
    }

    /**
     * 生成二维码Bitmap - 优化尺寸确保可扫描
     */
    private fun generateQrCodeBitmap(qrData: String): Bitmap? {
        return try {
            Log.d(TAG, "LoginViewModel生成二维码，数据长度: ${qrData.length}")
            // 使用优化后的参数生成二维码，确保可扫描
            QrCodeProcessor.generateQrCodeBitmap(qrData, 512, 512)
        } catch (e: Exception) {
            Log.e(TAG, "LoginViewModel生成二维码Bitmap失败", e)
            null
        }
    }

    /**
     * 获取二维码Key（兼容方法）
     */
    fun getQrKey() {
        _qrStatus.value = QrStatus.LOADING
        generateQrCode()
    }

    /**
     * 手动检查登录状态
     */
    fun checkLoginStatus() {
        viewModelScope.launch {
            try {
                val response = userRepository.checkLoginStatus()
                val jsonObject = JSONObject(response)
                val code = jsonObject.optInt("code")

                if (code == 200) {
                    Log.d(TAG, "登录状态检查成功")
                } else {
                    Log.w(TAG, "登录状态检查失败: $code")
                }
            } catch (e: Exception) {
                Log.e(TAG, "检查登录状态异常", e)
            }
        }
    }
}
