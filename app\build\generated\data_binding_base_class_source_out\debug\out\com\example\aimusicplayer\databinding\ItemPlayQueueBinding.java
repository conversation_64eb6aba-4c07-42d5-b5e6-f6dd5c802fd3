// Generated by view binder compiler. Do not edit!
package com.example.aimusicplayer.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.aimusicplayer.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ItemPlayQueueBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final ImageView ivMore;

  @NonNull
  public final ImageView ivPlayingIndicator;

  @NonNull
  public final TextView tvSongArtist;

  @NonNull
  public final TextView tvSongDuration;

  @NonNull
  public final TextView tvSongIndex;

  @NonNull
  public final TextView tvSongTitle;

  private ItemPlayQueueBinding(@NonNull LinearLayout rootView, @NonNull ImageView ivMore,
      @NonNull ImageView ivPlayingIndicator, @NonNull TextView tvSongArtist,
      @NonNull TextView tvSongDuration, @NonNull TextView tvSongIndex,
      @NonNull TextView tvSongTitle) {
    this.rootView = rootView;
    this.ivMore = ivMore;
    this.ivPlayingIndicator = ivPlayingIndicator;
    this.tvSongArtist = tvSongArtist;
    this.tvSongDuration = tvSongDuration;
    this.tvSongIndex = tvSongIndex;
    this.tvSongTitle = tvSongTitle;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ItemPlayQueueBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ItemPlayQueueBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.item_play_queue, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ItemPlayQueueBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.iv_more;
      ImageView ivMore = ViewBindings.findChildViewById(rootView, id);
      if (ivMore == null) {
        break missingId;
      }

      id = R.id.iv_playing_indicator;
      ImageView ivPlayingIndicator = ViewBindings.findChildViewById(rootView, id);
      if (ivPlayingIndicator == null) {
        break missingId;
      }

      id = R.id.tv_song_artist;
      TextView tvSongArtist = ViewBindings.findChildViewById(rootView, id);
      if (tvSongArtist == null) {
        break missingId;
      }

      id = R.id.tv_song_duration;
      TextView tvSongDuration = ViewBindings.findChildViewById(rootView, id);
      if (tvSongDuration == null) {
        break missingId;
      }

      id = R.id.tv_song_index;
      TextView tvSongIndex = ViewBindings.findChildViewById(rootView, id);
      if (tvSongIndex == null) {
        break missingId;
      }

      id = R.id.tv_song_title;
      TextView tvSongTitle = ViewBindings.findChildViewById(rootView, id);
      if (tvSongTitle == null) {
        break missingId;
      }

      return new ItemPlayQueueBinding((LinearLayout) rootView, ivMore, ivPlayingIndicator,
          tvSongArtist, tvSongDuration, tvSongIndex, tvSongTitle);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
