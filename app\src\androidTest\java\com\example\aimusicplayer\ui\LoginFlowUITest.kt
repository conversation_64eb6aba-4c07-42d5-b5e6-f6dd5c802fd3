package com.example.aimusicplayer.ui

import androidx.test.espresso.Espresso.onView
import androidx.test.espresso.action.ViewActions.clearText
import androidx.test.espresso.action.ViewActions.click
import androidx.test.espresso.action.ViewActions.closeSoftKeyboard
import androidx.test.espresso.action.ViewActions.requestFocus
import androidx.test.espresso.action.ViewActions.typeText
import androidx.test.espresso.assertion.ViewAssertions.matches
import androidx.test.espresso.matcher.ViewMatchers.Visibility
import androidx.test.espresso.matcher.ViewMatchers.hasContentDescription
import androidx.test.espresso.matcher.ViewMatchers.hasFocus
import androidx.test.espresso.matcher.ViewMatchers.hasMinimumChildCount
import androidx.test.espresso.matcher.ViewMatchers.isClickable
import androidx.test.espresso.matcher.ViewMatchers.isDisplayed
import androidx.test.espresso.matcher.ViewMatchers.withEffectiveVisibility
import androidx.test.espresso.matcher.ViewMatchers.withId
import androidx.test.espresso.matcher.ViewMatchers.withText
import androidx.test.ext.junit.rules.ActivityScenarioRule
import androidx.test.ext.junit.runners.AndroidJUnit4
import androidx.test.filters.LargeTest
import com.example.aimusicplayer.MainActivity
import com.example.aimusicplayer.R
import dagger.hilt.android.testing.HiltAndroidRule
import dagger.hilt.android.testing.HiltAndroidTest
import org.hamcrest.Matchers.allOf
import org.junit.Before
import org.junit.Rule
import org.junit.Test
import org.junit.runner.RunWith

/**
 * 登录流程UI自动化测试
 *
 * 测试覆盖：
 * - 二维码登录流程
 * - 验证码登录流程
 * - 手机号密码登录流程
 * - 登录状态验证
 * - Android Automotive适配验证
 *
 * 严格按照ponymusic-master项目测试标准实现
 */
@LargeTest
@RunWith(AndroidJUnit4::class)
@HiltAndroidTest
class LoginFlowUITest {

    @get:Rule
    var hiltRule = HiltAndroidRule(this)

    @get:Rule
    var activityScenarioRule = ActivityScenarioRule(MainActivity::class.java)

    @Before
    fun setup() {
        hiltRule.inject()
    }

    @Test
    fun testQrCodeLoginFlow() {
        // 导航到登录页面
        navigateToLoginPage()

        // 点击二维码登录选项卡
        onView(withText("二维码登录"))
            .perform(click())

        // 验证二维码显示
        onView(withId(R.id.qr_code_image))
            .check(matches(isDisplayed()))

        // 验证刷新按钮存在且可点击
        onView(withId(R.id.refresh_qr_button))
            .check(matches(allOf(isDisplayed(), isClickable())))

        // 点击刷新二维码
        onView(withId(R.id.refresh_qr_button))
            .perform(click())

        // 验证加载状态
        onView(withId(R.id.loading_indicator))
            .check(matches(isDisplayed()))

        // 等待加载完成
        Thread.sleep(2000)

        // 验证新的二维码显示
        onView(withId(R.id.qr_code_image))
            .check(matches(isDisplayed()))
    }

    @Test
    fun testCaptchaLoginFlow() {
        // 导航到登录页面
        navigateToLoginPage()

        // 点击验证码登录选项卡
        onView(withText("验证码登录"))
            .perform(click())

        // 输入手机号
        onView(withId(R.id.phone_edit_text))
            .perform(typeText("13800138000"), closeSoftKeyboard())

        // 点击发送验证码
        onView(withId(R.id.send_captcha_button))
            .perform(click())

        // 验证验证码输入框显示
        onView(withId(R.id.captcha_edit_text))
            .check(matches(isDisplayed()))

        // 输入验证码
        onView(withId(R.id.captcha_edit_text))
            .perform(typeText("123456"), closeSoftKeyboard())

        // 点击登录按钮
        onView(withId(R.id.login_button))
            .perform(click())

        // 验证登录处理中状态
        onView(withId(R.id.loading_indicator))
            .check(matches(isDisplayed()))
    }

    @Test
    fun testPhonePasswordLoginFlow() {
        // 导航到登录页面
        navigateToLoginPage()

        // 点击密码登录选项卡
        onView(withText("密码登录"))
            .perform(click())

        // 输入手机号
        onView(withId(R.id.phone_edit_text))
            .perform(typeText("13800138000"), closeSoftKeyboard())

        // 输入密码
        onView(withId(R.id.password_edit_text))
            .perform(typeText("password123"), closeSoftKeyboard())

        // 点击登录按钮
        onView(withId(R.id.login_button))
            .perform(click())

        // 验证登录处理中状态
        onView(withId(R.id.loading_indicator))
            .check(matches(isDisplayed()))
    }

    @Test
    fun testInputValidation() {
        // 导航到登录页面
        navigateToLoginPage()

        // 测试无效手机号
        onView(withText("验证码登录"))
            .perform(click())

        // 输入无效手机号
        onView(withId(R.id.phone_edit_text))
            .perform(typeText("123"), closeSoftKeyboard())

        // 点击发送验证码
        onView(withId(R.id.send_captcha_button))
            .perform(click())

        // 验证错误提示显示
        onView(withText("请输入正确的手机号"))
            .check(matches(isDisplayed()))

        // 清除输入并输入有效手机号
        onView(withId(R.id.phone_edit_text))
            .perform(clearText(), typeText("13800138000"), closeSoftKeyboard())

        // 验证错误提示消失
        onView(withText("请输入正确的手机号"))
            .check(matches(withEffectiveVisibility(Visibility.GONE)))
    }

    @Test
    fun testAndroidAutomotiveAdaptation() {
        // 验证横屏布局
        onView(withId(R.id.login_container))
            .check(matches(isDisplayed()))

        // 验证触摸目标大小（≥48dp）
        onView(withId(R.id.login_button))
            .check(matches(hasMinimumChildCount(0))) // 按钮应该足够大

        // 验证文字大小适合车载环境
        onView(withId(R.id.login_title))
            .check(matches(isDisplayed()))

        // 验证颜色对比度适合车载环境
        onView(withId(R.id.login_container))
            .check(matches(hasBackground()))
    }

    @Test
    fun testLoginStateTransition() {
        // 模拟成功登录
        performSuccessfulLogin()

        // 验证导航到主页面
        onView(withId(R.id.main_container))
            .check(matches(isDisplayed()))

        // 验证登录状态保持
        onView(withId(R.id.user_avatar))
            .check(matches(isDisplayed()))

        // 验证用户信息显示
        onView(withId(R.id.username_text))
            .check(matches(isDisplayed()))
    }

    @Test
    fun testErrorHandling() {
        // 导航到登录页面
        navigateToLoginPage()

        // 模拟网络错误
        onView(withText("验证码登录"))
            .perform(click())

        onView(withId(R.id.phone_edit_text))
            .perform(typeText("13800138000"), closeSoftKeyboard())

        // 断开网络连接（需要在测试环境中模拟）
        // 这里假设我们有方法来模拟网络错误

        onView(withId(R.id.send_captcha_button))
            .perform(click())

        // 验证错误提示显示
        onView(withText("网络连接失败，请检查网络设置"))
            .check(matches(isDisplayed()))

        // 验证重试按钮显示
        onView(withId(R.id.retry_button))
            .check(matches(allOf(isDisplayed(), isClickable())))
    }

    @Test
    fun testAccessibility() {
        // 验证所有重要元素都有内容描述
        onView(withId(R.id.qr_code_image))
            .check(matches(hasContentDescription()))

        onView(withId(R.id.refresh_qr_button))
            .check(matches(hasContentDescription()))

        onView(withId(R.id.login_button))
            .check(matches(hasContentDescription()))

        // 验证焦点导航
        onView(withId(R.id.phone_edit_text))
            .perform(requestFocus())
            .check(matches(hasFocus()))
    }

    @Test
    fun testPerformanceRequirements() {
        val startTime = System.currentTimeMillis()

        // 导航到登录页面
        navigateToLoginPage()

        // 执行登录操作
        onView(withText("二维码登录"))
            .perform(click())

        val endTime = System.currentTimeMillis()
        val duration = endTime - startTime

        // 验证UI响应时间<200ms
        assert(duration < 200) { "UI响应时间超过200ms: ${duration}ms" }

        // 验证动画流畅度（这里需要更复杂的测试工具）
        // 假设我们有方法来检测帧率
        // assertFrameRate(">30fps")
    }

    private fun navigateToLoginPage() {
        // 如果当前不在登录页面，导航到登录页面
        try {
            onView(withId(R.id.login_container))
                .check(matches(isDisplayed()))
        } catch (e: Exception) {
            // 点击登录按钮或菜单项导航到登录页面
            onView(withId(R.id.login_menu_item))
                .perform(click())
        }
    }

    private fun performSuccessfulLogin() {
        navigateToLoginPage()

        // 使用测试账号登录
        onView(withText("密码登录"))
            .perform(click())

        onView(withId(R.id.phone_edit_text))
            .perform(typeText("13800138000"), closeSoftKeyboard())

        onView(withId(R.id.password_edit_text))
            .perform(typeText("test_password"), closeSoftKeyboard())

        onView(withId(R.id.login_button))
            .perform(click())

        // 等待登录完成
        Thread.sleep(3000)
    }

    private fun hasBackground() = object : org.hamcrest.TypeSafeMatcher<android.view.View>() {
        override fun describeTo(description: org.hamcrest.Description?) {
            description?.appendText("has background")
        }

        override fun matchesSafely(item: android.view.View?): Boolean {
            return item?.background != null
        }
    }
}
