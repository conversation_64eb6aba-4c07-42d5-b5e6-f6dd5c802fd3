package com.example.aimusicplayer.network

import okhttp3.Interceptor
import okhttp3.Response

/**
 * User-Agent拦截器
 * 为所有请求添加统一的User-Agent头
 */
class UserAgentInterceptor : Interceptor {

    companion object {
        private const val USER_AGENT = "Mozilla/5.0 (Linux; Android 11; Android SDK built for x86) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.120 Mobile Safari/537.36"
    }

    override fun intercept(chain: Interceptor.Chain): Response {
        val originalRequest = chain.request()

        val requestWithUserAgent = originalRequest.newBuilder()
            .header("User-Agent", USER_AGENT)
            .header("Referer", "https://music.163.com/")
            .header("Origin", "https://music.163.com")
            .build()

        return chain.proceed(requestWithUserAgent)
    }
}
