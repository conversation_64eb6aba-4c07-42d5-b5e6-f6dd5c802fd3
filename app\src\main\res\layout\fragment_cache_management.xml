<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@android:color/background_light">

    <!-- 顶部应用栏 -->
    <com.google.android.material.appbar.AppBarLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:theme="@style/ThemeOverlay.AppCompat.Dark.ActionBar">

        <com.google.android.material.appbar.MaterialToolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="?attr/actionBarSize"
            android:background="?attr/colorPrimary"
            app:title="缓存管理"
            app:titleTextColor="@android:color/white" />

    </com.google.android.material.appbar.AppBarLayout>

    <!-- 主要内容 -->
    <androidx.swiperefreshlayout.widget.SwipeRefreshLayout
        android:id="@+id/swipeRefreshLayout"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:layout_behavior="@string/appbar_scrolling_view_behavior">

        <androidx.core.widget.NestedScrollView
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <!-- 缓存统计卡片 -->
                <com.google.android.material.card.MaterialCardView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="16dp"
                    app:cardCornerRadius="12dp"
                    app:cardElevation="4dp">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical"
                        android:padding="16dp">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="缓存统计"
                            android:textColor="?android:attr/textColorPrimary"
                            android:textSize="18sp"
                            android:textStyle="bold" />

                        <!-- 缓存使用情况 -->
                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="12dp"
                            android:orientation="horizontal">

                            <LinearLayout
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:orientation="vertical">

                                <TextView
                                    android:id="@+id/textCacheSize"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:textColor="?android:attr/textColorPrimary"
                                    android:textSize="16sp"
                                    android:textStyle="bold"
                                    tools:text="256MB / 500MB" />

                                <TextView
                                    android:id="@+id/textCacheCount"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:layout_marginTop="4dp"
                                    android:textColor="?android:attr/textColorSecondary"
                                    android:textSize="14sp"
                                    tools:text="128首歌曲" />

                            </LinearLayout>

                            <LinearLayout
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:gravity="center"
                                android:orientation="vertical">

                                <TextView
                                    android:id="@+id/textCacheUsage"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:textColor="@color/text_primary"
                                    android:textSize="24sp"
                                    android:textStyle="bold"
                                    tools:text="51%" />

                                <TextView
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:text="使用率"
                                    android:textColor="@color/text_secondary"
                                    android:textSize="12sp" />

                            </LinearLayout>

                        </LinearLayout>

                        <!-- 进度条 -->
                        <ProgressBar
                            android:id="@+id/progressBarCacheUsage"
                            style="?android:attr/progressBarStyleHorizontal"
                            android:layout_width="match_parent"
                            android:layout_height="8dp"
                            android:layout_marginTop="12dp"
                            android:max="100"
                            android:progressTint="?attr/colorPrimary"
                            tools:progress="51" />

                    </LinearLayout>

                </com.google.android.material.card.MaterialCardView>

                <!-- 缓存设置卡片 -->
                <com.google.android.material.card.MaterialCardView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="16dp"
                    app:cardCornerRadius="12dp"
                    app:cardElevation="4dp">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical"
                        android:padding="16dp">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="缓存设置"
                            android:textColor="?android:attr/textColorPrimary"
                            android:textSize="18sp"
                            android:textStyle="bold" />

                        <!-- 最大缓存大小 -->
                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="12dp"
                            android:orientation="horizontal">

                            <TextView
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:text="最大缓存大小"
                                android:textColor="?android:attr/textColorPrimary"
                                android:textSize="16sp" />

                            <TextView
                                android:id="@+id/textMaxCacheSize"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:textColor="?android:attr/textColorSecondary"
                                android:textSize="14sp"
                                tools:text="500MB" />

                        </LinearLayout>

                        <!-- 仅WiFi缓存 -->
                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="12dp"
                            android:orientation="horizontal">

                            <TextView
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:text="仅WiFi缓存"
                                android:textColor="?android:attr/textColorPrimary"
                                android:textSize="16sp" />

                            <com.google.android.material.switchmaterial.SwitchMaterial
                                android:id="@+id/switchWifiOnly"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content" />

                        </LinearLayout>

                        <!-- 自动缓存 -->
                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="12dp"
                            android:orientation="horizontal">

                            <TextView
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:text="自动缓存播放歌曲"
                                android:textColor="?android:attr/textColorPrimary"
                                android:textSize="16sp" />

                            <com.google.android.material.switchmaterial.SwitchMaterial
                                android:id="@+id/switchAutoCache"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content" />

                        </LinearLayout>

                        <!-- 设置按钮 -->
                        <com.google.android.material.button.MaterialButton
                            android:id="@+id/buttonCacheSettings"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="16dp"
                            android:text="更多设置"
                            app:icon="@android:drawable/ic_menu_preferences"
                            style="@style/Widget.Material3.Button.OutlinedButton" />

                    </LinearLayout>

                </com.google.android.material.card.MaterialCardView>

                <!-- 操作按钮 -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="16dp"
                    android:orientation="horizontal">

                    <com.google.android.material.button.MaterialButton
                        android:id="@+id/buttonBatchDelete"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginEnd="8dp"
                        android:layout_weight="1"
                        android:text="批量删除"
                        app:icon="@android:drawable/ic_menu_delete"
                        style="@style/Widget.Material3.Button.OutlinedButton" />

                    <com.google.android.material.button.MaterialButton
                        android:id="@+id/buttonClearAll"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="8dp"
                        android:layout_weight="1"
                        android:text="清空缓存"
                        app:icon="@android:drawable/ic_menu_delete"
                        style="@style/Widget.Material3.Button.OutlinedButton" />

                </LinearLayout>

                <!-- 缓存列表 -->
                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/recyclerViewCachedSongs"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:nestedScrollingEnabled="false"
                    tools:listitem="@layout/item_cached_song" />

                <!-- 空状态 -->
                <TextView
                    android:id="@+id/textEmptyState"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="32dp"
                    android:gravity="center"
                    android:text="暂无缓存文件"
                    android:textColor="?android:attr/textColorSecondary"
                    android:textSize="16sp"
                    android:visibility="gone" />

            </LinearLayout>

        </androidx.core.widget.NestedScrollView>

    </androidx.swiperefreshlayout.widget.SwipeRefreshLayout>

    <!-- 加载进度条 -->
    <ProgressBar
        android:id="@+id/progressBar"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:visibility="gone" />

</androidx.coordinatorlayout.widget.CoordinatorLayout>
