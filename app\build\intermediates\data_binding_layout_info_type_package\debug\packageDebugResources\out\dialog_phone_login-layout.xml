<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="dialog_phone_login" modulePackage="com.example.aimusicplayer" filePath="app\src\main\res\layout\dialog_phone_login.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.LinearLayout"><Targets><Target tag="layout/dialog_phone_login_0" view="LinearLayout"><Expressions/><location startLine="1" startOffset="0" endLine="198" endOffset="14"/></Target><Target id="@+id/tv_title" view="TextView"><Expressions/><location startLine="8" startOffset="4" endLine="15" endOffset="33"/></Target><Target id="@+id/tv_subtitle" view="TextView"><Expressions/><location startLine="23" startOffset="8" endLine="30" endOffset="36"/></Target><Target id="@+id/tv_switch_login_method" view="TextView"><Expressions/><location startLine="32" startOffset="8" endLine="39" endOffset="34"/></Target><Target id="@+id/tv_phone_label" view="TextView"><Expressions/><location startLine="42" startOffset="4" endLine="49" endOffset="32"/></Target><Target id="@+id/et_phone" view="EditText"><Expressions/><location startLine="51" startOffset="4" endLine="62" endOffset="29"/></Target><Target id="@+id/password_login_container" view="LinearLayout"><Expressions/><location startLine="65" startOffset="4" endLine="92" endOffset="18"/></Target><Target id="@+id/tv_password_label" view="TextView"><Expressions/><location startLine="71" startOffset="8" endLine="78" endOffset="36"/></Target><Target id="@+id/et_password" view="EditText"><Expressions/><location startLine="80" startOffset="8" endLine="91" endOffset="33"/></Target><Target id="@+id/captcha_login_container" view="LinearLayout"><Expressions/><location startLine="95" startOffset="4" endLine="141" endOffset="18"/></Target><Target id="@+id/tv_captcha_label" view="TextView"><Expressions/><location startLine="102" startOffset="8" endLine="109" endOffset="36"/></Target><Target id="@+id/et_captcha" view="EditText"><Expressions/><location startLine="117" startOffset="12" endLine="128" endOffset="37"/></Target><Target id="@+id/btn_get_captcha" view="Button"><Expressions/><location startLine="130" startOffset="12" endLine="139" endOffset="41"/></Target><Target id="@+id/progress_bar" view="ProgressBar"><Expressions/><location startLine="149" startOffset="8" endLine="155" endOffset="39"/></Target><Target id="@+id/tv_status" view="TextView"><Expressions/><location startLine="158" startOffset="8" endLine="166" endOffset="39"/></Target><Target id="@+id/btn_cancel" view="Button"><Expressions/><location startLine="175" startOffset="8" endLine="184" endOffset="38"/></Target><Target id="@+id/btn_login" view="Button"><Expressions/><location startLine="186" startOffset="8" endLine="195" endOffset="38"/></Target></Targets></Layout>