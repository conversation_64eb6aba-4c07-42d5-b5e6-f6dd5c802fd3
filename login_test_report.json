{"timestamp": "2025-05-26T12:26:25.553Z", "summary": {"totalFlows": 6, "passedFlows": 3, "warningFlows": 3, "failedFlows": 0, "successRate": 100}, "detailed_results": {"primary": {"qrLogin": {"name": "二维码登录流程", "totalSteps": 3, "passedSteps": 3, "warningSteps": 0, "failedSteps": 0, "results": [{"passed": true, "result": {"success": true, "statusCode": 200, "headers": {"server": "openresty", "date": "Mon, 26 May 2025 12:26:15 GMT", "content-type": "application/json; charset=utf-8", "content-length": "80", "connection": "keep-alive", "x-powered-by": "Express", "access-control-allow-credentials": "true", "access-control-allow-origin": "https://ncm.zhenxin.me", "access-control-allow-headers": "X-Requested-With,Content-Type", "access-control-allow-methods": "PUT,POST,GET,DELETE,OPTIONS", "set-cookie": ["NMTID=00OQcJoiHSd-4GhF00HkiPS-KAEWy0AAAGXDI7RNQ; Max-Age=********0; Expires=Thu, 24 May 2035 12:26:15 GMT; Path=/;; SameSite=None; Secure"], "etag": "W/\"50-LwKc5fWoLrj5ZOZHBdHNXB3hFvw\"", "cache-control": "max-age=120", "strict-transport-security": "max-age=********", "alt-svc": "h3=\":443\"; ma=2592000"}, "data": {"data": {"code": 200, "unikey": "7dfccdf4-2209-4209-a9a4-64f1da143c08"}, "code": 200}, "cookies": ["NMTID=00OQcJoiHSd-4GhF00HkiPS-KAEWy0AAAGXDI7RNQ; Max-Age=********0; Expires=Thu, 24 May 2035 12:26:15 GMT; Path=/;; SameSite=None; Secure"]}}, {"passed": true, "result": {"success": true, "statusCode": 200, "headers": {"server": "openresty", "date": "Mon, 26 May 2025 12:26:16 GMT", "content-type": "application/json; charset=utf-8", "content-length": "1865", "connection": "keep-alive", "x-powered-by": "Express", "access-control-allow-credentials": "true", "access-control-allow-origin": "https://ncm.zhenxin.me", "access-control-allow-headers": "X-Requested-With,Content-Type", "access-control-allow-methods": "PUT,POST,GET,DELETE,OPTIONS", "etag": "W/\"749-iSWG4J/ieHaiD9YGE5oCyTJYb0M\"", "cache-control": "max-age=120", "strict-transport-security": "max-age=********", "alt-svc": "h3=\":443\"; ma=2592000"}, "data": {"code": 200, "data": {"qrurl": "https://music.163.com/login?codekey=test", "qrimg": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAJQAAACUCAYAAAB1PADUAAAAAklEQVR4AewaftIAAATfSURBVO3BQY4cSRIEQdNA/f/Lun30UwCJ9GpyuCaCP1K15KRq0UnVopOqRSdVi06qFp1ULTqpWnRSteikatFJ1aKTqkUnVYtOqhadVC06qVr0yUtAfpOaGyA3aiYgk5oJyKRmArJJzQTkN6l546Rq0UnVopOqRZ8sU7MJyBtqJiCTmieA3Kh5AsgTajYB2XRSteikatFJ1aJPvgzIE2qeAHID5AbIpGZSMwF5Asg3AXlCzTedVC06qVp0UrXok3+MmgnIjZoJyI2aCcgEZFIzAZnU/EtOqhadVC06qVr0yT9OzQTkRs0TaiYgE5D/JydVi06qFp1ULfrky9T8JiCTmhsgk5oJyBtqboBMap5Q8zc5qVp0UrXopGrRJ8uA/ElqJiCTmgnIE2omIJOaCcik5g0gf7OTqkUnVYtOqhbhj/yHAXlDzQ2QGzVvAJnU/JedVC06qVp0UrXok5eATGomIJvUTGomIG8AuVEzAblRMwGZ1NwA2aTmm06qFp1ULTqpWvTJMiBvqHkCyBtAJjUTkCfUTEBugGxS8wSQSc0bJ1WLTqoWnVQtwh9ZBORGzQ2Qb1JzA2RSMwF5Qs0NkBs1E5AbNX/SSdWik6pFJ1WLPnkJyKRmAnIDZFLzBJBJzQTkBsikZgLyhJobIDdqJiCTmhsgk5oJyKRm00nVopOqRSdVi/BHXgDym9TcALlRcwPkCTUTkEnNBGRSMwGZ1DwB5EbNN51ULTqpWnRStQh/5A8CcqPmBsikZgIyqXkCyKRmAvI3UfMEkEnNGydVi06qFp1ULfrkJSCTmgnIpOYJIJOaN4DcqLkBcqNmAjKpmYBMaiYgN2omIDdqJjWbTqoWnVQtOqla9MkvAzKpeQLIpGYCMql5Q80baiYgk5oJyI2aCcik5gbIpGbTSdWik6pFJ1WLPlkGZBOQSc0EZFIzAblRswnIpGZS84SaCcikZgJyo+abTqoWnVQtOqla9MkyNTdAnlDzBJAbNTdAJjU3QG6APKFmAnIDZFIzAbkBMql546Rq0UnVopOqRZ/8MjU3QG7UPKHmDSBvqHkCyKRmArJJzaaTqkUnVYtOqhZ98mVA3lBzo2YCMgGZ1ExAbtRMQG7UTECeULNJzQRkUrPppGrRSdWik6pFn3yZmieATEDeUPOEmgnIjZoJyBtANgG5ATKpeeOkatFJ1aKTqkWfvKRmk5ongExqJiCTmifU3AC5UXMDZFLzBJAJyJ90UrXopGrRSdWiT14C8pvUTGpu1GwCMqmZgNwAeQLIpOYNNd90UrXopGrRSdWiT5ap2QTkBsik5gbIpOYGyA2QSc2NmgnIjZon1NwAmdRsOqladFK16KRq0SdfBuQJNb8JyKRmUjMBmdRMQCY1TwDZBOQGyKTmjZOqRSdVi06qFn1SV0AmNROQSc0E5A01E5BJzQTkTzqpWnRSteikatEn/xggN2pugExqJiA3QG7UPAHkBsgbajadVC06qVp0UrXoky9T801qboA8oeZGzQRkUnMD5EbNpGYC8oSaCcg3nVQtOqladFK1CH/kBSC/Sc0EZFKzCcik5gbIpOYJIE+ouQEyqfmmk6pFJ1WLTqoW4Y9ULTmpWnRSteikatFJ1aKTqkUnVYtOqhadVC06qVp0UrXopGrRSdWik6pFJ1WLTqoW/Q+bjjZXr3S9BwAAAABJRU5ErkJggg=="}}, "cookies": []}}, {"passed": true, "result": {"success": true, "statusCode": 200, "headers": {"server": "openresty", "date": "Mon, 26 May 2025 12:26:17 GMT", "content-type": "application/json; charset=utf-8", "content-length": "181", "connection": "keep-alive", "x-powered-by": "Express", "access-control-allow-credentials": "true", "access-control-allow-origin": "https://ncm.zhenxin.me", "access-control-allow-headers": "X-Requested-With,Content-Type", "access-control-allow-methods": "PUT,POST,GET,DELETE,OPTIONS", "set-cookie": ["NMTID=00O8ZcsFBEaWguST0FSm_Rj-63SfVoAAAGXDI7WPw; Max-Age=********0; Expires=Thu, 24 May 2035 12:26:17 GMT; Path=/;; SameSite=None; Secure"], "etag": "W/\"b5-1ILISVod2wAwsongq1UQbtZm5vI\"", "cache-control": "max-age=120", "strict-transport-security": "max-age=********", "alt-svc": "h3=\":443\"; ma=2592000"}, "data": {"code": 800, "message": "二维码不存在或已过期", "cookie": "NMTID=00O8ZcsFBEaWguST0FSm_Rj-63SfVoAAAGXDI7WPw; Max-Age=********0; Expires=Thu, 24 May 2035 12:26:17 GMT; Path=/;"}, "cookies": ["NMTID=00O8ZcsFBEaWguST0FSm_Rj-63SfVoAAAGXDI7WPw; Max-Age=********0; Expires=Thu, 24 May 2035 12:26:17 GMT; Path=/;; SameSite=None; Secure"]}}]}, "captchaLogin": {"name": "验证码登录流程", "totalSteps": 3, "passedSteps": 3, "warningSteps": 0, "failedSteps": 0, "results": [{"passed": true, "result": {"success": true, "statusCode": 200, "headers": {"server": "openresty", "date": "Mon, 26 May 2025 12:26:17 GMT", "content-type": "application/json; charset=utf-8", "content-length": "80", "connection": "keep-alive", "x-powered-by": "Express", "access-control-allow-credentials": "true", "access-control-allow-origin": "https://ncm.zhenxin.me", "access-control-allow-headers": "X-Requested-With,Content-Type", "access-control-allow-methods": "PUT,POST,GET,DELETE,OPTIONS", "etag": "W/\"50-s5c5Bnnr9Cb/xmSorLiuZCswzMw\"", "cache-control": "max-age=120", "strict-transport-security": "max-age=********", "alt-svc": "h3=\":443\"; ma=2592000"}, "data": {"code": 400, "message": "当天发送验证码的条数超过限制", "data": false}, "cookies": []}}, {"passed": true, "result": {"success": true, "statusCode": 503, "headers": {"server": "openresty", "date": "Mon, 26 May 2025 12:26:18 GMT", "content-type": "application/json; charset=utf-8", "content-length": "53", "connection": "keep-alive", "x-powered-by": "Express", "access-control-allow-credentials": "true", "access-control-allow-origin": "https://ncm.zhenxin.me", "access-control-allow-headers": "X-Requested-With,Content-Type", "access-control-allow-methods": "PUT,POST,GET,DELETE,OPTIONS", "etag": "W/\"35-8HLq5DQ/sSWWL3UxijLYNX15u48\"", "cache-control": "no-cache, no-store, must-revalidate"}, "data": {"message": "验证码错误", "code": 503, "data": false}, "cookies": []}}, {"passed": true, "result": {"success": true, "statusCode": 503, "headers": {"server": "openresty", "date": "Mon, 26 May 2025 12:26:19 GMT", "content-type": "application/json; charset=utf-8", "content-length": "64", "connection": "keep-alive", "x-powered-by": "Express", "access-control-allow-credentials": "true", "access-control-allow-origin": "https://ncm.zhenxin.me", "access-control-allow-headers": "X-Requested-With,Content-Type", "access-control-allow-methods": "PUT,POST,GET,DELETE,OPTIONS", "set-cookie": ["NMTID=00Ot907whwOYTPgdEL0pDNV7z7orLIAAAGXDI7e5A; Max-Age=********0; Expires=Thu, 24 May 2035 12:26:19 GMT; Path=/;"], "etag": "W/\"40-+BYWMdNVXutv2JterJ4Kfpp6Bhs\"", "cache-control": "no-cache, no-store, must-revalidate"}, "data": {"msg": "验证码错误", "code": 503, "message": "验证码错误"}, "cookies": ["NMTID=00Ot907whwOYTPgdEL0pDNV7z7orLIAAAGXDI7e5A; Max-Age=********0; Expires=Thu, 24 May 2035 12:26:19 GMT; Path=/;"]}}]}, "statusCheck": {"name": "登录状态检查", "totalSteps": 2, "passedSteps": 1, "warningSteps": 1, "failedSteps": 0, "results": [{"passed": false, "result": {"success": true, "statusCode": 200, "headers": {"server": "openresty", "date": "Mon, 26 May 2025 12:26:19 GMT", "content-type": "application/json; charset=utf-8", "content-length": "51", "connection": "keep-alive", "x-powered-by": "Express", "access-control-allow-credentials": "true", "access-control-allow-origin": "https://ncm.zhenxin.me", "access-control-allow-headers": "X-Requested-With,Content-Type", "access-control-allow-methods": "PUT,POST,GET,DELETE,OPTIONS", "etag": "W/\"33-FeciofGtVGbBLjeXDrf2feuS8DQ\"", "cache-control": "max-age=120", "strict-transport-security": "max-age=********", "alt-svc": "h3=\":443\"; ma=2592000"}, "data": {"data": {"code": 200, "account": null, "profile": null}}, "cookies": []}, "warning": true}, {"passed": true, "result": {"success": true, "statusCode": 200, "headers": {"server": "openresty", "date": "Mon, 26 May 2025 12:26:20 GMT", "content-type": "application/json; charset=utf-8", "content-length": "42", "connection": "keep-alive", "x-powered-by": "Express", "access-control-allow-credentials": "true", "access-control-allow-origin": "https://ncm.zhenxin.me", "access-control-allow-headers": "X-Requested-With,Content-Type", "access-control-allow-methods": "PUT,POST,GET,DELETE,OPTIONS", "etag": "W/\"2a-TdKSRUX0zB8qdbCYF0XAfiSQoe4\"", "cache-control": "max-age=120", "strict-transport-security": "max-age=********", "alt-svc": "h3=\":443\"; ma=2592000"}, "data": {"code": 200, "account": null, "profile": null}, "cookies": []}}]}}, "backup": {"qrLogin": {"name": "二维码登录流程", "totalSteps": 3, "passedSteps": 3, "warningSteps": 0, "failedSteps": 0, "results": [{"passed": true, "result": {"success": true, "statusCode": 200, "headers": {"access-control-allow-credentials": "true", "access-control-allow-headers": "X-Requested-With,Content-Type", "access-control-allow-methods": "PUT,POST,GET,DELETE,OPTIONS", "access-control-allow-origin": "https://**********-4499wupl9z.ap-guangzhou.tencentscf.com", "cache-control": "max-age=120", "content-disposition": "attachment", "content-length": "80", "content-type": "application/json; charset=utf-8", "date": "Mon, 26 May 2025 12:26:21 GMT", "etag": "W/\"50-T1+il21ERq7sbtzDLym20sEXeRU\"", "server": "openresty", "set-cookie": ["NMTID=00Ord6hqnvDnadlekBfnyoogu3pn1wAAAGXDI7ndw; Max-Age=********0; Expires=Thu, 24 May 2035 12:26:21 GMT; Path=/;"], "vary": "Accept-Encoding", "x-powered-by": "Express", "x-scf-private-duration": "83", "x-scf-private-memsize": "40968192", "x-scf-private-wantraffic": "2469", "x-scf-request-id": "a1a2101c-3a2c-11f0-b8c7-525400befb55", "x-scf-status": "200"}, "data": {"data": {"code": 200, "unikey": "59cb7a46-10ac-4a70-bb5f-35cfb118d4e7"}, "code": 200}, "cookies": ["NMTID=00Ord6hqnvDnadlekBfnyoogu3pn1wAAAGXDI7ndw; Max-Age=********0; Expires=Thu, 24 May 2035 12:26:21 GMT; Path=/;"]}}, {"passed": true, "result": {"success": true, "statusCode": 200, "headers": {"access-control-allow-credentials": "true", "access-control-allow-headers": "X-Requested-With,Content-Type", "access-control-allow-methods": "PUT,POST,GET,DELETE,OPTIONS", "access-control-allow-origin": "https://**********-4499wupl9z.ap-guangzhou.tencentscf.com", "cache-control": "max-age=120", "content-disposition": "attachment", "content-length": "1865", "content-type": "application/json; charset=utf-8", "date": "Mon, 26 May 2025 12:26:22 GMT", "etag": "W/\"749-iSWG4J/ieHaiD9YGE5oCyTJYb0M\"", "server": "openresty", "vary": "Accept-Encoding, Accept-Encoding", "x-powered-by": "Express", "x-scf-private-duration": "73", "x-scf-private-memsize": "41508864", "x-scf-private-wantraffic": "0", "x-scf-request-id": "a207238d-3a2c-11f0-b8c7-525400befb55", "x-scf-status": "200"}, "data": {"code": 200, "data": {"qrurl": "https://music.163.com/login?codekey=test", "qrimg": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAJQAAACUCAYAAAB1PADUAAAAAklEQVR4AewaftIAAATfSURBVO3BQY4cSRIEQdNA/f/Lun30UwCJ9GpyuCaCP1K15KRq0UnVopOqRSdVi06qFp1ULTqpWnRSteikatFJ1aKTqkUnVYtOqhadVC06qVr0yUtAfpOaGyA3aiYgk5oJyKRmArJJzQTkN6l546Rq0UnVopOqRZ8sU7MJyBtqJiCTmieA3Kh5AsgTajYB2XRSteikatFJ1aJPvgzIE2qeAHID5AbIpGZSMwF5Asg3AXlCzTedVC06qVp0UrXok3+MmgnIjZoJyI2aCcgEZFIzAZnU/EtOqhadVC06qVr0yT9OzQTkRs0TaiYgE5D/JydVi06qFp1ULfrky9T8JiCTmhsgk5oJyBtqboBMap5Q8zc5qVp0UrXopGrRJ8uA/ElqJiCTmgnIE2omIJOaCcik5g0gf7OTqkUnVYtOqhbhj/yHAXlDzQ2QGzVvAJnU/JedVC06qVp0UrXok5eATGomIJvUTGomIG8AuVEzAblRMwGZ1NwA2aTmm06qFp1ULTqpWvTJMiBvqHkCyBtAJjUTkCfUTEBugGxS8wSQSc0bJ1WLTqoWnVQtwh9ZBORGzQ2Qb1JzA2RSMwF5Qs0NkBs1E5AbNX/SSdWik6pFJ1WLPnkJyKRmAnIDZFLzBJBJzQTkBsikZgLyhJobIDdqJiCTmhsgk5oJyKRm00nVopOqRSdVi/BHXgDym9TcALlRcwPkCTUTkEnNBGRSMwGZ1DwB5EbNN51ULTqpWnRStQh/5A8CcqPmBsikZgIyqXkCyKRmAvI3UfMEkEnNGydVi06qFp1ULfrkJSCTmgnIpOYJIJOaN4DcqLkBcqNmAjKpmYBMaiYgN2omIDdqJjWbTqoWnVQtOqla9MkvAzKpeQLIpGYCMql5Q80baiYgk5oJyI2aCcik5gbIpGbTSdWik6pFJ1WLPlkGZBOQSc0EZFIzAblRswnIpGZS84SaCcikZgJyo+abTqoWnVQtOqla9MkyNTdAnlDzBJAbNTdAJjU3QG6APKFmAnIDZFIzAbkBMql546Rq0UnVopOqRZ/8MjU3QG7UPKHmDSBvqHkCyKRmArJJzaaTqkUnVYtOqhZ98mVA3lBzo2YCMgGZ1ExAbtRMQG7UTECeULNJzQRkUrPppGrRSdWik6pFn3yZmieATEDeUPOEmgnIjZoJyBtANgG5ATKpeeOkatFJ1aKTqkWfvKRmk5ongExqJiCTmifU3AC5UXMDZFLzBJAJyJ90UrXopGrRSdWiT14C8pvUTGpu1GwCMqmZgNwAeQLIpOYNNd90UrXopGrRSdWiT5ap2QTkBsik5gbIpOYGyA2QSc2NmgnIjZon1NwAmdRsOqladFK16KRq0SdfBuQJNb8JyKRmUjMBmdRMQCY1TwDZBOQGyKTmjZOqRSdVi06qFn1SV0AmNROQSc0E5A01E5BJzQTkTzqpWnRSteikatEn/xggN2pugExqJiA3QG7UPAHkBsgbajadVC06qVp0UrXoky9T801qboA8oeZGzQRkUnMD5EbNpGYC8oSaCcg3nVQtOqladFK1CH/kBSC/Sc0EZFKzCcik5gbIpOYJIE+ouQEyqfmmk6pFJ1WLTqoW4Y9ULTmpWnRSteikatFJ1aKTqkUnVYtOqhadVC06qVp0UrXopGrRSdWik6pFJ1WLTqoW/Q+bjjZXr3S9BwAAAABJRU5ErkJggg=="}}, "cookies": []}}, {"passed": true, "result": {"success": true, "statusCode": 200, "headers": {"access-control-allow-credentials": "true", "access-control-allow-headers": "X-Requested-With,Content-Type", "access-control-allow-methods": "PUT,POST,GET,DELETE,OPTIONS", "access-control-allow-origin": "https://**********-4499wupl9z.ap-guangzhou.tencentscf.com", "cache-control": "max-age=120", "content-disposition": "attachment", "content-length": "181", "content-type": "application/json; charset=utf-8", "date": "Mon, 26 May 2025 12:26:22 GMT", "etag": "W/\"b5-YFbeyynlJzUaZsPWS7euNrhtoto\"", "server": "openresty", "set-cookie": ["NMTID=00OonbqvN6QVUgJjUbHoYzQWpWD1HcAAAGXDI7scg; Max-Age=********0; Expires=Thu, 24 May 2035 12:26:22 GMT; Path=/;"], "vary": "Accept-Encoding", "x-powered-by": "Express", "x-scf-private-duration": "86", "x-scf-private-memsize": "40734720", "x-scf-private-wantraffic": "2507", "x-scf-request-id": "a268ca0c-3a2c-11f0-b8c7-525400befb55", "x-scf-status": "200"}, "data": {"code": 800, "message": "二维码不存在或已过期", "cookie": "NMTID=00OonbqvN6QVUgJjUbHoYzQWpWD1HcAAAGXDI7scg; Max-Age=********0; Expires=Thu, 24 May 2035 12:26:22 GMT; Path=/;"}, "cookies": ["NMTID=00OonbqvN6QVUgJjUbHoYzQWpWD1HcAAAGXDI7scg; Max-Age=********0; Expires=Thu, 24 May 2035 12:26:22 GMT; Path=/;"]}}]}, "captchaLogin": {"name": "验证码登录流程", "totalSteps": 3, "passedSteps": 2, "warningSteps": 1, "failedSteps": 0, "results": [{"passed": false, "result": {"success": true, "statusCode": 405, "headers": {"access-control-allow-credentials": "true", "access-control-allow-headers": "X-Requested-With,Content-Type", "access-control-allow-methods": "PUT,POST,GET,DELETE,OPTIONS", "access-control-allow-origin": "https://**********-4499wupl9z.ap-guangzhou.tencentscf.com", "cache-control": "no-cache, no-store, must-revalidate", "content-disposition": "attachment", "content-length": "65", "content-type": "application/json; charset=utf-8", "date": "Mon, 26 May 2025 12:26:23 GMT", "etag": "W/\"41-mQ6gQyrd7jngpq27FQU3nmk2ZNE\"", "server": "openresty", "x-powered-by": "Express", "x-scf-private-duration": "187", "x-scf-private-memsize": "40849408", "x-scf-private-wantraffic": "2410", "x-scf-request-id": "a2c9fad9-3a2c-11f0-b8c7-525400befb55", "x-scf-status": "405"}, "data": {"code": 405, "message": "发送验证码间隔过短", "data": false}, "cookies": []}, "warning": true}, {"passed": true, "result": {"success": true, "statusCode": 503, "headers": {"access-control-allow-credentials": "true", "access-control-allow-headers": "X-Requested-With,Content-Type", "access-control-allow-methods": "PUT,POST,GET,DELETE,OPTIONS", "access-control-allow-origin": "https://**********-4499wupl9z.ap-guangzhou.tencentscf.com", "cache-control": "no-cache, no-store, must-revalidate", "content-disposition": "attachment", "content-length": "53", "content-type": "application/json; charset=utf-8", "date": "Mon, 26 May 2025 12:26:24 GMT", "etag": "W/\"35-8HLq5DQ/sSWWL3UxijLYNX15u48\"", "server": "openresty", "x-powered-by": "Express", "x-scf-private-duration": "84", "x-scf-private-memsize": "41254912", "x-scf-private-wantraffic": "2448", "x-scf-request-id": "a339b689-3a2c-11f0-b8c7-525400befb55", "x-scf-status": "503"}, "data": {"message": "验证码错误", "code": 503, "data": false}, "cookies": []}}, {"passed": true, "result": {"success": true, "statusCode": 503, "headers": {"access-control-allow-credentials": "true", "access-control-allow-headers": "X-Requested-With,Content-Type", "access-control-allow-methods": "PUT,POST,GET,DELETE,OPTIONS", "access-control-allow-origin": "https://**********-4499wupl9z.ap-guangzhou.tencentscf.com", "cache-control": "no-cache, no-store, must-revalidate", "content-disposition": "attachment", "content-length": "64", "content-type": "application/json; charset=utf-8", "date": "Mon, 26 May 2025 12:26:24 GMT", "etag": "W/\"40-+BYWMdNVXutv2JterJ4Kfpp6Bhs\"", "server": "openresty", "set-cookie": ["NMTID=00O_uAZO7DU5Gg6DUs-jKNZKjJEU20AAAGXDI70vw; Max-Age=********0; Expires=Thu, 24 May 2035 12:26:24 GMT; Path=/;"], "x-powered-by": "Express", "x-scf-private-duration": "163", "x-scf-private-memsize": "41058304", "x-scf-private-wantraffic": "2713", "x-scf-request-id": "a39fbe22-3a2c-11f0-b8c7-525400befb55", "x-scf-status": "503"}, "data": {"msg": "验证码错误", "code": 503, "message": "验证码错误"}, "cookies": ["NMTID=00O_uAZO7DU5Gg6DUs-jKNZKjJEU20AAAGXDI70vw; Max-Age=********0; Expires=Thu, 24 May 2035 12:26:24 GMT; Path=/;"]}}]}, "statusCheck": {"name": "登录状态检查", "totalSteps": 2, "passedSteps": 1, "warningSteps": 1, "failedSteps": 0, "results": [{"passed": false, "result": {"success": true, "statusCode": 200, "headers": {"access-control-allow-credentials": "true", "access-control-allow-headers": "X-Requested-With,Content-Type", "access-control-allow-methods": "PUT,POST,GET,DELETE,OPTIONS", "access-control-allow-origin": "https://**********-4499wupl9z.ap-guangzhou.tencentscf.com", "cache-control": "max-age=120", "content-disposition": "attachment", "content-length": "51", "content-type": "application/json; charset=utf-8", "date": "Mon, 26 May 2025 12:26:25 GMT", "etag": "W/\"33-FeciofGtVGbBLjeXDrf2feuS8DQ\"", "server": "openresty", "vary": "Accept-Encoding", "x-powered-by": "Express", "x-scf-private-duration": "93", "x-scf-private-memsize": "********", "x-scf-private-wantraffic": "2397", "x-scf-request-id": "a40d9a44-3a2c-11f0-b8c7-525400befb55", "x-scf-status": "200"}, "data": {"data": {"code": 200, "account": null, "profile": null}}, "cookies": []}, "warning": true}, {"passed": true, "result": {"success": true, "statusCode": 200, "headers": {"access-control-allow-credentials": "true", "access-control-allow-headers": "X-Requested-With,Content-Type", "access-control-allow-methods": "PUT,POST,GET,DELETE,OPTIONS", "access-control-allow-origin": "https://**********-4499wupl9z.ap-guangzhou.tencentscf.com", "cache-control": "max-age=120", "content-disposition": "attachment", "content-length": "42", "content-type": "application/json; charset=utf-8", "date": "Mon, 26 May 2025 12:26:26 GMT", "etag": "W/\"2a-TdKSRUX0zB8qdbCYF0XAfiSQoe4\"", "server": "openresty", "vary": "Accept-Encoding", "x-powered-by": "Express", "x-scf-private-duration": "116", "x-scf-private-memsize": "********", "x-scf-private-wantraffic": "2395", "x-scf-request-id": "a4736299-3a2c-11f0-b8c7-525400befb55", "x-scf-status": "200"}, "data": {"code": 200, "account": null, "profile": null}, "cookies": []}}]}}}, "conclusions": {"api_fix_successful": true, "post_method_working": true, "headers_configured": true, "qr_login_available": true, "captcha_login_responding": true}}