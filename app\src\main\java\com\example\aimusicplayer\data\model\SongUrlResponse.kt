package com.example.aimusicplayer.data.model

import com.google.gson.annotations.SerializedName

/**
 * 歌曲播放链接API响应模型
 * 严格对应网易云音乐API /song/url/v1 接口
 * 参考：ponymusic-master项目标准
 */
data class SongUrlResponse(
    val data: List<SongUrlData>? = null,
) : BaseResponse()

/**
 * 歌曲播放链接数据
 * 对应API返回的data数组中的每个链接对象
 */
data class SongUrlData(
    @SerializedName("id")
    val id: Long = 0,

    @SerializedName("url")
    val url: String? = null,

    @SerializedName("br")
    val bitrate: Int = 0,

    @SerializedName("size")
    val size: Long = 0,

    @SerializedName("md5")
    val md5: String? = null,

    @SerializedName("code")
    val code: Int = 0,

    @SerializedName("expi")
    val expi: Long = 0,

    @SerializedName("type")
    val type: String? = null,

    @SerializedName("gain")
    val gain: Float = 0f,

    @SerializedName("peak")
    val peak: Float = 0f,

    @SerializedName("fee")
    val fee: Int = 0,

    @SerializedName("uf")
    val uf: Any? = null,

    @SerializedName("payed")
    val payed: Int = 0,

    @SerializedName("flag")
    val flag: Int = 0,

    @SerializedName("canExtend")
    val canExtend: Boolean = false,

    @SerializedName("freeTrialInfo")
    val freeTrialInfo: Any? = null,

    @SerializedName("level")
    val level: String? = null,

    @SerializedName("encodeType")
    val encodeType: String? = null,

    @SerializedName("freeTrialPrivilege")
    val freeTrialPrivilege: FreeTrialPrivilege? = null,

    @SerializedName("freeTimeTrialPrivilege")
    val freeTimeTrialPrivilege: FreeTimeTrialPrivilege? = null,

    @SerializedName("urlSource")
    val urlSource: Int = 0,

    @SerializedName("rightSource")
    val rightSource: Int = 0,

    @SerializedName("podcastCtrp")
    val podcastCtrp: Any? = null,

    @SerializedName("effectTypes")
    val effectTypes: Any? = null,

    @SerializedName("time")
    val time: Long = 0,
) {
    /**
     * 检查歌曲是否可播放
     */
    fun isPlayable(): Boolean {
        return !url.isNullOrEmpty() && code == 200
    }

    /**
     * 获取音质描述
     */
    fun getQualityDescription(): String {
        return when {
            bitrate >= 999000 -> "无损"
            bitrate >= 320000 -> "极高"
            bitrate >= 192000 -> "较高"
            bitrate >= 128000 -> "标准"
            else -> "流畅"
        }
    }

    /**
     * 获取文件大小描述
     */
    fun getSizeDescription(): String {
        return when {
            size > 1024 * 1024 * 1024 -> String.format("%.1f GB", size / (1024.0 * 1024.0 * 1024.0))
            size > 1024 * 1024 -> String.format("%.1f MB", size / (1024.0 * 1024.0))
            size > 1024 -> String.format("%.1f KB", size / 1024.0)
            else -> "$size B"
        }
    }
}

/**
 * 免费试听特权信息
 */
data class FreeTrialPrivilege(
    @SerializedName("resConsumable")
    val resConsumable: Boolean = false,

    @SerializedName("userConsumable")
    val userConsumable: Boolean = false,

    @SerializedName("listenType")
    val listenType: Any? = null,

    @SerializedName("cannotListenReason")
    val cannotListenReason: Any? = null,

    @SerializedName("playReason")
    val playReason: Any? = null,
)

/**
 * 免费时长试听特权信息
 */
data class FreeTimeTrialPrivilege(
    @SerializedName("resConsumable")
    val resConsumable: Boolean = false,

    @SerializedName("userConsumable")
    val userConsumable: Boolean = false,

    @SerializedName("type")
    val type: Int = 0,

    @SerializedName("remainTime")
    val remainTime: Long = 0,
)

/**
 * 数据转换扩展方法
 * 将API响应数据转换为应用内部使用的播放链接
 */
fun SongUrlData.getPlayableUrl(): String? {
    return if (isPlayable()) url else null
}

/**
 * 批量转换方法
 */
fun List<SongUrlData>.getPlayableUrls(): Map<Long, String> {
    return this.filter { it.isPlayable() }
        .associate { it.id to (it.url ?: "") }
}

/**
 * SongUrlResponse扩展方法 - 获取播放链接映射
 */
fun SongUrlResponse.getUrlMap(): Map<Long, String> {
    return data?.getPlayableUrls() ?: emptyMap()
}

/**
 * 根据歌曲ID获取播放链接
 */
fun SongUrlResponse.getUrlById(songId: Long): String? {
    return data?.find { it.id == songId }?.getPlayableUrl()
}
