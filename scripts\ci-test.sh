#!/bin/bash

# CI/CD 测试脚本
# 用于验证CI/CD配置的正确性

set -e

echo "🚀 开始CI/CD配置验证..."

# 检查必要的文件
echo "📋 检查必要文件..."
if [ ! -f ".github/workflows/android-tests.yml" ]; then
    echo "❌ GitHub Actions配置文件不存在"
    exit 1
fi
echo "✅ GitHub Actions配置文件存在"

if [ ! -f "gradlew" ]; then
    echo "❌ Gradle Wrapper不存在"
    exit 1
fi
echo "✅ Gradle Wrapper存在"

# 检查权限
echo "🔐 检查文件权限..."
chmod +x gradlew
echo "✅ Gradle Wrapper权限设置完成"

# 验证Gradle配置
echo "⚙️ 验证Gradle配置..."
./gradlew --version
echo "✅ Gradle配置正常"

# 编译检查
echo "🔨 编译检查..."
./gradlew compileDebugKotlin --console=plain --no-daemon --quiet
echo "✅ 编译检查通过"

# 测试检查
echo "🧪 测试检查..."
./gradlew testDebugUnitTest --console=plain --no-daemon --quiet
echo "✅ 测试检查通过"

echo ""
echo "🎉 CI/CD配置验证完成！"
echo ""
echo "📊 验证结果："
echo "✅ GitHub Actions配置正确"
echo "✅ Gradle构建系统正常"
echo "✅ 编译流程正常"
echo "✅ 测试流程正常"
echo ""
echo "🚀 CI/CD流程已就绪，可以推送到GitHub触发自动化流程！"
