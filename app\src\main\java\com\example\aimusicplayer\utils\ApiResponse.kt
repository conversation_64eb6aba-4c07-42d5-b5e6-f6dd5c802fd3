package com.example.aimusicplayer.utils

import retrofit2.HttpException
import java.io.IOException
import java.net.SocketTimeoutException
import java.net.UnknownHostException

/**
 * API响应工具类
 * 提供网络错误消息处理
 */
object ApiResponse {

    /**
     * 获取网络错误消息
     * @param throwable 错误
     * @return 错误消息
     */
    fun getNetworkErrorMessage(throwable: Throwable): String {
        return when (throwable) {
            is UnknownHostException -> "网络连接失败，请检查网络设置"
            is SocketTimeoutException -> "网络请求超时，请重试"
            is IOException -> "网络连接异常，请检查网络"
            is HttpException -> {
                when (throwable.code()) {
                    400 -> "请求参数错误"
                    401 -> "未授权，请重新登录"
                    403 -> "访问被拒绝"
                    404 -> "请求的资源不存在"
                    500 -> "服务器内部错误"
                    502 -> "网关错误"
                    503 -> "服务不可用"
                    else -> "网络请求失败 (${throwable.code()})"
                }
            }
            else -> throwable.message ?: "未知错误"
        }
    }
}
