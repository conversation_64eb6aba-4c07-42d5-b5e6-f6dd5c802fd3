package com.example.aimusicplayer.data.model

import com.google.gson.annotations.SerializedName

/**
 * 推荐歌单API响应模型
 * 严格对应网易云音乐API /recommend/resource 接口
 * 参考：NeteaseCloudMusicApiBackup-main/module/recommend_resource.js
 */
data class RecommendPlaylistResponse(
    val recommend: List<RecommendPlaylistData>? = null,
) : BaseResponse()

/**
 * 推荐歌单数据项
 * 对应API返回的recommend数组中的每个歌单对象
 */
data class RecommendPlaylistData(
    val id: Long = 0,
    val name: String = "",

    @SerializedName("picUrl")
    val picUrl: String = "",

    @SerializedName("copywriter")
    val copywriter: String = "",

    @SerializedName("playCount")
    val playCount: Long = 0,

    @SerializedName("trackCount")
    val trackCount: Int = 0,

    @SerializedName("userId")
    val userId: Long = 0,

    @SerializedName("creator")
    val creator: RecommendPlaylistCreator? = null,

    @SerializedName("alg")
    val alg: String = "",
)

/**
 * 推荐歌单创建者信息
 */
data class RecommendPlaylistCreator(
    @SerializedName("userId")
    val userId: Long = 0,

    @SerializedName("nickname")
    val nickname: String = "",

    @SerializedName("avatarUrl")
    val avatarUrl: String = "",
)

/**
 * 数据转换扩展方法
 * 将API响应数据转换为应用内部的PlayList模型
 */
fun RecommendPlaylistData.toPlayList(): PlayList {
    return PlayList(
        id = this.id.toString(),
        name = this.name,
        coverImgUrl = this.picUrl,
        description = this.copywriter,
        creatorId = this.creator?.userId?.toString() ?: "",
        creatorName = this.creator?.nickname ?: "",
        songCount = this.trackCount,
        playCount = this.playCount.toInt(),
        subscribed = false,
        songs = mutableListOf(),
    )
}

/**
 * 批量转换方法
 */
fun List<RecommendPlaylistData>.toPlayLists(): List<PlayList> {
    return this.map { it.toPlayList() }
}
