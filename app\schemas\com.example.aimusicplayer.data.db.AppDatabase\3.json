{"formatVersion": 1, "database": {"version": 3, "identityHash": "e090f777145fb91f3b50ad2734377926", "entities": [{"tableName": "songs", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`type` INTEGER NOT NULL, `song_id` INTEGER NOT NULL, `title` TEXT NOT NULL, `artist` TEXT NOT NULL, `artist_id` INTEGER NOT NULL, `album` TEXT NOT NULL, `album_id` INTEGER NOT NULL, `album_cover` TEXT NOT NULL, `duration` INTEGER NOT NULL, `uri` TEXT NOT NULL DEFAULT '', `path` TEXT NOT NULL, `file_name` TEXT NOT NULL, `file_size` INTEGER NOT NULL, `is_vip` INTEGER NOT NULL, `is_favorite` INTEGER NOT NULL, `last_played_time` INTEGER NOT NULL, `unique_id` TEXT NOT NULL, PRIMARY KEY(`unique_id`))", "fields": [{"fieldPath": "type", "columnName": "type", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "songId", "columnName": "song_id", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "title", "columnName": "title", "affinity": "TEXT", "notNull": true}, {"fieldPath": "artist", "columnName": "artist", "affinity": "TEXT", "notNull": true}, {"fieldPath": "artistId", "columnName": "artist_id", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "album", "columnName": "album", "affinity": "TEXT", "notNull": true}, {"fieldPath": "albumId", "columnName": "album_id", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "albumCover", "columnName": "album_cover", "affinity": "TEXT", "notNull": true}, {"fieldPath": "duration", "columnName": "duration", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "uri", "columnName": "uri", "affinity": "TEXT", "notNull": true, "defaultValue": "''"}, {"fieldPath": "path", "columnName": "path", "affinity": "TEXT", "notNull": true}, {"fieldPath": "fileName", "columnName": "file_name", "affinity": "TEXT", "notNull": true}, {"fieldPath": "fileSize", "columnName": "file_size", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "isVip", "columnName": "is_vip", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "isFavorite", "columnName": "is_favorite", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "lastPlayedTime", "columnName": "last_played_time", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "uniqueId", "columnName": "unique_id", "affinity": "TEXT", "notNull": true}], "primaryKey": {"autoGenerate": false, "columnNames": ["unique_id"]}, "indices": [{"name": "index_songs_title", "unique": false, "columnNames": ["title"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_songs_title` ON `${TABLE_NAME}` (`title`)"}, {"name": "index_songs_artist", "unique": false, "columnNames": ["artist"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_songs_artist` ON `${TABLE_NAME}` (`artist`)"}, {"name": "index_songs_album", "unique": false, "columnNames": ["album"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_songs_album` ON `${TABLE_NAME}` (`album`)"}], "foreignKeys": []}, {"tableName": "playlists", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`playlist_id` INTEGER NOT NULL, `name` TEXT NOT NULL, `cover_url` TEXT NOT NULL, `description` TEXT NOT NULL, `creator_id` INTEGER NOT NULL, `creator_name` TEXT NOT NULL, `song_count` INTEGER NOT NULL, `play_count` INTEGER NOT NULL, `is_subscribed` INTEGER NOT NULL, `create_time` INTEGER NOT NULL, `update_time` INTEGER NOT NULL, `is_local` INTEGER NOT NULL, PRIMARY KEY(`playlist_id`))", "fields": [{"fieldPath": "playlistId", "columnName": "playlist_id", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "name", "columnName": "name", "affinity": "TEXT", "notNull": true}, {"fieldPath": "coverUrl", "columnName": "cover_url", "affinity": "TEXT", "notNull": true}, {"fieldPath": "description", "columnName": "description", "affinity": "TEXT", "notNull": true}, {"fieldPath": "creatorId", "columnName": "creator_id", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "<PERSON><PERSON><PERSON>", "columnName": "creator_name", "affinity": "TEXT", "notNull": true}, {"fieldPath": "songCount", "columnName": "song_count", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "playCount", "columnName": "play_count", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "isSubscribed", "columnName": "is_subscribed", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "createTime", "columnName": "create_time", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "updateTime", "columnName": "update_time", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "isLocal", "columnName": "is_local", "affinity": "INTEGER", "notNull": true}], "primaryKey": {"autoGenerate": false, "columnNames": ["playlist_id"]}, "indices": [], "foreignKeys": []}, {"tableName": "playlist_song_cross_ref", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`playlist_id` INTEGER NOT NULL, `song_unique_id` TEXT NOT NULL, `sort_order` INTEGER NOT NULL, `add_time` INTEGER NOT NULL, PRIMARY KEY(`playlist_id`, `song_unique_id`))", "fields": [{"fieldPath": "playlistId", "columnName": "playlist_id", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "songUniqueId", "columnName": "song_unique_id", "affinity": "TEXT", "notNull": true}, {"fieldPath": "sortOrder", "columnName": "sort_order", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "addTime", "columnName": "add_time", "affinity": "INTEGER", "notNull": true}], "primaryKey": {"autoGenerate": false, "columnNames": ["playlist_id", "song_unique_id"]}, "indices": [{"name": "index_playlist_song_cross_ref_playlist_id", "unique": false, "columnNames": ["playlist_id"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_playlist_song_cross_ref_playlist_id` ON `${TABLE_NAME}` (`playlist_id`)"}, {"name": "index_playlist_song_cross_ref_song_unique_id", "unique": false, "columnNames": ["song_unique_id"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_playlist_song_cross_ref_song_unique_id` ON `${TABLE_NAME}` (`song_unique_id`)"}], "foreignKeys": []}, {"tableName": "users", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`user_id` INTEGER NOT NULL, `username` TEXT NOT NULL, `nickname` TEXT NOT NULL, `avatar_url` TEXT NOT NULL, `background_url` TEXT NOT NULL, `signature` TEXT NOT NULL, `follows` INTEGER NOT NULL, `followers` INTEGER NOT NULL, `level` INTEGER NOT NULL, `vip_type` INTEGER NOT NULL, `last_login_time` INTEGER NOT NULL, `cookie` TEXT NOT NULL, PRIMARY KEY(`user_id`))", "fields": [{"fieldPath": "userId", "columnName": "user_id", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "username", "columnName": "username", "affinity": "TEXT", "notNull": true}, {"fieldPath": "nickname", "columnName": "nickname", "affinity": "TEXT", "notNull": true}, {"fieldPath": "avatarUrl", "columnName": "avatar_url", "affinity": "TEXT", "notNull": true}, {"fieldPath": "backgroundUrl", "columnName": "background_url", "affinity": "TEXT", "notNull": true}, {"fieldPath": "signature", "columnName": "signature", "affinity": "TEXT", "notNull": true}, {"fieldPath": "follows", "columnName": "follows", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "followers", "columnName": "followers", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "level", "columnName": "level", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "vipType", "columnName": "vip_type", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "lastLoginTime", "columnName": "last_login_time", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "cookie", "columnName": "cookie", "affinity": "TEXT", "notNull": true}], "primaryKey": {"autoGenerate": false, "columnNames": ["user_id"]}, "indices": [], "foreignKeys": []}, {"tableName": "api_cache", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`cache_key` TEXT NOT NULL, `data` TEXT NOT NULL, `cache_time` INTEGER NOT NULL, `expiration_time` INTEGER NOT NULL, `cache_type` TEXT NOT NULL, PRIMARY KEY(`cache_key`))", "fields": [{"fieldPath": "cache<PERSON>ey", "columnName": "cache_key", "affinity": "TEXT", "notNull": true}, {"fieldPath": "data", "columnName": "data", "affinity": "TEXT", "notNull": true}, {"fieldPath": "cacheTime", "columnName": "cache_time", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "expirationTime", "columnName": "expiration_time", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "cacheType", "columnName": "cache_type", "affinity": "TEXT", "notNull": true}], "primaryKey": {"autoGenerate": false, "columnNames": ["cache_key"]}, "indices": [], "foreignKeys": []}], "views": [], "setupQueries": ["CREATE TABLE IF NOT EXISTS room_master_table (id INTEGER PRIMARY KEY,identity_hash TEXT)", "INSERT OR REPLACE INTO room_master_table (id,identity_hash) VALUES(42, 'e090f777145fb91f3b50ad2734377926')"]}}