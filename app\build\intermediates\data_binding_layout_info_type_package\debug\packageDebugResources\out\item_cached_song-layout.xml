<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="item_cached_song" modulePackage="com.example.aimusicplayer" filePath="app\src\main\res\layout\item_cached_song.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="com.google.android.material.card.MaterialCardView"><Targets><Target tag="layout/item_cached_song_0" view="com.google.android.material.card.MaterialCardView"><Expressions/><location startLine="1" startOffset="0" endLine="107" endOffset="51"/></Target><Target id="@+id/checkboxSelect" view="CheckBox"><Expressions/><location startLine="21" startOffset="8" endLine="27" endOffset="39"/></Target><Target id="@+id/textSongName" view="TextView"><Expressions/><location startLine="37" startOffset="12" endLine="46" endOffset="35"/></Target><Target id="@+id/textFileSize" view="TextView"><Expressions/><location startLine="55" startOffset="16" endLine="61" endOffset="40"/></Target><Target id="@+id/textCacheTime" view="TextView"><Expressions/><location startLine="71" startOffset="16" endLine="77" endOffset="39"/></Target><Target id="@+id/textLastAccess" view="TextView"><Expressions/><location startLine="82" startOffset="12" endLine="89" endOffset="41"/></Target><Target id="@+id/buttonDelete" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="94" startOffset="8" endLine="103" endOffset="61"/></Target></Targets></Layout>