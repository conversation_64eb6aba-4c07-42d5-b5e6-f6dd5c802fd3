#!/usr/bin/env node

/**
 * 游客登录功能测试脚本
 * 测试修复后的游客登录功能是否正常工作
 */

const https = require('https');

// 服务器配置
const SERVERS = {
    primary: 'zm.armoe.cn',
    backup: 'ncm.zhenxin.me'
};

// 测试游客登录接口
function testGuestLogin(serverName, hostname) {
    return new Promise((resolve) => {
        const startTime = Date.now();
        
        const options = {
            hostname: hostname,
            path: '/register/anonimous',
            method: 'GET',
            timeout: 10000,
            headers: {
                'User-Agent': 'Guest-Login-Test/1.0',
                'Accept': 'application/json'
            }
        };

        console.log(`[${new Date().toISOString()}] 测试 ${serverName} (${hostname}) 游客登录...`);

        const req = https.request(options, (res) => {
            let data = '';
            res.on('data', (chunk) => {
                data += chunk;
            });

            res.on('end', () => {
                const responseTime = Date.now() - startTime;
                let result = {
                    server: serverName,
                    hostname,
                    success: false,
                    responseTime,
                    statusCode: res.statusCode,
                    error: null,
                    data: null
                };

                try {
                    const json = JSON.parse(data);
                    result.data = json;
                    result.success = res.statusCode === 200;

                    if (result.success) {
                        const code = json.code;
                        if (code === 200) {
                            console.log(`[${new Date().toISOString()}] ✅ ${serverName} 游客登录成功 (${responseTime}ms)`);
                            console.log(`[${new Date().toISOString()}] 响应数据: ${JSON.stringify(json).substring(0, 200)}...`);
                        } else {
                            console.log(`[${new Date().toISOString()}] ⚠️  ${serverName} 游客登录返回错误码: ${code}`);
                            result.error = `错误码: ${code}, 消息: ${json.msg || json.message || '未知错误'}`;
                        }
                    } else {
                        result.error = `HTTP状态码错误: ${res.statusCode}`;
                        console.log(`[${new Date().toISOString()}] ❌ ${serverName} HTTP错误: ${res.statusCode}`);
                    }

                } catch (e) {
                    result.error = `JSON解析失败: ${e.message}`;
                    console.log(`[${new Date().toISOString()}] ❌ ${serverName} JSON解析失败: ${e.message}`);
                    console.log(`[${new Date().toISOString()}] 原始响应: ${data.substring(0, 200)}...`);
                }

                resolve(result);
            });
        });

        req.on('error', (e) => {
            const responseTime = Date.now() - startTime;
            console.log(`[${new Date().toISOString()}] ❌ ${serverName} 请求错误: ${e.message}`);
            resolve({
                server: serverName,
                hostname,
                success: false,
                responseTime,
                error: `请求失败: ${e.message}`,
                data: null
            });
        });

        req.on('timeout', () => {
            req.destroy();
            const responseTime = Date.now() - startTime;
            console.log(`[${new Date().toISOString()}] ❌ ${serverName} 请求超时`);
            resolve({
                server: serverName,
                hostname,
                success: false,
                responseTime,
                error: '请求超时',
                data: null
            });
        });

        req.end();
    });
}

// 主测试函数
async function main() {
    console.log(`[${new Date().toISOString()}] 🚀 开始游客登录功能测试`);
    console.log(`[${new Date().toISOString()}] 主服务器: ${SERVERS.primary}`);
    console.log(`[${new Date().toISOString()}] 备用服务器: ${SERVERS.backup}`);
    console.log('');

    const results = [];

    // 测试主服务器
    const primaryResult = await testGuestLogin('主服务器', SERVERS.primary);
    results.push(primaryResult);

    console.log('');

    // 测试备用服务器
    const backupResult = await testGuestLogin('备用服务器', SERVERS.backup);
    results.push(backupResult);

    console.log('');
    console.log(`[${new Date().toISOString()}] 📊 测试结果汇总:`);
    console.log('='.repeat(50));

    let successCount = 0;
    results.forEach(result => {
        const status = result.success ? '✅ 成功' : '❌ 失败';
        console.log(`${result.server}: ${status} (${result.responseTime}ms)`);
        if (result.error) {
            console.log(`  错误: ${result.error}`);
        }
        if (result.success) {
            successCount++;
        }
    });

    console.log('');
    console.log(`[${new Date().toISOString()}] 总结:`);
    console.log(`- 测试服务器数: ${results.length}`);
    console.log(`- 成功服务器数: ${successCount}`);
    console.log(`- 成功率: ${(successCount / results.length * 100).toFixed(1)}%`);

    if (successCount > 0) {
        console.log(`[${new Date().toISOString()}] 🎉 游客登录功能修复成功！`);
        process.exit(0);
    } else {
        console.log(`[${new Date().toISOString()}] ⚠️  游客登录功能仍需进一步修复`);
        process.exit(1);
    }
}

// 运行测试
main().catch(error => {
    console.error(`[${new Date().toISOString()}] 💥 测试脚本执行失败:`, error);
    process.exit(1);
});
