<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="page_player_comment" modulePackage="com.example.aimusicplayer" filePath="app\src\main\res\layout\page_player_comment.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.LinearLayout"><Targets><Target tag="layout/page_player_comment_0" view="LinearLayout"><Expressions/><location startLine="1" startOffset="0" endLine="100" endOffset="14"/></Target><Target id="@+id/swipe_refresh_layout" view="androidx.swiperefreshlayout.widget.SwipeRefreshLayout"><Expressions/><location startLine="10" startOffset="4" endLine="55" endOffset="59"/></Target><Target id="@+id/recycler_view_comments" view="androidx.recyclerview.widget.RecyclerView"><Expressions/><location startLine="21" startOffset="12" endLine="28" endOffset="55"/></Target><Target id="@+id/text_empty_comment" view="TextView"><Expressions/><location startLine="31" startOffset="12" endLine="43" endOffset="42"/></Target><Target id="@+id/progress_bar" view="ProgressBar"><Expressions/><location startLine="46" startOffset="12" endLine="52" endOffset="64"/></Target><Target id="@+id/edit_text_comment" view="EditText"><Expressions/><location startLine="67" startOffset="8" endLine="83" endOffset="38"/></Target><Target id="@+id/btn_send_comment" view="ImageButton"><Expressions/><location startLine="86" startOffset="8" endLine="96" endOffset="37"/></Target></Targets></Layout>