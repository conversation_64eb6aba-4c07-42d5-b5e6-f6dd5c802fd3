<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="page_player_comment" modulePackage="com.example.aimusicplayer" filePath="app\src\main\res\layout\page_player_comment.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.LinearLayout"><Targets><Target tag="layout/page_player_comment_0" view="LinearLayout"><Expressions/><location startLine="1" startOffset="0" endLine="135" endOffset="14"/></Target><Target id="@+id/btn_back_to_lyric" view="ImageButton"><Expressions/><location startLine="18" startOffset="8" endLine="28" endOffset="42"/></Target><Target id="@+id/swipe_refresh_layout" view="androidx.swiperefreshlayout.widget.SwipeRefreshLayout"><Expressions/><location startLine="45" startOffset="4" endLine="90" endOffset="59"/></Target><Target id="@+id/recycler_view_comments" view="androidx.recyclerview.widget.RecyclerView"><Expressions/><location startLine="56" startOffset="12" endLine="63" endOffset="55"/></Target><Target id="@+id/text_empty_comment" view="TextView"><Expressions/><location startLine="66" startOffset="12" endLine="78" endOffset="42"/></Target><Target id="@+id/progress_bar" view="ProgressBar"><Expressions/><location startLine="81" startOffset="12" endLine="87" endOffset="64"/></Target><Target id="@+id/edit_text_comment" view="EditText"><Expressions/><location startLine="102" startOffset="8" endLine="118" endOffset="38"/></Target><Target id="@+id/btn_send_comment" view="ImageButton"><Expressions/><location startLine="121" startOffset="8" endLine="131" endOffset="37"/></Target></Targets></Layout>