package com.example.aimusicplayer.viewmodel

import android.util.Log
import androidx.lifecycle.ViewModel
import androidx.lifecycle.asLiveData
import androidx.lifecycle.viewModelScope
import com.example.aimusicplayer.data.repository.MusicRepository
import com.example.aimusicplayer.utils.toUnMutable
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * 主界面ViewModel
 * 负责主界面的数据管理和业务逻辑
 */
@HiltViewModel
class MainViewModel @Inject constructor(
    private val musicRepository: MusicRepository,
) : ViewModel() {

    companion object {
        private const val TAG = "MainViewModel"
    }

    // 加载状态
    private val _isLoading = MutableStateFlow(false)
    val isLoading = _isLoading.toUnMutable()

    // 错误信息
    private val _errorMessage = MutableStateFlow<String?>(null)
    val errorMessage = _errorMessage.toUnMutable()

    // 当前选中的导航项
    private val _selectedNavItem = MutableStateFlow(0)
    val selectedNavItem = _selectedNavItem.toUnMutable()

    // 抽屉状态
    private val _isDrawerOpen = MutableStateFlow(false)
    val isDrawerOpen = _isDrawerOpen.toUnMutable()

    /**
     * 设置选中的导航项
     */
    fun setSelectedNavItem(index: Int) {
        _selectedNavItem.value = index
    }

    /**
     * 初始化主界面数据
     */
    fun initializeMainData() {
        viewModelScope.launch {
            try {
                _isLoading.value = true
                // TODO: 加载主界面需要的数据
                Log.d(TAG, "主界面数据初始化完成")
            } catch (e: Exception) {
                Log.e(TAG, "主界面数据初始化失败", e)
                _errorMessage.value = "初始化失败: ${e.message}"
            } finally {
                _isLoading.value = false
            }
        }
    }

    /**
     * 清除错误信息
     */
    fun clearError() {
        _errorMessage.value = null
    }

    // Java兼容方法

    /**
     * 获取抽屉状态 (兼容Java代码)
     */
    fun getDrawerOpenState() = isDrawerOpen.asLiveData()

    /**
     * 获取选中的导航项 (兼容Java代码)
     */
    fun getSelectedNavItemState() = selectedNavItem.asLiveData()

    /**
     * 切换抽屉状态
     */
    fun toggleDrawer() {
        _isDrawerOpen.value = !_isDrawerOpen.value
    }

    /**
     * 初始化侧边栏控制器
     */
    fun initializeSidebarController(
        @Suppress("UNUSED_PARAMETER") sidebarNav: android.widget.LinearLayout,
        @Suppress("UNUSED_PARAMETER") btnMenuRight: android.widget.ImageView,
        @Suppress("UNUSED_PARAMETER") fragmentContainer: android.widget.FrameLayout,
        @Suppress("UNUSED_PARAMETER") navButtons: Array<android.view.View>,
    ) {
        // TODO: 实现侧边栏控制器初始化逻辑
        Log.d(TAG, "初始化侧边栏控制器")
    }

    /**
     * 安排侧边栏自动隐藏
     */
    fun scheduleSidebarAutoHide() {
        // TODO: 实现侧边栏自动隐藏逻辑
        Log.d(TAG, "安排侧边栏自动隐藏")
    }
}
