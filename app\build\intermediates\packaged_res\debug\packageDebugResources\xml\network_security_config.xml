<?xml version="1.0" encoding="utf-8"?>
<network-security-config>
    <!-- Android Automotive环境网络安全配置 -->
    <base-config cleartextTrafficPermitted="false">
        <trust-anchors>
            <certificates src="system" />
            <!-- 仅在调试模式下信任用户证书 -->
            <certificates src="user" />
        </trust-anchors>
    </base-config>

    <!-- 调试环境配置 -->
    <debug-overrides>
        <trust-anchors>
            <certificates src="user" />
            <certificates src="system" />
        </trust-anchors>
    </debug-overrides>

    <!-- 特定API域名配置 - 已调整主备服务器 -->
    <domain-config cleartextTrafficPermitted="true">
        <domain includeSubdomains="true">ncm.zhenxin.me</domain>
        <domain includeSubdomains="true">1355831898-4499wupl9z.ap-guangzhou.tencentscf.com</domain>
        <domain includeSubdomains="true">zm.armoe.cn</domain>
        <domain includeSubdomains="true">music.163.com</domain>
        <domain includeSubdomains="true">p1.music.126.net</domain>
        <domain includeSubdomains="true">p2.music.126.net</domain>
        <trust-anchors>
            <certificates src="system" />
            <certificates src="user" />
        </trust-anchors>
        <!-- 证书固定（可选，增强安全性） -->
        <pin-set expiration="2025-12-31">
            <!-- 这里可以添加证书指纹，增强安全性 -->
        </pin-set>
    </domain-config>
</network-security-config>