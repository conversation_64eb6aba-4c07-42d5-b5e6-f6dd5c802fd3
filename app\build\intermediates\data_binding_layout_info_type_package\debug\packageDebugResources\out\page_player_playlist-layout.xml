<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="page_player_playlist" modulePackage="com.example.aimusicplayer" filePath="app\src\main\res\layout\page_player_playlist.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.LinearLayout"><Targets><Target tag="layout/page_player_playlist_0" view="LinearLayout"><Expressions/><location startLine="1" startOffset="0" endLine="186" endOffset="14"/></Target><Target id="@+id/btn_back_to_lyric" view="ImageButton"><Expressions/><location startLine="18" startOffset="8" endLine="28" endOffset="42"/></Target><Target id="@+id/text_song_count" view="TextView"><Expressions/><location startLine="43" startOffset="8" endLine="49" endOffset="37"/></Target><Target id="@+id/layout_play_mode" view="LinearLayout"><Expressions/><location startLine="62" startOffset="8" endLine="90" endOffset="22"/></Target><Target id="@+id/image_play_mode" view="ImageView"><Expressions/><location startLine="74" startOffset="12" endLine="79" endOffset="46"/></Target><Target id="@+id/text_play_mode" view="TextView"><Expressions/><location startLine="81" startOffset="12" endLine="88" endOffset="41"/></Target><Target id="@+id/btn_shuffle_playlist" view="ImageButton"><Expressions/><location startLine="93" startOffset="8" endLine="105" endOffset="42"/></Target><Target id="@+id/btn_clear_playlist" view="ImageButton"><Expressions/><location startLine="108" startOffset="8" endLine="118" endOffset="42"/></Target><Target id="@+id/recycler_view_playlist" view="androidx.recyclerview.widget.RecyclerView"><Expressions/><location startLine="137" startOffset="8" endLine="147" endOffset="54"/></Target><Target id="@+id/layout_empty_state" view="LinearLayout"><Expressions/><location startLine="150" startOffset="8" endLine="182" endOffset="22"/></Target></Targets></Layout>