package com.example.aimusicplayer.ui.dialog

import android.app.Dialog
import android.os.Build
import android.os.Bundle
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.WindowManager
import androidx.core.view.WindowCompat
import androidx.core.view.WindowInsetsCompat
import androidx.core.view.WindowInsetsControllerCompat
import androidx.fragment.app.DialogFragment
import androidx.fragment.app.activityViewModels
import androidx.lifecycle.lifecycleScope
import androidx.media3.common.MediaItem
import androidx.recyclerview.widget.LinearLayoutManager
import com.example.aimusicplayer.R
import com.example.aimusicplayer.databinding.DialogPlayQueueBinding
import com.example.aimusicplayer.ui.adapter.PlayQueueAdapter
import com.example.aimusicplayer.viewmodel.PlayerViewModel
import com.google.android.material.dialog.MaterialAlertDialogBuilder
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.launch

/**
 * 播放队列对话框
 * 适配Android Automotive车载环境
 */
@AndroidEntryPoint
class PlayQueueDialogFragment : DialogFragment() {

    companion object {
        private const val TAG = "PlayQueueDialogFragment"

        fun newInstance(): PlayQueueDialogFragment {
            return PlayQueueDialogFragment()
        }
    }

    private var _binding: DialogPlayQueueBinding? = null
    private val binding get() = _binding!!

    private val playerViewModel: PlayerViewModel by activityViewModels()
    private lateinit var playQueueAdapter: PlayQueueAdapter

    override fun onCreateDialog(savedInstanceState: Bundle?): Dialog {
        return MaterialAlertDialogBuilder(requireContext(), R.style.FullScreenDialogTheme)
            .create()
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?,
    ): View {
        _binding = DialogPlayQueueBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        setupDialog()
        setupRecyclerView()
        setupClickListeners()
        observeViewModel()

        Log.d(TAG, "播放队列对话框已创建")
    }

    override fun onStart() {
        super.onStart()

        // 设置对话框为全屏，适配车载环境
        dialog?.window?.let { window ->
            val layoutParams = window.attributes
            layoutParams.width = WindowManager.LayoutParams.MATCH_PARENT
            layoutParams.height = WindowManager.LayoutParams.MATCH_PARENT
            window.attributes = layoutParams

            // 设置车载适配的窗口标志
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
                // Android 11+ 使用新的WindowInsetsController
                WindowCompat.setDecorFitsSystemWindows(window, false)
                val controller = WindowInsetsControllerCompat(window, window.decorView)
                controller.hide(WindowInsetsCompat.Type.systemBars())
                controller.systemBarsBehavior = WindowInsetsControllerCompat.BEHAVIOR_SHOW_TRANSIENT_BARS_BY_SWIPE
            } else {
                // Android 10及以下使用传统方式
                @Suppress("DEPRECATION")
                window.addFlags(WindowManager.LayoutParams.FLAG_FULLSCREEN)
            }
            window.clearFlags(WindowManager.LayoutParams.FLAG_DIM_BEHIND)
        }
    }

    private fun setupDialog() {
        // 设置对话框样式
        dialog?.setCanceledOnTouchOutside(true)
        dialog?.setCancelable(true)
    }

    private fun setupRecyclerView() {
        playQueueAdapter = PlayQueueAdapter(
            onItemClick = { position, mediaItem ->
                // 点击歌曲项，跳转播放
                Log.d(TAG, "点击播放队列项: position=$position, title=${mediaItem.mediaMetadata.title}")
                playerViewModel.seekToQueueItem(position)
                dismiss()
            },
            onItemLongClick = { position, mediaItem ->
                // 长按显示更多选项
                showItemOptions(position, mediaItem)
            },
        )

        binding.rvPlayQueue.apply {
            layoutManager = LinearLayoutManager(requireContext())
            adapter = playQueueAdapter

            // 车载适配：优化滚动性能
            setHasFixedSize(true)
            setItemViewCacheSize(20)
        }
    }

    private fun setupClickListeners() {
        // 关闭按钮
        binding.ivClose.setOnClickListener {
            dismiss()
        }

        // 播放模式切换
        binding.llPlayMode.setOnClickListener {
            playerViewModel.toggleRepeatMode()
        }

        // 随机播放
        binding.btnShuffle.setOnClickListener {
            playerViewModel.shuffleQueue()
            showToast("已开启随机播放")
        }

        // 清空列表
        binding.btnClear.setOnClickListener {
            showClearConfirmDialog()
        }
    }

    private fun observeViewModel() {
        // 观察播放队列
        lifecycleScope.launch {
            playerViewModel.playQueueFlow.collect { queue ->
                Log.d(TAG, "播放队列更新: ${queue.size}首歌曲")
                updatePlayQueue(queue)
            }
        }

        // 观察当前播放索引
        lifecycleScope.launch {
            playerViewModel.currentMediaItemIndex.collect { index ->
                Log.d(TAG, "当前播放索引: $index")
                playQueueAdapter.setCurrentPlayingIndex(index)

                // 滚动到当前播放的歌曲
                if (index >= 0) {
                    binding.rvPlayQueue.scrollToPosition(index)
                }
            }
        }

        // 观察播放模式
        lifecycleScope.launch {
            playerViewModel.repeatMode.collect { mode ->
                updatePlayModeUI(mode)
            }
        }
    }

    private fun updatePlayQueue(queue: List<MediaItem>) {
        playQueueAdapter.submitList(queue)

        // 更新歌曲数量
        binding.tvSongCount.text = "(${queue.size}首)"

        // 显示/隐藏空状态
        if (queue.isEmpty()) {
            binding.rvPlayQueue.visibility = View.GONE
            binding.llEmptyState.visibility = View.VISIBLE
        } else {
            binding.rvPlayQueue.visibility = View.VISIBLE
            binding.llEmptyState.visibility = View.GONE
        }
    }

    private fun updatePlayModeUI(repeatMode: Int) {
        when (repeatMode) {
            0 -> { // REPEAT_MODE_OFF
                binding.ivPlayMode.setImageResource(R.drawable.ic_repeat_off)
                binding.tvPlayMode.text = "顺序播放"
            }
            1 -> { // REPEAT_MODE_ONE
                binding.ivPlayMode.setImageResource(R.drawable.ic_repeat_one)
                binding.tvPlayMode.text = "单曲循环"
            }
            2 -> { // REPEAT_MODE_ALL
                binding.ivPlayMode.setImageResource(R.drawable.ic_repeat)
                binding.tvPlayMode.text = "列表循环"
            }
        }
    }

    private fun showItemOptions(position: Int, mediaItem: MediaItem) {
        val options = arrayOf("从队列中移除", "查看歌曲信息")

        MaterialAlertDialogBuilder(requireContext())
            .setTitle(mediaItem.mediaMetadata.title)
            .setItems(options) { _, which ->
                when (which) {
                    0 -> removeFromQueue(position)
                    1 -> showSongInfo(mediaItem)
                }
            }
            .show()
    }

    private fun removeFromQueue(position: Int) {
        playerViewModel.removeFromQueue(position)
        showToast("已从播放队列移除")
    }

    private fun showSongInfo(mediaItem: MediaItem) {
        val title = mediaItem.mediaMetadata.title ?: "未知歌曲"
        val artist = mediaItem.mediaMetadata.artist ?: "未知艺术家"
        val duration = mediaItem.mediaMetadata.extras?.getLong("duration") ?: 0L

        val message = """
            歌曲：$title
            艺术家：$artist
            时长：${formatDuration(duration)}
            ID：${mediaItem.mediaId}
        """.trimIndent()

        MaterialAlertDialogBuilder(requireContext())
            .setTitle("歌曲信息")
            .setMessage(message)
            .setPositiveButton("确定", null)
            .show()
    }

    private fun showClearConfirmDialog() {
        MaterialAlertDialogBuilder(requireContext())
            .setTitle("清空播放列表")
            .setMessage("确定要清空当前播放列表吗？")
            .setPositiveButton("确定") { _, _ ->
                playerViewModel.clearQueue()
                dismiss()
            }
            .setNegativeButton("取消", null)
            .show()
    }

    private fun showToast(message: String) {
        // 这里可以使用Snackbar或Toast显示消息
        Log.d(TAG, "Toast: $message")
    }

    private fun formatDuration(duration: Long): String {
        val minutes = duration / 1000 / 60
        val seconds = (duration / 1000) % 60
        return String.format("%02d:%02d", minutes, seconds)
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }
}
