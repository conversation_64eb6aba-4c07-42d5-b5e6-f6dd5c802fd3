package com.example.aimusicplayer.data.model

/**
 * 搜索项密封类
 * 用于区分搜索历史和搜索建议
 */
sealed class SearchItem {
    abstract val text: String
    abstract val id: String

    /**
     * 搜索历史项
     * @param keyword 搜索关键词
     * @param timestamp 时间戳
     */
    data class HistoryItem(
        override val text: String,
        val timestamp: Long,
    ) : SearchItem() {
        override val id: String = "history_$text"
    }

    /**
     * 搜索建议项
     * @param suggestion 建议内容
     * @param source 来源（API建议）
     */
    data class SuggestionItem(
        override val text: String,
        val source: String = "api",
    ) : SearchItem() {
        override val id: String = "suggestion_$text"
    }
}

/**
 * 搜索项类型枚举
 */
enum class SearchItemType {
    HISTORY, // 历史记录
    SUGGESTION, // API建议
}
