package com.example.aimusicplayer.data.model

import com.google.gson.annotations.SerializedName

/**
 * 歌单详情API响应模型
 * 严格对应网易云音乐API /playlist/detail 接口
 * 参考：ponymusic-master项目标准
 */
data class PlaylistDetailResponse(
    val playlist: PlaylistDetailData? = null,
    val privileges: List<PrivilegeData>? = null,
    val fromUsers: Any? = null,
    val fromUserCount: Int = 0,
    val songFromUsers: Any? = null,
) : BaseResponse()

/**
 * 歌单详情数据
 * 对应API返回的playlist对象
 */
data class PlaylistDetailData(
    @SerializedName("id")
    val id: Long = 0,

    @SerializedName("name")
    val name: String = "",

    @SerializedName("coverImgId")
    val coverImgId: Long = 0,

    @SerializedName("coverImgUrl")
    val coverImgUrl: String = "",

    @SerializedName("coverImgId_str")
    val coverImgIdStr: String? = null,

    @SerializedName("adType")
    val adType: Int = 0,

    @SerializedName("userId")
    val userId: Long = 0,

    @SerializedName("createTime")
    val createTime: Long = 0,

    @SerializedName("status")
    val status: Int = 0,

    @SerializedName("opRecommend")
    val opRecommend: Boolean = false,

    @SerializedName("highQuality")
    val highQuality: Boolean = false,

    @SerializedName("newImported")
    val newImported: Boolean = false,

    @SerializedName("updateTime")
    val updateTime: Long = 0,

    @SerializedName("trackCount")
    val trackCount: Int = 0,

    @SerializedName("specialType")
    val specialType: Int = 0,

    @SerializedName("privacy")
    val privacy: Int = 0,

    @SerializedName("trackUpdateTime")
    val trackUpdateTime: Long = 0,

    @SerializedName("commentThreadId")
    val commentThreadId: String? = null,

    @SerializedName("playCount")
    val playCount: Long = 0,

    @SerializedName("trackNumberUpdateTime")
    val trackNumberUpdateTime: Long = 0,

    @SerializedName("subscribedCount")
    val subscribedCount: Long = 0,

    @SerializedName("cloudTrackCount")
    val cloudTrackCount: Int = 0,

    @SerializedName("ordered")
    val ordered: Boolean = true,

    @SerializedName("description")
    val description: String? = null,

    @SerializedName("tags")
    val tags: List<String>? = null,

    @SerializedName("updateFrequency")
    val updateFrequency: Any? = null,

    @SerializedName("backgroundCoverId")
    val backgroundCoverId: Long = 0,

    @SerializedName("backgroundCoverUrl")
    val backgroundCoverUrl: String? = null,

    @SerializedName("titleImage")
    val titleImage: Long = 0,

    @SerializedName("titleImageUrl")
    val titleImageUrl: String? = null,

    @SerializedName("englishTitle")
    val englishTitle: String? = null,

    @SerializedName("officialPlaylistType")
    val officialPlaylistType: Any? = null,

    @SerializedName("copied")
    val copied: Boolean = false,

    @SerializedName("relateResType")
    val relateResType: Any? = null,

    @SerializedName("subscribers")
    val subscribers: List<User>? = null,

    @SerializedName("subscribed")
    val subscribed: Boolean = false,

    @SerializedName("creator")
    val creator: User? = null,

    @SerializedName("tracks")
    val tracks: List<Song>? = null,

    @SerializedName("videoIds")
    val videoIds: Any? = null,

    @SerializedName("videos")
    val videos: Any? = null,

    @SerializedName("trackIds")
    val trackIds: List<TrackIdData>? = null,

    @SerializedName("bannedTrackIds")
    val bannedTrackIds: Any? = null,

    @SerializedName("mvResourceInfos")
    val mvResourceInfos: Any? = null,

    @SerializedName("shareCount")
    val shareCount: Long = 0,

    @SerializedName("commentCount")
    val commentCount: Long = 0,

    @SerializedName("remixVideo")
    val remixVideo: Any? = null,

    @SerializedName("sharedUsers")
    val sharedUsers: Any? = null,

    @SerializedName("historySharedUsers")
    val historySharedUsers: Any? = null,

    @SerializedName("gradeStatus")
    val gradeStatus: String? = null,

    @SerializedName("score")
    val score: Any? = null,

    @SerializedName("algTags")
    val algTags: Any? = null,

    @SerializedName("trialMode")
    val trialMode: Int = 0,

    @SerializedName("displayTags")
    val displayTags: Any? = null,

    @SerializedName("platFormAlgTags")
    val platFormAlgTags: Any? = null,

    @SerializedName("relatedVideos")
    val relatedVideos: Any? = null,

    @SerializedName("appLinks")
    val appLinks: Any? = null,

    @SerializedName("ToplistType")
    val toplistType: String? = null,
) {
    /**
     * 获取歌单封面URL（高清）
     */
    fun getHighQualityCoverUrl(): String {
        return if (coverImgUrl.isNotEmpty()) {
            "$coverImgUrl?param=400y400"
        } else {
            ""
        }
    }

    /**
     * 获取播放次数描述
     */
    fun getPlayCountDescription(): String {
        return when {
            playCount >= 100000000 -> String.format("%.1f亿", playCount / 100000000.0)
            playCount >= 10000 -> String.format("%.1f万", playCount / 10000.0)
            else -> playCount.toString()
        }
    }

    /**
     * 获取收藏次数描述
     */
    fun getSubscribedCountDescription(): String {
        return when {
            subscribedCount >= 100000000 -> String.format("%.1f亿", subscribedCount / 100000000.0)
            subscribedCount >= 10000 -> String.format("%.1f万", subscribedCount / 10000.0)
            else -> subscribedCount.toString()
        }
    }

    /**
     * 检查是否为官方歌单
     */
    fun isOfficialPlaylist(): Boolean {
        return creator?.userId == "1" || creator?.username?.contains("网易云音乐") == true
    }
}

/**
 * 歌曲权限数据
 */
data class PrivilegeData(
    @SerializedName("id")
    val id: Long = 0,

    @SerializedName("fee")
    val fee: Int = 0,

    @SerializedName("payed")
    val payed: Int = 0,

    @SerializedName("realPayed")
    val realPayed: Int = 0,

    @SerializedName("st")
    val st: Int = 0,

    @SerializedName("pl")
    val pl: Int = 0,

    @SerializedName("dl")
    val dl: Int = 0,

    @SerializedName("sp")
    val sp: Int = 0,

    @SerializedName("cp")
    val cp: Int = 0,

    @SerializedName("subp")
    val subp: Int = 0,

    @SerializedName("cs")
    val cs: Boolean = false,

    @SerializedName("maxbr")
    val maxbr: Int = 0,

    @SerializedName("fl")
    val fl: Int = 0,

    @SerializedName("pc")
    val pc: Any? = null,

    @SerializedName("toast")
    val toast: Boolean = false,

    @SerializedName("flag")
    val flag: Int = 0,

    @SerializedName("paidBigBang")
    val paidBigBang: Boolean = false,

    @SerializedName("preSell")
    val preSell: Boolean = false,

    @SerializedName("playMaxbr")
    val playMaxbr: Int = 0,

    @SerializedName("downloadMaxbr")
    val downloadMaxbr: Int = 0,

    @SerializedName("maxBrLevel")
    val maxBrLevel: String? = null,

    @SerializedName("playMaxBrLevel")
    val playMaxBrLevel: String? = null,

    @SerializedName("downloadMaxBrLevel")
    val downloadMaxBrLevel: String? = null,

    @SerializedName("plLevel")
    val plLevel: String? = null,

    @SerializedName("dlLevel")
    val dlLevel: String? = null,

    @SerializedName("flLevel")
    val flLevel: String? = null,

    @SerializedName("rscl")
    val rscl: Any? = null,

    @SerializedName("freeTrialPrivilege")
    val freeTrialPrivilege: FreeTrialPrivilege? = null,

    @SerializedName("rightSource")
    val rightSource: Int = 0,

    @SerializedName("chargeInfoList")
    val chargeInfoList: List<ChargeInfo>? = null,
) {
    /**
     * 检查歌曲是否可播放
     */
    fun isPlayable(): Boolean {
        return st == 0 && pl > 0
    }

    /**
     * 检查歌曲是否可下载
     */
    fun isDownloadable(): Boolean {
        return dl > 0
    }
}

/**
 * 收费信息
 */
data class ChargeInfo(
    @SerializedName("rate")
    val rate: Int = 0,

    @SerializedName("chargeUrl")
    val chargeUrl: String? = null,

    @SerializedName("chargeMessage")
    val chargeMessage: String? = null,

    @SerializedName("chargeType")
    val chargeType: Int = 0,
)

/**
 * 歌曲ID数据
 */
data class TrackIdData(
    @SerializedName("id")
    val id: Long = 0,

    @SerializedName("v")
    val v: Int = 0,

    @SerializedName("t")
    val t: Int = 0,

    @SerializedName("at")
    val at: Long = 0,

    @SerializedName("alg")
    val alg: String? = null,

    @SerializedName("uid")
    val uid: Long = 0,

    @SerializedName("rcmdReason")
    val rcmdReason: String? = null,

    @SerializedName("sc")
    val sc: Any? = null,

    @SerializedName("f")
    val f: Any? = null,

    @SerializedName("sr")
    val sr: Any? = null,
)

/**
 * 数据转换扩展方法
 * 将歌单详情数据转换为应用内部的PlayList模型
 */
fun PlaylistDetailData.toPlayList(): PlayList {
    return PlayList(
        id = this.id.toString(),
        name = this.name,
        coverImgUrl = this.getHighQualityCoverUrl(),
        playCount = this.playCount.toInt(),
        songCount = this.trackCount,
        description = this.description ?: "",
        creatorName = this.creator?.username ?: "",
        subscribed = this.subscribed,
    )
}

/**
 * PlaylistDetailResponse扩展方法 - 获取歌单信息
 */
fun PlaylistDetailResponse.getPlaylist(): PlayList? {
    return playlist?.toPlayList()
}

/**
 * 获取歌单中的歌曲列表
 */
fun PlaylistDetailResponse.getSongs(): List<Song> {
    return playlist?.tracks ?: emptyList()
}
