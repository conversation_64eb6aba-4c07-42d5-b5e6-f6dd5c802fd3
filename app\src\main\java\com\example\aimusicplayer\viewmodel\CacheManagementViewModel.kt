package com.example.aimusicplayer.viewmodel

import android.util.Log
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.example.aimusicplayer.data.cache.MusicFileCache
import com.example.aimusicplayer.utils.toUnMutable
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * 缓存管理ViewModel
 * 负责缓存管理界面的业务逻辑
 */
@HiltViewModel
class CacheManagementViewModel @Inject constructor(
    private val musicFileCache: MusicFileCache,
) : ViewModel() {

    companion object {
        private const val TAG = "CacheManagementViewModel"
    }

    // 缓存统计信息
    val cacheStats = musicFileCache.cacheStats

    // 已缓存的歌曲列表 - 使用懒加载初始化
    private val _cachedSongs by lazy { MutableStateFlow<List<MusicFileCache.CacheInfo>>(emptyList()) }
    val cachedSongs by lazy { _cachedSongs.toUnMutable() }

    // 缓存设置 - 使用懒加载初始化
    private val _cacheSettings by lazy { MutableStateFlow(CacheSettings()) }
    val cacheSettings by lazy { _cacheSettings.toUnMutable() }

    // 操作状态 - 使用懒加载初始化
    private val _isLoading by lazy { MutableStateFlow(false) }
    val isLoading by lazy { _isLoading.toUnMutable() }

    private val _operationMessage by lazy { MutableStateFlow<String?>(null) }
    val operationMessage by lazy { _operationMessage.toUnMutable() }

    /**
     * 缓存设置数据类
     */
    data class CacheSettings(
        val maxCacheSize: Long = 500L * 1024 * 1024, // 500MB
        val wifiOnlyCache: Boolean = true,
        val autoCacheEnabled: Boolean = true,
    )

    init {
        // 延迟初始化，确保StateFlow安全
        viewModelScope.launch {
            delay(50) // 短暂延迟确保初始化完成
            loadCacheSettings()
            loadCachedSongs()
        }
    }

    /**
     * 加载缓存设置
     */
    private fun loadCacheSettings() {
        val (maxSize, wifiOnly) = musicFileCache.getCacheConfig()
        _cacheSettings.value = _cacheSettings.value.copy(
            maxCacheSize = maxSize,
            wifiOnlyCache = wifiOnly,
        )
    }

    /**
     * 加载已缓存的歌曲列表
     */
    fun loadCachedSongs() {
        viewModelScope.launch {
            try {
                _isLoading.value = true
                val songs = musicFileCache.getCachedSongs()
                _cachedSongs.value = songs
                Log.d(TAG, "加载缓存歌曲列表成功，共${songs.size}首")
            } catch (e: Exception) {
                Log.e(TAG, "加载缓存歌曲列表失败", e)
                showMessage("加载缓存列表失败: ${e.message}")
            } finally {
                _isLoading.value = false
            }
        }
    }

    /**
     * 删除指定歌曲的缓存
     */
    fun deleteCachedSong(songId: Long) {
        viewModelScope.launch {
            try {
                _isLoading.value = true
                val success = musicFileCache.deleteCachedSong(songId)
                if (success) {
                    showMessage("删除缓存成功")
                    loadCachedSongs() // 刷新列表
                } else {
                    showMessage("删除缓存失败")
                }
            } catch (e: Exception) {
                Log.e(TAG, "删除缓存失败", e)
                showMessage("删除缓存失败: ${e.message}")
            } finally {
                _isLoading.value = false
            }
        }
    }

    /**
     * 批量删除缓存
     */
    fun deleteCachedSongs(songIds: List<Long>) {
        viewModelScope.launch {
            try {
                _isLoading.value = true
                var successCount = 0
                var failCount = 0

                for (songId in songIds) {
                    val success = musicFileCache.deleteCachedSong(songId)
                    if (success) {
                        successCount++
                    } else {
                        failCount++
                    }
                }

                val message = if (failCount == 0) {
                    "成功删除${successCount}个缓存"
                } else {
                    "删除完成：成功${successCount}个，失败${failCount}个"
                }
                showMessage(message)
                loadCachedSongs() // 刷新列表
            } catch (e: Exception) {
                Log.e(TAG, "批量删除缓存失败", e)
                showMessage("批量删除失败: ${e.message}")
            } finally {
                _isLoading.value = false
            }
        }
    }

    /**
     * 清空所有缓存
     */
    fun clearAllCache() {
        viewModelScope.launch {
            try {
                _isLoading.value = true
                val success = musicFileCache.clearAllCache()
                if (success) {
                    showMessage("清空缓存成功")
                    loadCachedSongs() // 刷新列表
                } else {
                    showMessage("清空缓存失败")
                }
            } catch (e: Exception) {
                Log.e(TAG, "清空缓存失败", e)
                showMessage("清空缓存失败: ${e.message}")
            } finally {
                _isLoading.value = false
            }
        }
    }

    /**
     * 更新缓存设置
     */
    fun updateCacheSettings(
        maxCacheSize: Long? = null,
        wifiOnlyCache: Boolean? = null,
        autoCacheEnabled: Boolean? = null,
    ) {
        val currentSettings = _cacheSettings.value
        val newSettings = currentSettings.copy(
            maxCacheSize = maxCacheSize ?: currentSettings.maxCacheSize,
            wifiOnlyCache = wifiOnlyCache ?: currentSettings.wifiOnlyCache,
            autoCacheEnabled = autoCacheEnabled ?: currentSettings.autoCacheEnabled,
        )

        _cacheSettings.value = newSettings

        // 更新缓存管理器的配置
        musicFileCache.setCacheConfig(newSettings.maxCacheSize, newSettings.wifiOnlyCache)

        // TODO: 保存设置到SharedPreferences
        Log.d(TAG, "缓存设置已更新: $newSettings")
        showMessage("设置已保存")
    }

    /**
     * 获取缓存大小选项
     */
    fun getCacheSizeOptions(): List<Pair<String, Long>> {
        return listOf(
            "100MB" to (100L * 1024 * 1024),
            "200MB" to (200L * 1024 * 1024),
            "500MB" to (500L * 1024 * 1024),
            "1GB" to (1024L * 1024 * 1024),
            "2GB" to (2L * 1024 * 1024 * 1024),
            "5GB" to (5L * 1024 * 1024 * 1024),
        )
    }

    /**
     * 格式化文件大小
     */
    fun formatFileSize(bytes: Long): String {
        return when {
            bytes < 1024 -> "${bytes}B"
            bytes < 1024 * 1024 -> "${bytes / 1024}KB"
            bytes < 1024 * 1024 * 1024 -> "${bytes / 1024 / 1024}MB"
            else -> "${bytes / 1024 / 1024 / 1024}GB"
        }
    }

    /**
     * 格式化时间
     */
    fun formatTime(timestamp: Long): String {
        val now = System.currentTimeMillis()
        val diff = now - timestamp

        return when {
            diff < 60 * 1000 -> "刚刚"
            diff < 60 * 60 * 1000 -> "${diff / (60 * 1000)}分钟前"
            diff < 24 * 60 * 60 * 1000 -> "${diff / (60 * 60 * 1000)}小时前"
            diff < 7 * 24 * 60 * 60 * 1000 -> "${diff / (24 * 60 * 60 * 1000)}天前"
            else -> {
                val date = java.util.Date(timestamp)
                java.text.SimpleDateFormat("MM-dd", java.util.Locale.getDefault()).format(date)
            }
        }
    }

    /**
     * 显示操作消息
     */
    private fun showMessage(message: String) {
        _operationMessage.value = message
        // 3秒后清除消息
        viewModelScope.launch {
            kotlinx.coroutines.delay(3000)
            if (_operationMessage.value == message) {
                _operationMessage.value = null
            }
        }
    }

    /**
     * 清除操作消息
     */
    fun clearMessage() {
        _operationMessage.value = null
    }

    /**
     * 检查歌曲是否已缓存
     */
    suspend fun isSongCached(songId: Long): Boolean {
        return musicFileCache.isCached(songId)
    }

    /**
     * 手动缓存歌曲
     */
    fun cacheSong(song: com.example.aimusicplayer.data.model.Song) {
        viewModelScope.launch {
            try {
                _isLoading.value = true
                val success = musicFileCache.manualCacheSong(song) { progress ->
                    // TODO: 更新下载进度UI
                    Log.d(TAG, "缓存进度: ${song.name} - ${(progress.progress * 100).toInt()}%")
                }

                if (success) {
                    showMessage("缓存完成: ${song.name}")
                    loadCachedSongs() // 刷新列表
                } else {
                    showMessage("缓存失败: ${song.name}")
                }
            } catch (e: Exception) {
                Log.e(TAG, "缓存歌曲失败", e)
                showMessage("缓存失败: ${e.message}")
            } finally {
                _isLoading.value = false
            }
        }
    }

    /**
     * 获取缓存使用率颜色
     */
    fun getCacheUsageColor(percentage: Float): Int {
        return when {
            percentage < 50 -> android.graphics.Color.GREEN
            percentage < 80 -> android.graphics.Color.YELLOW
            else -> android.graphics.Color.RED
        }
    }
}
