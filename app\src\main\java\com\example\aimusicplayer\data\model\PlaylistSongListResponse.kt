package com.example.aimusicplayer.data.model

import com.google.gson.annotations.SerializedName

/**
 * 歌单歌曲列表API响应模型
 * 严格对应网易云音乐API /playlist/track/all 接口
 * 参考：ponymusic-master项目标准
 */
data class PlaylistSongListResponse(
    val songs: List<Song>? = null,
    val privileges: List<PrivilegeData>? = null,
) : BaseResponse()

/**
 * 用户歌单API响应模型
 * 严格对应网易云音乐API /user/playlist 接口
 */
data class UserPlaylistResponse(
    val version: String? = null,
    val more: Boolean = false,
    val playlist: List<UserPlaylistData>? = null,
) : BaseResponse()

/**
 * 用户歌单数据
 */
data class UserPlaylistData(
    @SerializedName("id")
    val id: Long = 0,

    @SerializedName("name")
    val name: String = "",

    @SerializedName("coverImgId")
    val coverImgId: Long = 0,

    @SerializedName("coverImgUrl")
    val coverImgUrl: String = "",

    @SerializedName("coverImgId_str")
    val coverImgIdStr: String? = null,

    @SerializedName("adType")
    val adType: Int = 0,

    @SerializedName("userId")
    val userId: Long = 0,

    @SerializedName("createTime")
    val createTime: Long = 0,

    @SerializedName("status")
    val status: Int = 0,

    @SerializedName("opRecommend")
    val opRecommend: Boolean = false,

    @SerializedName("highQuality")
    val highQuality: Boolean = false,

    @SerializedName("newImported")
    val newImported: Boolean = false,

    @SerializedName("updateTime")
    val updateTime: Long = 0,

    @SerializedName("trackCount")
    val trackCount: Int = 0,

    @SerializedName("specialType")
    val specialType: Int = 0,

    @SerializedName("privacy")
    val privacy: Int = 0,

    @SerializedName("trackUpdateTime")
    val trackUpdateTime: Long = 0,

    @SerializedName("commentThreadId")
    val commentThreadId: String? = null,

    @SerializedName("playCount")
    val playCount: Long = 0,

    @SerializedName("trackNumberUpdateTime")
    val trackNumberUpdateTime: Long = 0,

    @SerializedName("subscribedCount")
    val subscribedCount: Long = 0,

    @SerializedName("cloudTrackCount")
    val cloudTrackCount: Int = 0,

    @SerializedName("ordered")
    val ordered: Boolean = true,

    @SerializedName("description")
    val description: String? = null,

    @SerializedName("tags")
    val tags: List<String>? = null,

    @SerializedName("updateFrequency")
    val updateFrequency: Any? = null,

    @SerializedName("backgroundCoverId")
    val backgroundCoverId: Long = 0,

    @SerializedName("backgroundCoverUrl")
    val backgroundCoverUrl: String? = null,

    @SerializedName("titleImage")
    val titleImage: Long = 0,

    @SerializedName("titleImageUrl")
    val titleImageUrl: String? = null,

    @SerializedName("englishTitle")
    val englishTitle: String? = null,

    @SerializedName("officialPlaylistType")
    val officialPlaylistType: Any? = null,

    @SerializedName("copied")
    val copied: Boolean = false,

    @SerializedName("relateResType")
    val relateResType: Any? = null,

    @SerializedName("subscribers")
    val subscribers: List<User>? = null,

    @SerializedName("subscribed")
    val subscribed: Boolean = false,

    @SerializedName("creator")
    val creator: User? = null,

    @SerializedName("tracks")
    val tracks: List<Song>? = null,

    @SerializedName("videoIds")
    val videoIds: Any? = null,

    @SerializedName("videos")
    val videos: Any? = null,

    @SerializedName("trackIds")
    val trackIds: List<TrackIdData>? = null,

    @SerializedName("bannedTrackIds")
    val bannedTrackIds: Any? = null,

    @SerializedName("mvResourceInfos")
    val mvResourceInfos: Any? = null,

    @SerializedName("shareCount")
    val shareCount: Long = 0,

    @SerializedName("commentCount")
    val commentCount: Long = 0,

    @SerializedName("remixVideo")
    val remixVideo: Any? = null,

    @SerializedName("sharedUsers")
    val sharedUsers: Any? = null,

    @SerializedName("historySharedUsers")
    val historySharedUsers: Any? = null,

    @SerializedName("gradeStatus")
    val gradeStatus: String? = null,

    @SerializedName("score")
    val score: Any? = null,

    @SerializedName("algTags")
    val algTags: Any? = null,

    @SerializedName("trialMode")
    val trialMode: Int = 0,

    @SerializedName("displayTags")
    val displayTags: Any? = null,

    @SerializedName("platFormAlgTags")
    val platFormAlgTags: Any? = null,

    @SerializedName("relatedVideos")
    val relatedVideos: Any? = null,

    @SerializedName("appLinks")
    val appLinks: Any? = null,

    @SerializedName("ToplistType")
    val toplistType: String? = null,
) {
    /**
     * 获取歌单封面URL（高清）
     */
    fun getHighQualityCoverUrl(): String {
        return if (coverImgUrl.isNotEmpty()) {
            "$coverImgUrl?param=300y300"
        } else {
            ""
        }
    }

    /**
     * 获取播放次数描述
     */
    fun getPlayCountDescription(): String {
        return when {
            playCount >= 100000000 -> String.format("%.1f亿", playCount / 100000000.0)
            playCount >= 10000 -> String.format("%.1f万", playCount / 10000.0)
            else -> playCount.toString()
        }
    }

    /**
     * 检查是否为用户创建的歌单
     */
    fun isUserCreated(): Boolean {
        return specialType == 0
    }

    /**
     * 检查是否为收藏的歌单
     */
    fun isSubscribed(): Boolean {
        return subscribed
    }
}

/**
 * 数据转换扩展方法
 */
fun UserPlaylistData.toPlayList(): PlayList {
    return PlayList(
        id = this.id.toString(),
        name = this.name,
        coverImgUrl = this.getHighQualityCoverUrl(),
        playCount = this.playCount.toInt(),
        songCount = this.trackCount,
        description = this.description ?: "",
        creatorName = this.creator?.username ?: "",
        subscribed = this.subscribed,
    )
}

/**
 * 批量转换方法
 */
fun List<UserPlaylistData>.toPlayLists(): List<PlayList> {
    return this.map { it.toPlayList() }
}

/**
 * UserPlaylistResponse扩展方法 - 获取歌单列表
 */
fun UserPlaylistResponse.getPlaylists(): List<PlayList> {
    return playlist?.toPlayLists() ?: emptyList()
}

/**
 * PlaylistSongListResponse扩展方法 - 获取歌曲列表
 */
fun PlaylistSongListResponse.getSongs(): List<Song> {
    return songs ?: emptyList()
}
