#!/usr/bin/env node

/**
 * 添加新API接口的辅助脚本
 * 用于快速添加新的API接口到综合测试脚本中
 * 
 * 使用方法：
 * node add_api_endpoint.js --name=newApi --path="/new/api?param=value" --description="新API接口" --critical=false --category="其他"
 */

const fs = require('fs');
const path = require('path');

// 命令行参数解析
const args = process.argv.slice(2);
const config = {
    name: '',
    path: '',
    description: '',
    critical: false,
    category: '其他功能',
    expectedFields: ['code']
};

args.forEach(arg => {
    if (arg.startsWith('--name=')) {
        config.name = arg.split('=')[1];
    } else if (arg.startsWith('--path=')) {
        config.path = arg.split('=')[1];
    } else if (arg.startsWith('--description=')) {
        config.description = arg.split('=')[1];
    } else if (arg.startsWith('--critical=')) {
        config.critical = arg.split('=')[1] === 'true';
    } else if (arg.startsWith('--category=')) {
        config.category = arg.split('=')[1];
    } else if (arg.startsWith('--fields=')) {
        config.expectedFields = arg.split('=')[1].split(',');
    }
});

// 验证参数
if (!config.name || !config.path || !config.description) {
    console.error('❌ 缺少必要参数！');
    console.log('使用方法：');
    console.log('node add_api_endpoint.js --name=接口名 --path="/api/path" --description="接口描述" [--critical=true/false] [--category="分类"] [--fields="code,data"]');
    console.log('');
    console.log('示例：');
    console.log('node add_api_endpoint.js --name=albumDetail --path="/album?id=12345" --description="专辑详情接口" --critical=false --category="专辑相关" --fields="code,album"');
    process.exit(1);
}

// 生成新的API配置
const newApiConfig = {
    path: config.path,
    expectedFields: config.expectedFields,
    description: config.description,
    critical: config.critical
};

// 读取综合测试脚本
const testScriptPath = path.join(__dirname, 'comprehensive_test.js');
let testScriptContent = fs.readFileSync(testScriptPath, 'utf8');

// 查找API_ENDPOINTS配置的位置
const apiEndpointsStart = testScriptContent.indexOf('const API_ENDPOINTS = {');
const apiEndpointsEnd = testScriptContent.indexOf('};', apiEndpointsStart);

if (apiEndpointsStart === -1 || apiEndpointsEnd === -1) {
    console.error('❌ 无法找到API_ENDPOINTS配置！');
    process.exit(1);
}

// 提取现有的API配置
const beforeConfig = testScriptContent.substring(0, apiEndpointsEnd);
const afterConfig = testScriptContent.substring(apiEndpointsEnd);

// 查找合适的分类位置插入新接口
const categoryComment = `// ===== ${config.category} =====`;
let insertPosition = apiEndpointsEnd;

if (beforeConfig.includes(categoryComment)) {
    // 找到现有分类，在该分类的最后添加
    const categoryStart = beforeConfig.lastIndexOf(categoryComment);
    const nextCategoryStart = beforeConfig.indexOf('// =====', categoryStart + categoryComment.length);
    
    if (nextCategoryStart !== -1) {
        // 在下一个分类之前插入
        insertPosition = nextCategoryStart;
    } else {
        // 在配置末尾插入
        insertPosition = apiEndpointsEnd;
    }
} else {
    // 创建新分类
    const newCategorySection = `
    
    ${categoryComment}`;
    const beforeInsert = testScriptContent.substring(0, apiEndpointsEnd);
    const afterInsert = testScriptContent.substring(apiEndpointsEnd);
    testScriptContent = beforeInsert + newCategorySection + afterInsert;
    insertPosition = apiEndpointsEnd + newCategorySection.length;
}

// 生成新接口配置代码
const newApiCode = `
    ${config.name}: {
        path: '${config.path}',
        expectedFields: ${JSON.stringify(config.expectedFields)},
        description: '${config.description}',
        critical: ${config.critical}
    },`;

// 插入新接口配置
const beforeInsert = testScriptContent.substring(0, insertPosition);
const afterInsert = testScriptContent.substring(insertPosition);
const updatedContent = beforeInsert + newApiCode + afterInsert;

// 写回文件
fs.writeFileSync(testScriptPath, updatedContent, 'utf8');

console.log('✅ 成功添加新API接口！');
console.log(`📝 接口名称: ${config.name}`);
console.log(`🔗 接口路径: ${config.path}`);
console.log(`📋 接口描述: ${config.description}`);
console.log(`⚠️  是否关键: ${config.critical ? '是' : '否'}`);
console.log(`📁 分类: ${config.category}`);
console.log(`🔍 预期字段: ${config.expectedFields.join(', ')}`);
console.log('');
console.log('🧪 运行测试验证新接口：');
console.log('node comprehensive_test.js --mode=api --timeout=10 --retry=1');
console.log('');
console.log('📊 运行监控模式测试所有接口：');
console.log('node comprehensive_test.js --mode=monitor --timeout=10 --retry=1');

// 显示当前API统计
const apiMatches = updatedContent.match(/^\s+\w+:\s*{/gm);
const totalApis = apiMatches ? apiMatches.length : 0;
console.log('');
console.log(`📈 当前总接口数: ${totalApis}`);
