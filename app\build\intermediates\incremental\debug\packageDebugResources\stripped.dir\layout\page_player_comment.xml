<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:background="@android:color/transparent">

    <!-- 顶部标题栏 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center_vertical"
        android:padding="12dp">

        <!-- 返回按钮 -->
        <ImageButton
            android:id="@+id/btn_back_to_lyric"
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:background="@drawable/control_button_no_ripple"
            android:src="@drawable/ic_arrow_back"
            android:contentDescription="返回歌词"
            android:padding="12dp"
            android:clickable="true"
            android:focusable="true"
            app:tint="@color/text_light" />

        <!-- 标题 -->
        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:layout_marginStart="12dp"
            android:text="评论"
            android:textColor="@color/text_light"
            android:textSize="18sp"
            android:textStyle="bold"
            android:gravity="start|center_vertical" />

    </LinearLayout>

    <!-- 评论列表 -->
    <androidx.swiperefreshlayout.widget.SwipeRefreshLayout
        android:id="@+id/swipe_refresh_layout"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1">

        <FrameLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <!-- 评论RecyclerView -->
            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/recycler_view_comments"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:clipToPadding="false"
                android:paddingVertical="8dp"
                android:paddingHorizontal="12dp"
                tools:listitem="@layout/item_comment" />

            <!-- 空状态提示 -->
            <TextView
                android:id="@+id/text_empty_comment"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:text="暂无评论"
                android:textColor="@color/color_gray_500"
                android:textSize="16sp"
                android:visibility="gone"
                android:drawableTop="@drawable/ic_comment"
                android:drawableTint="@color/color_gray_500"
                android:drawablePadding="8dp"
                android:gravity="center" />

            <!-- 加载指示器 -->
            <ProgressBar
                android:id="@+id/progress_bar"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:visibility="gone"
                android:indeterminateTint="@color/colorAccent" />

        </FrameLayout>
    </androidx.swiperefreshlayout.widget.SwipeRefreshLayout>

    <!-- 评论输入区域 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:padding="12dp"
        android:background="@color/color_gray_900"
        android:alpha="0.9">

        <!-- 评论输入框 -->
        <EditText
            android:id="@+id/edit_text_comment"
            android:layout_width="0dp"
            android:layout_height="48dp"
            android:layout_weight="1"
            android:layout_marginEnd="8dp"
            android:background="@drawable/sakura_search_background"
            android:hint="写评论..."
            android:textColorHint="@color/color_gray_500"
            android:textColor="@color/color_black"
            android:textSize="14sp"
            android:paddingHorizontal="12dp"
            android:paddingVertical="8dp"
            android:imeOptions="actionSend"
            android:inputType="text"
            android:maxLines="3"
            android:minHeight="48dp" />

        <!-- 发送按钮 -->
        <ImageButton
            android:id="@+id/btn_send_comment"
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:background="@drawable/sakura_button"
            android:src="@drawable/ic_send"
            android:contentDescription="发送评论"
            android:padding="12dp"
            android:clickable="true"
            android:focusable="true"
            app:tint="@color/white" />

    </LinearLayout>

</LinearLayout>
