package com.example.aimusicplayer.ui.intelligence

import android.util.Log
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.example.aimusicplayer.data.db.entity.SongEntity
import com.example.aimusicplayer.data.model.SongModel
import com.example.aimusicplayer.data.source.MusicDataSource
import com.example.aimusicplayer.utils.toUnMutable
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * 心动模式ViewModel
 * 严格按照ponymusic项目标准重构
 */
@HiltViewModel
class IntelligenceViewModel @Inject constructor(
    private val musicDataSource: MusicDataSource,
) : ViewModel() {

    companion object {
        private const val TAG = "IntelligenceViewModel"
    }

    // 严格按照ponymusic项目标准的StateFlow使用
    private val _currentSongFlow = MutableStateFlow<SongModel?>(null)
    val currentSongFlow = _currentSongFlow.toUnMutable()

    private val _intelligenceSongsFlow = MutableStateFlow<List<SongModel>>(emptyList())
    val intelligenceSongsFlow = _intelligenceSongsFlow.toUnMutable()

    // 加载状态
    private val _loading = MutableStateFlow(false)
    val loading = _loading.toUnMutable()

    // 错误信息
    private val _errorMessage = MutableStateFlow<String?>(null)
    val errorMessage = _errorMessage.toUnMutable()

    // UI兼容属性（为了兼容现有UI层代码）
    val currentSong = currentSongFlow
    val intelligenceSongs = intelligenceSongsFlow

    /**
     * 加载心动模式歌曲列表
     * @param songId 歌曲ID
     * @param playlistId 歌单ID，可选
     */
    fun loadIntelligenceList(songId: Long, playlistId: Long = -1L) {
        viewModelScope.launch {
            _loading.value = true
            _errorMessage.value = null

            try {
                // 加载当前歌曲信息
                val song = musicDataSource.getSongByIdFromDb(songId, SongEntity.TYPE_ONLINE)
                if (song != null) {
                    _currentSongFlow.value = SongModel(
                        songId = song.songId,
                        title = song.title,
                        artist = song.artist,
                        album = song.album,
                        albumCover = song.albumCover,
                        duration = song.duration,
                        isVip = false, // 默认值
                        isLocal = song.type == SongEntity.TYPE_LOCAL,
                        url = song.uri,
                    )
                }

                // 加载心动模式歌曲列表
                val songs = if (playlistId > 0) {
                    musicDataSource.getIntelligenceList(songId, playlistId)
                } else {
                    musicDataSource.getIntelligenceList(songId)
                }

                // 转换为SongModel列表
                _intelligenceSongsFlow.value = songs.map { entity ->
                    SongModel(
                        songId = entity.songId,
                        title = entity.title,
                        artist = entity.artist,
                        album = entity.album,
                        albumCover = entity.albumCover,
                        duration = entity.duration,
                        isVip = false, // 默认值
                        isLocal = entity.type == SongEntity.TYPE_LOCAL,
                        url = entity.uri,
                    )
                }

                Log.d(TAG, "心动模式歌曲加载成功: ${songs.size}首歌曲")
            } catch (e: Exception) {
                _errorMessage.value = "加载心动模式歌曲失败: ${e.message}"
                Log.e(TAG, "加载心动模式歌曲列表失败", e)
                _intelligenceSongsFlow.value = emptyList()
            } finally {
                _loading.value = false
            }
        }
    }
}
