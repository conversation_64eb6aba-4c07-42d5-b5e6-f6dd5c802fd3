package com.example.aimusicplayer.utils

import android.content.Context
import android.content.SharedPreferences
import android.util.Log
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 搜索历史管理器
 * 负责搜索历史的持久化存储、读取和管理
 */
@Singleton
class SearchHistoryManager @Inject constructor(
    @ApplicationContext private val context: Context,
) {
    companion object {
        private const val TAG = "SearchHistoryManager"
        private const val PREFS_NAME = "search_history_prefs"
        private const val KEY_SEARCH_HISTORY = "search_history"
        private const val MAX_HISTORY_SIZE = 10
    }

    private val sharedPreferences: SharedPreferences =
        context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)

    private val gson = Gson()

    // 搜索历史状态流
    private val _historyFlow = MutableStateFlow<List<SearchHistoryItem>>(emptyList())
    val historyFlow: StateFlow<List<SearchHistoryItem>> = _historyFlow.asStateFlow()

    init {
        // 初始化时加载历史记录
        loadHistory()
    }

    /**
     * 搜索历史项数据类
     */
    data class SearchHistoryItem(
        val keyword: String,
        val timestamp: Long = System.currentTimeMillis(),
    )

    /**
     * 添加搜索记录
     * @param keyword 搜索关键词
     */
    fun addSearchHistory(keyword: String) {
        if (keyword.isBlank()) return

        try {
            val currentHistory = _historyFlow.value.toMutableList()

            // 移除已存在的相同关键词
            currentHistory.removeAll { it.keyword == keyword }

            // 添加到顶部
            currentHistory.add(0, SearchHistoryItem(keyword))

            // 限制最大数量
            if (currentHistory.size > MAX_HISTORY_SIZE) {
                currentHistory.removeAt(currentHistory.size - 1)
            }

            // 保存到SharedPreferences
            saveHistory(currentHistory)

            // 更新状态流
            _historyFlow.value = currentHistory

            Log.d(TAG, "添加搜索历史: $keyword")
        } catch (e: Exception) {
            Log.e(TAG, "添加搜索历史失败", e)
        }
    }

    /**
     * 删除单条搜索记录
     * @param keyword 要删除的关键词
     */
    fun removeSearchHistory(keyword: String) {
        try {
            val currentHistory = _historyFlow.value.toMutableList()
            currentHistory.removeAll { it.keyword == keyword }

            // 保存到SharedPreferences
            saveHistory(currentHistory)

            // 更新状态流
            _historyFlow.value = currentHistory

            Log.d(TAG, "删除搜索历史: $keyword")
        } catch (e: Exception) {
            Log.e(TAG, "删除搜索历史失败", e)
        }
    }

    /**
     * 清除所有搜索历史
     */
    fun clearAllHistory() {
        try {
            sharedPreferences.edit().remove(KEY_SEARCH_HISTORY).apply()
            _historyFlow.value = emptyList()
            Log.d(TAG, "清除所有搜索历史")
        } catch (e: Exception) {
            Log.e(TAG, "清除搜索历史失败", e)
        }
    }

    /**
     * 获取搜索历史列表
     * @param maxCount 最大返回数量，默认3条
     * @return 搜索历史列表
     */
    fun getSearchHistory(maxCount: Int = 3): List<SearchHistoryItem> {
        return _historyFlow.value.take(maxCount)
    }

    /**
     * 从SharedPreferences加载历史记录
     */
    private fun loadHistory() {
        try {
            val historyJson = sharedPreferences.getString(KEY_SEARCH_HISTORY, null)
            if (historyJson != null) {
                val type = object : TypeToken<List<SearchHistoryItem>>() {}.type
                val history = gson.fromJson<List<SearchHistoryItem>>(historyJson, type)
                _historyFlow.value = history ?: emptyList()
                Log.d(TAG, "加载搜索历史: ${history?.size ?: 0}条")
            }
        } catch (e: Exception) {
            Log.e(TAG, "加载搜索历史失败", e)
            _historyFlow.value = emptyList()
        }
    }

    /**
     * 保存历史记录到SharedPreferences
     */
    private fun saveHistory(history: List<SearchHistoryItem>) {
        try {
            val historyJson = gson.toJson(history)
            sharedPreferences.edit().putString(KEY_SEARCH_HISTORY, historyJson).apply()
        } catch (e: Exception) {
            Log.e(TAG, "保存搜索历史失败", e)
        }
    }
}
