<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="fragment_player" modulePackage="com.example.aimusicplayer" filePath="app\src\main\res\layout\fragment_player.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.RelativeLayout"><Targets><Target tag="layout/fragment_player_0" view="RelativeLayout"><Expressions/><location startLine="1" startOffset="0" endLine="431" endOffset="16"/></Target><Target id="@+id/background_blur" view="ImageView"><Expressions/><location startLine="8" startOffset="4" endLine="14" endOffset="29"/></Target><Target id="@+id/background_overlay" view="View"><Expressions/><location startLine="17" startOffset="4" endLine="22" endOffset="29"/></Target><Target id="@+id/content_container" view="LinearLayout"><Expressions/><location startLine="25" startOffset="4" endLine="248" endOffset="18"/></Target><Target id="@+id/vinyl_record_view" view="com.example.aimusicplayer.ui.widget.VinylRecordView"><Expressions/><location startLine="41" startOffset="12" endLine="46" endOffset="49"/></Target><Target id="@+id/album_cover_view" view="com.example.aimusicplayer.ui.widget.AlbumCoverView"><Expressions/><location startLine="49" startOffset="12" endLine="55" endOffset="43"/></Target><Target id="@+id/album_art" view="ImageView"><Expressions/><location startLine="58" startOffset="12" endLine="69" endOffset="57"/></Target><Target id="@+id/vinyl_background" view="ImageView"><Expressions/><location startLine="72" startOffset="12" endLine="80" endOffset="43"/></Target><Target id="@+id/song_title" view="TextView"><Expressions/><location startLine="92" startOffset="16" endLine="111" endOffset="46"/></Target><Target id="@+id/song_artist" view="TextView"><Expressions/><location startLine="113" startOffset="16" endLine="132" endOffset="46"/></Target><Target id="@+id/search_container" view="RelativeLayout"><Expressions/><location startLine="145" startOffset="12" endLine="199" endOffset="28"/></Target><Target id="@+id/search_edit_text" view="EditText"><Expressions/><location startLine="154" startOffset="16" endLine="171" endOffset="47"/></Target><Target id="@+id/search_button" view="ImageView"><Expressions/><location startLine="174" startOffset="16" endLine="185" endOffset="46"/></Target><Target id="@+id/heart_mode_button" view="ImageView"><Expressions/><location startLine="188" startOffset="16" endLine="198" endOffset="46"/></Target><Target id="@+id/search_suggestions_recycler" view="androidx.recyclerview.widget.RecyclerView"><Expressions/><location startLine="202" startOffset="12" endLine="212" endOffset="56"/></Target><Target id="@+id/search_results_recycler" view="androidx.recyclerview.widget.RecyclerView"><Expressions/><location startLine="215" startOffset="12" endLine="224" endOffset="41"/></Target><Target id="@+id/tab_layout_player" view="com.google.android.material.tabs.TabLayout"><Expressions/><location startLine="227" startOffset="12" endLine="237" endOffset="43"/></Target><Target id="@+id/view_pager_player" view="androidx.viewpager2.widget.ViewPager2"><Expressions/><location startLine="240" startOffset="12" endLine="246" endOffset="54"/></Target><Target id="@+id/loading_view" view="com.example.aimusicplayer.ui.widget.LottieLoadingView"><Expressions/><location startLine="251" startOffset="4" endLine="261" endOffset="25"/></Target><Target id="@+id/control_container" view="LinearLayout"><Expressions/><location startLine="264" startOffset="4" endLine="430" endOffset="18"/></Target><Target id="@+id/textview_player_current_time" view="TextView"><Expressions/><location startLine="284" startOffset="12" endLine="292" endOffset="38"/></Target><Target id="@+id/seekbar_player_progress" view="SeekBar"><Expressions/><location startLine="294" startOffset="12" endLine="304" endOffset="43"/></Target><Target id="@+id/textview_player_total_time" view="TextView"><Expressions/><location startLine="306" startOffset="12" endLine="314" endOffset="38"/></Target><Target id="@+id/button_player_playlist" view="ImageView"><Expressions/><location startLine="326" startOffset="12" endLine="338" endOffset="41"/></Target><Target id="@+id/button_player_play_mode" view="ImageView"><Expressions/><location startLine="341" startOffset="12" endLine="353" endOffset="41"/></Target><Target id="@+id/button_player_prev" view="ImageView"><Expressions/><location startLine="356" startOffset="12" endLine="368" endOffset="41"/></Target><Target id="@+id/button_player_play_pause" view="ImageView"><Expressions/><location startLine="371" startOffset="12" endLine="383" endOffset="51"/></Target><Target id="@+id/button_player_next" view="ImageView"><Expressions/><location startLine="386" startOffset="12" endLine="398" endOffset="41"/></Target><Target id="@+id/button_player_comment" view="ImageView"><Expressions/><location startLine="401" startOffset="12" endLine="413" endOffset="41"/></Target><Target id="@+id/button_player_collect" view="ImageView"><Expressions/><location startLine="416" startOffset="12" endLine="428" endOffset="41"/></Target></Targets></Layout>