package com.example.aimusicplayer.network

import android.util.Log
import kotlinx.coroutines.*
import javax.inject.Inject
import javax.inject.Singleton

/**
 * API调用策略管理器 - 极简版
 * 严格参考ponymusic-master项目标准，专注核心功能
 */
@Singleton
class ApiCallStrategy @Inject constructor() {

    companion object {
        private const val TAG = "ApiCallStrategy"
        const val SEARCH_DEBOUNCE_DELAY = 300L // 搜索防抖动300ms
    }

    // 搜索防抖动
    private var searchJob: Job? = null

    /**
     * 搜索防抖动 - 参考ponymusic标准
     */
    fun debouncedSearch(keywords: String, searchAction: suspend (String) -> Unit) {
        searchJob?.cancel()
        searchJob = CoroutineScope(Dispatchers.Main).launch {
            delay(SEARCH_DEBOUNCE_DELAY)
            kotlin.runCatching { searchAction(keywords) }
                .onFailure { Log.e(TAG, "搜索执行失败", it) }
        }
    }

    /**
     * 取消搜索任务
     */
    fun cancelSearch() {
        searchJob?.cancel()
        searchJob = null
    }
}
