package com.example.aimusicplayer;

import android.app.Activity;
import android.app.Service;
import android.content.Context;
import android.content.SharedPreferences;
import android.view.View;
import androidx.fragment.app.Fragment;
import androidx.lifecycle.SavedStateHandle;
import androidx.lifecycle.ViewModel;
import com.example.aimusicplayer.data.cache.ApiCacheManager;
import com.example.aimusicplayer.data.cache.MusicFileCache;
import com.example.aimusicplayer.data.db.AppDatabase;
import com.example.aimusicplayer.data.db.dao.ApiCacheDao;
import com.example.aimusicplayer.data.db.dao.PlaylistDao;
import com.example.aimusicplayer.data.db.dao.SongDao;
import com.example.aimusicplayer.data.db.dao.UserDao;
import com.example.aimusicplayer.data.repository.BaseRepository_MembersInjector;
import com.example.aimusicplayer.data.repository.CommentRepository;
import com.example.aimusicplayer.data.repository.CommentRepository_Factory;
import com.example.aimusicplayer.data.repository.MusicRepository;
import com.example.aimusicplayer.data.repository.MusicRepository_Factory;
import com.example.aimusicplayer.data.repository.UserRepository;
import com.example.aimusicplayer.data.repository.UserRepository_Factory;
import com.example.aimusicplayer.data.source.ApiService;
import com.example.aimusicplayer.data.source.MusicDataSource;
import com.example.aimusicplayer.di.AppModule_ProvideAlbumArtCacheFactory;
import com.example.aimusicplayer.di.AppModule_ProvideContextFactory;
import com.example.aimusicplayer.di.AppModule_ProvideMusicDataSourceFactory;
import com.example.aimusicplayer.di.AppModule_ProvideSharedPreferencesFactory;
import com.example.aimusicplayer.di.DatabaseModule_ProvideApiCacheDaoFactory;
import com.example.aimusicplayer.di.DatabaseModule_ProvideAppDatabaseFactory;
import com.example.aimusicplayer.di.DatabaseModule_ProvidePlaylistDaoFactory;
import com.example.aimusicplayer.di.DatabaseModule_ProvideSongDaoFactory;
import com.example.aimusicplayer.di.DatabaseModule_ProvideUserDaoFactory;
import com.example.aimusicplayer.di.NetworkModule_ProvideApiServiceFactory;
import com.example.aimusicplayer.di.NetworkModule_ProvideGsonFactory;
import com.example.aimusicplayer.di.NetworkModule_ProvideOkHttpClientFactory;
import com.example.aimusicplayer.di.NetworkModule_ProvideRetrofitFactory;
import com.example.aimusicplayer.network.ApiCallStrategy;
import com.example.aimusicplayer.network.NetworkStateManager;
import com.example.aimusicplayer.service.UnifiedPlaybackService;
import com.example.aimusicplayer.service.UnifiedPlaybackService_MembersInjector;
import com.example.aimusicplayer.service.UserServiceImpl;
import com.example.aimusicplayer.ui.comment.CommentFragment;
import com.example.aimusicplayer.ui.dialog.PlayQueueDialogFragment;
import com.example.aimusicplayer.ui.discovery.DiscoveryFragment;
import com.example.aimusicplayer.ui.intelligence.IntelligenceFragment;
import com.example.aimusicplayer.ui.intelligence.IntelligenceViewModel;
import com.example.aimusicplayer.ui.intelligence.IntelligenceViewModel_HiltModules_KeyModule_ProvideFactory;
import com.example.aimusicplayer.ui.login.LoginActivity;
import com.example.aimusicplayer.ui.main.MainActivity;
import com.example.aimusicplayer.ui.player.CommentPageFragment;
import com.example.aimusicplayer.ui.player.PlayerFragment;
import com.example.aimusicplayer.ui.player.PlayerFragment_MembersInjector;
import com.example.aimusicplayer.ui.player.PlaylistPageFragment;
import com.example.aimusicplayer.ui.profile.UserProfileFragment;
import com.example.aimusicplayer.ui.settings.CacheManagementFragment;
import com.example.aimusicplayer.ui.splash.SplashActivity;
import com.example.aimusicplayer.utils.AlbumArtCache;
import com.example.aimusicplayer.viewmodel.CacheManagementViewModel;
import com.example.aimusicplayer.viewmodel.CacheManagementViewModel_HiltModules_KeyModule_ProvideFactory;
import com.example.aimusicplayer.viewmodel.CommentViewModel;
import com.example.aimusicplayer.viewmodel.CommentViewModel_HiltModules_KeyModule_ProvideFactory;
import com.example.aimusicplayer.viewmodel.DrivingModeViewModel;
import com.example.aimusicplayer.viewmodel.DrivingModeViewModel_HiltModules_KeyModule_ProvideFactory;
import com.example.aimusicplayer.viewmodel.ExampleViewModel;
import com.example.aimusicplayer.viewmodel.ExampleViewModel_HiltModules_KeyModule_ProvideFactory;
import com.example.aimusicplayer.viewmodel.LoginViewModel;
import com.example.aimusicplayer.viewmodel.LoginViewModel_HiltModules_KeyModule_ProvideFactory;
import com.example.aimusicplayer.viewmodel.MainViewModel;
import com.example.aimusicplayer.viewmodel.MainViewModel_HiltModules_KeyModule_ProvideFactory;
import com.example.aimusicplayer.viewmodel.MusicLibraryViewModel;
import com.example.aimusicplayer.viewmodel.MusicLibraryViewModel_HiltModules_KeyModule_ProvideFactory;
import com.example.aimusicplayer.viewmodel.PlayerViewModel;
import com.example.aimusicplayer.viewmodel.PlayerViewModel_HiltModules_KeyModule_ProvideFactory;
import com.example.aimusicplayer.viewmodel.SettingsViewModel;
import com.example.aimusicplayer.viewmodel.SettingsViewModel_HiltModules_KeyModule_ProvideFactory;
import com.example.aimusicplayer.viewmodel.SplashViewModel;
import com.example.aimusicplayer.viewmodel.SplashViewModel_HiltModules_KeyModule_ProvideFactory;
import com.example.aimusicplayer.viewmodel.UserProfileViewModel;
import com.example.aimusicplayer.viewmodel.UserProfileViewModel_HiltModules_KeyModule_ProvideFactory;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.ImmutableSet;
import com.google.errorprone.annotations.CanIgnoreReturnValue;
import com.google.gson.Gson;
import dagger.hilt.android.ActivityRetainedLifecycle;
import dagger.hilt.android.ViewModelLifecycle;
import dagger.hilt.android.internal.builders.ActivityComponentBuilder;
import dagger.hilt.android.internal.builders.ActivityRetainedComponentBuilder;
import dagger.hilt.android.internal.builders.FragmentComponentBuilder;
import dagger.hilt.android.internal.builders.ServiceComponentBuilder;
import dagger.hilt.android.internal.builders.ViewComponentBuilder;
import dagger.hilt.android.internal.builders.ViewModelComponentBuilder;
import dagger.hilt.android.internal.builders.ViewWithFragmentComponentBuilder;
import dagger.hilt.android.internal.lifecycle.DefaultViewModelFactories;
import dagger.hilt.android.internal.lifecycle.DefaultViewModelFactories_InternalFactoryFactory_Factory;
import dagger.hilt.android.internal.managers.ActivityRetainedComponentManager_LifecycleModule_ProvideActivityRetainedLifecycleFactory;
import dagger.hilt.android.internal.managers.SavedStateHandleHolder;
import dagger.hilt.android.internal.modules.ApplicationContextModule;
import dagger.hilt.android.internal.modules.ApplicationContextModule_ProvideContextFactory;
import dagger.internal.DaggerGenerated;
import dagger.internal.DoubleCheck;
import dagger.internal.Preconditions;
import dagger.internal.Provider;
import java.util.Map;
import java.util.Set;
import javax.annotation.processing.Generated;
import okhttp3.OkHttpClient;
import retrofit2.Retrofit;

@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class DaggerMusicApplication_HiltComponents_SingletonC {
  private DaggerMusicApplication_HiltComponents_SingletonC() {
  }

  public static Builder builder() {
    return new Builder();
  }

  public static final class Builder {
    private ApplicationContextModule applicationContextModule;

    private Builder() {
    }

    public Builder applicationContextModule(ApplicationContextModule applicationContextModule) {
      this.applicationContextModule = Preconditions.checkNotNull(applicationContextModule);
      return this;
    }

    public MusicApplication_HiltComponents.SingletonC build() {
      Preconditions.checkBuilderRequirement(applicationContextModule, ApplicationContextModule.class);
      return new SingletonCImpl(applicationContextModule);
    }
  }

  private static final class ActivityRetainedCBuilder implements MusicApplication_HiltComponents.ActivityRetainedC.Builder {
    private final SingletonCImpl singletonCImpl;

    private SavedStateHandleHolder savedStateHandleHolder;

    private ActivityRetainedCBuilder(SingletonCImpl singletonCImpl) {
      this.singletonCImpl = singletonCImpl;
    }

    @Override
    public ActivityRetainedCBuilder savedStateHandleHolder(
        SavedStateHandleHolder savedStateHandleHolder) {
      this.savedStateHandleHolder = Preconditions.checkNotNull(savedStateHandleHolder);
      return this;
    }

    @Override
    public MusicApplication_HiltComponents.ActivityRetainedC build() {
      Preconditions.checkBuilderRequirement(savedStateHandleHolder, SavedStateHandleHolder.class);
      return new ActivityRetainedCImpl(singletonCImpl, savedStateHandleHolder);
    }
  }

  private static final class ActivityCBuilder implements MusicApplication_HiltComponents.ActivityC.Builder {
    private final SingletonCImpl singletonCImpl;

    private final ActivityRetainedCImpl activityRetainedCImpl;

    private Activity activity;

    private ActivityCBuilder(SingletonCImpl singletonCImpl,
        ActivityRetainedCImpl activityRetainedCImpl) {
      this.singletonCImpl = singletonCImpl;
      this.activityRetainedCImpl = activityRetainedCImpl;
    }

    @Override
    public ActivityCBuilder activity(Activity activity) {
      this.activity = Preconditions.checkNotNull(activity);
      return this;
    }

    @Override
    public MusicApplication_HiltComponents.ActivityC build() {
      Preconditions.checkBuilderRequirement(activity, Activity.class);
      return new ActivityCImpl(singletonCImpl, activityRetainedCImpl, activity);
    }
  }

  private static final class FragmentCBuilder implements MusicApplication_HiltComponents.FragmentC.Builder {
    private final SingletonCImpl singletonCImpl;

    private final ActivityRetainedCImpl activityRetainedCImpl;

    private final ActivityCImpl activityCImpl;

    private Fragment fragment;

    private FragmentCBuilder(SingletonCImpl singletonCImpl,
        ActivityRetainedCImpl activityRetainedCImpl, ActivityCImpl activityCImpl) {
      this.singletonCImpl = singletonCImpl;
      this.activityRetainedCImpl = activityRetainedCImpl;
      this.activityCImpl = activityCImpl;
    }

    @Override
    public FragmentCBuilder fragment(Fragment fragment) {
      this.fragment = Preconditions.checkNotNull(fragment);
      return this;
    }

    @Override
    public MusicApplication_HiltComponents.FragmentC build() {
      Preconditions.checkBuilderRequirement(fragment, Fragment.class);
      return new FragmentCImpl(singletonCImpl, activityRetainedCImpl, activityCImpl, fragment);
    }
  }

  private static final class ViewWithFragmentCBuilder implements MusicApplication_HiltComponents.ViewWithFragmentC.Builder {
    private final SingletonCImpl singletonCImpl;

    private final ActivityRetainedCImpl activityRetainedCImpl;

    private final ActivityCImpl activityCImpl;

    private final FragmentCImpl fragmentCImpl;

    private View view;

    private ViewWithFragmentCBuilder(SingletonCImpl singletonCImpl,
        ActivityRetainedCImpl activityRetainedCImpl, ActivityCImpl activityCImpl,
        FragmentCImpl fragmentCImpl) {
      this.singletonCImpl = singletonCImpl;
      this.activityRetainedCImpl = activityRetainedCImpl;
      this.activityCImpl = activityCImpl;
      this.fragmentCImpl = fragmentCImpl;
    }

    @Override
    public ViewWithFragmentCBuilder view(View view) {
      this.view = Preconditions.checkNotNull(view);
      return this;
    }

    @Override
    public MusicApplication_HiltComponents.ViewWithFragmentC build() {
      Preconditions.checkBuilderRequirement(view, View.class);
      return new ViewWithFragmentCImpl(singletonCImpl, activityRetainedCImpl, activityCImpl, fragmentCImpl, view);
    }
  }

  private static final class ViewCBuilder implements MusicApplication_HiltComponents.ViewC.Builder {
    private final SingletonCImpl singletonCImpl;

    private final ActivityRetainedCImpl activityRetainedCImpl;

    private final ActivityCImpl activityCImpl;

    private View view;

    private ViewCBuilder(SingletonCImpl singletonCImpl, ActivityRetainedCImpl activityRetainedCImpl,
        ActivityCImpl activityCImpl) {
      this.singletonCImpl = singletonCImpl;
      this.activityRetainedCImpl = activityRetainedCImpl;
      this.activityCImpl = activityCImpl;
    }

    @Override
    public ViewCBuilder view(View view) {
      this.view = Preconditions.checkNotNull(view);
      return this;
    }

    @Override
    public MusicApplication_HiltComponents.ViewC build() {
      Preconditions.checkBuilderRequirement(view, View.class);
      return new ViewCImpl(singletonCImpl, activityRetainedCImpl, activityCImpl, view);
    }
  }

  private static final class ViewModelCBuilder implements MusicApplication_HiltComponents.ViewModelC.Builder {
    private final SingletonCImpl singletonCImpl;

    private final ActivityRetainedCImpl activityRetainedCImpl;

    private SavedStateHandle savedStateHandle;

    private ViewModelLifecycle viewModelLifecycle;

    private ViewModelCBuilder(SingletonCImpl singletonCImpl,
        ActivityRetainedCImpl activityRetainedCImpl) {
      this.singletonCImpl = singletonCImpl;
      this.activityRetainedCImpl = activityRetainedCImpl;
    }

    @Override
    public ViewModelCBuilder savedStateHandle(SavedStateHandle handle) {
      this.savedStateHandle = Preconditions.checkNotNull(handle);
      return this;
    }

    @Override
    public ViewModelCBuilder viewModelLifecycle(ViewModelLifecycle viewModelLifecycle) {
      this.viewModelLifecycle = Preconditions.checkNotNull(viewModelLifecycle);
      return this;
    }

    @Override
    public MusicApplication_HiltComponents.ViewModelC build() {
      Preconditions.checkBuilderRequirement(savedStateHandle, SavedStateHandle.class);
      Preconditions.checkBuilderRequirement(viewModelLifecycle, ViewModelLifecycle.class);
      return new ViewModelCImpl(singletonCImpl, activityRetainedCImpl, savedStateHandle, viewModelLifecycle);
    }
  }

  private static final class ServiceCBuilder implements MusicApplication_HiltComponents.ServiceC.Builder {
    private final SingletonCImpl singletonCImpl;

    private Service service;

    private ServiceCBuilder(SingletonCImpl singletonCImpl) {
      this.singletonCImpl = singletonCImpl;
    }

    @Override
    public ServiceCBuilder service(Service service) {
      this.service = Preconditions.checkNotNull(service);
      return this;
    }

    @Override
    public MusicApplication_HiltComponents.ServiceC build() {
      Preconditions.checkBuilderRequirement(service, Service.class);
      return new ServiceCImpl(singletonCImpl, service);
    }
  }

  private static final class ViewWithFragmentCImpl extends MusicApplication_HiltComponents.ViewWithFragmentC {
    private final SingletonCImpl singletonCImpl;

    private final ActivityRetainedCImpl activityRetainedCImpl;

    private final ActivityCImpl activityCImpl;

    private final FragmentCImpl fragmentCImpl;

    private final ViewWithFragmentCImpl viewWithFragmentCImpl = this;

    private ViewWithFragmentCImpl(SingletonCImpl singletonCImpl,
        ActivityRetainedCImpl activityRetainedCImpl, ActivityCImpl activityCImpl,
        FragmentCImpl fragmentCImpl, View viewParam) {
      this.singletonCImpl = singletonCImpl;
      this.activityRetainedCImpl = activityRetainedCImpl;
      this.activityCImpl = activityCImpl;
      this.fragmentCImpl = fragmentCImpl;


    }
  }

  private static final class FragmentCImpl extends MusicApplication_HiltComponents.FragmentC {
    private final SingletonCImpl singletonCImpl;

    private final ActivityRetainedCImpl activityRetainedCImpl;

    private final ActivityCImpl activityCImpl;

    private final FragmentCImpl fragmentCImpl = this;

    private FragmentCImpl(SingletonCImpl singletonCImpl,
        ActivityRetainedCImpl activityRetainedCImpl, ActivityCImpl activityCImpl,
        Fragment fragmentParam) {
      this.singletonCImpl = singletonCImpl;
      this.activityRetainedCImpl = activityRetainedCImpl;
      this.activityCImpl = activityCImpl;


    }

    @Override
    public void injectCommentFragment(CommentFragment commentFragment) {
    }

    @Override
    public void injectPlayQueueDialogFragment(PlayQueueDialogFragment playQueueDialogFragment) {
    }

    @Override
    public void injectDiscoveryFragment(DiscoveryFragment discoveryFragment) {
    }

    @Override
    public void injectIntelligenceFragment(IntelligenceFragment intelligenceFragment) {
    }

    @Override
    public void injectCommentPageFragment(CommentPageFragment commentPageFragment) {
    }

    @Override
    public void injectPlayerFragment(PlayerFragment playerFragment) {
      injectPlayerFragment2(playerFragment);
    }

    @Override
    public void injectPlaylistPageFragment(PlaylistPageFragment playlistPageFragment) {
    }

    @Override
    public void injectUserProfileFragment(UserProfileFragment userProfileFragment) {
    }

    @Override
    public void injectCacheManagementFragment(CacheManagementFragment cacheManagementFragment) {
    }

    @Override
    public DefaultViewModelFactories.InternalFactoryFactory getHiltInternalFactoryFactory() {
      return activityCImpl.getHiltInternalFactoryFactory();
    }

    @Override
    public ViewWithFragmentComponentBuilder viewWithFragmentComponentBuilder() {
      return new ViewWithFragmentCBuilder(singletonCImpl, activityRetainedCImpl, activityCImpl, fragmentCImpl);
    }

    @CanIgnoreReturnValue
    private PlayerFragment injectPlayerFragment2(PlayerFragment instance) {
      PlayerFragment_MembersInjector.injectAlbumArtCache(instance, singletonCImpl.provideAlbumArtCacheProvider.get());
      PlayerFragment_MembersInjector.injectNetworkStateManager(instance, singletonCImpl.networkStateManagerProvider.get());
      return instance;
    }
  }

  private static final class ViewCImpl extends MusicApplication_HiltComponents.ViewC {
    private final SingletonCImpl singletonCImpl;

    private final ActivityRetainedCImpl activityRetainedCImpl;

    private final ActivityCImpl activityCImpl;

    private final ViewCImpl viewCImpl = this;

    private ViewCImpl(SingletonCImpl singletonCImpl, ActivityRetainedCImpl activityRetainedCImpl,
        ActivityCImpl activityCImpl, View viewParam) {
      this.singletonCImpl = singletonCImpl;
      this.activityRetainedCImpl = activityRetainedCImpl;
      this.activityCImpl = activityCImpl;


    }
  }

  private static final class ActivityCImpl extends MusicApplication_HiltComponents.ActivityC {
    private final SingletonCImpl singletonCImpl;

    private final ActivityRetainedCImpl activityRetainedCImpl;

    private final ActivityCImpl activityCImpl = this;

    private ActivityCImpl(SingletonCImpl singletonCImpl,
        ActivityRetainedCImpl activityRetainedCImpl, Activity activityParam) {
      this.singletonCImpl = singletonCImpl;
      this.activityRetainedCImpl = activityRetainedCImpl;


    }

    @Override
    public void injectLoginActivity(LoginActivity loginActivity) {
    }

    @Override
    public void injectMainActivity(MainActivity mainActivity) {
    }

    @Override
    public void injectSplashActivity(SplashActivity splashActivity) {
    }

    @Override
    public DefaultViewModelFactories.InternalFactoryFactory getHiltInternalFactoryFactory() {
      return DefaultViewModelFactories_InternalFactoryFactory_Factory.newInstance(getViewModelKeys(), new ViewModelCBuilder(singletonCImpl, activityRetainedCImpl));
    }

    @Override
    public Set<String> getViewModelKeys() {
      return ImmutableSet.<String>of(CacheManagementViewModel_HiltModules_KeyModule_ProvideFactory.provide(), CommentViewModel_HiltModules_KeyModule_ProvideFactory.provide(), DrivingModeViewModel_HiltModules_KeyModule_ProvideFactory.provide(), ExampleViewModel_HiltModules_KeyModule_ProvideFactory.provide(), IntelligenceViewModel_HiltModules_KeyModule_ProvideFactory.provide(), LoginViewModel_HiltModules_KeyModule_ProvideFactory.provide(), MainViewModel_HiltModules_KeyModule_ProvideFactory.provide(), MusicLibraryViewModel_HiltModules_KeyModule_ProvideFactory.provide(), PlayerViewModel_HiltModules_KeyModule_ProvideFactory.provide(), SettingsViewModel_HiltModules_KeyModule_ProvideFactory.provide(), SplashViewModel_HiltModules_KeyModule_ProvideFactory.provide(), UserProfileViewModel_HiltModules_KeyModule_ProvideFactory.provide());
    }

    @Override
    public ViewModelComponentBuilder getViewModelComponentBuilder() {
      return new ViewModelCBuilder(singletonCImpl, activityRetainedCImpl);
    }

    @Override
    public FragmentComponentBuilder fragmentComponentBuilder() {
      return new FragmentCBuilder(singletonCImpl, activityRetainedCImpl, activityCImpl);
    }

    @Override
    public ViewComponentBuilder viewComponentBuilder() {
      return new ViewCBuilder(singletonCImpl, activityRetainedCImpl, activityCImpl);
    }
  }

  private static final class ViewModelCImpl extends MusicApplication_HiltComponents.ViewModelC {
    private final SingletonCImpl singletonCImpl;

    private final ActivityRetainedCImpl activityRetainedCImpl;

    private final ViewModelCImpl viewModelCImpl = this;

    private Provider<CacheManagementViewModel> cacheManagementViewModelProvider;

    private Provider<CommentViewModel> commentViewModelProvider;

    private Provider<DrivingModeViewModel> drivingModeViewModelProvider;

    private Provider<ExampleViewModel> exampleViewModelProvider;

    private Provider<IntelligenceViewModel> intelligenceViewModelProvider;

    private Provider<LoginViewModel> loginViewModelProvider;

    private Provider<MainViewModel> mainViewModelProvider;

    private Provider<MusicLibraryViewModel> musicLibraryViewModelProvider;

    private Provider<PlayerViewModel> playerViewModelProvider;

    private Provider<SettingsViewModel> settingsViewModelProvider;

    private Provider<SplashViewModel> splashViewModelProvider;

    private Provider<UserProfileViewModel> userProfileViewModelProvider;

    private ViewModelCImpl(SingletonCImpl singletonCImpl,
        ActivityRetainedCImpl activityRetainedCImpl, SavedStateHandle savedStateHandleParam,
        ViewModelLifecycle viewModelLifecycleParam) {
      this.singletonCImpl = singletonCImpl;
      this.activityRetainedCImpl = activityRetainedCImpl;

      initialize(savedStateHandleParam, viewModelLifecycleParam);

    }

    @SuppressWarnings("unchecked")
    private void initialize(final SavedStateHandle savedStateHandleParam,
        final ViewModelLifecycle viewModelLifecycleParam) {
      this.cacheManagementViewModelProvider = new SwitchingProvider<>(singletonCImpl, activityRetainedCImpl, viewModelCImpl, 0);
      this.commentViewModelProvider = new SwitchingProvider<>(singletonCImpl, activityRetainedCImpl, viewModelCImpl, 1);
      this.drivingModeViewModelProvider = new SwitchingProvider<>(singletonCImpl, activityRetainedCImpl, viewModelCImpl, 2);
      this.exampleViewModelProvider = new SwitchingProvider<>(singletonCImpl, activityRetainedCImpl, viewModelCImpl, 3);
      this.intelligenceViewModelProvider = new SwitchingProvider<>(singletonCImpl, activityRetainedCImpl, viewModelCImpl, 4);
      this.loginViewModelProvider = new SwitchingProvider<>(singletonCImpl, activityRetainedCImpl, viewModelCImpl, 5);
      this.mainViewModelProvider = new SwitchingProvider<>(singletonCImpl, activityRetainedCImpl, viewModelCImpl, 6);
      this.musicLibraryViewModelProvider = new SwitchingProvider<>(singletonCImpl, activityRetainedCImpl, viewModelCImpl, 7);
      this.playerViewModelProvider = new SwitchingProvider<>(singletonCImpl, activityRetainedCImpl, viewModelCImpl, 8);
      this.settingsViewModelProvider = new SwitchingProvider<>(singletonCImpl, activityRetainedCImpl, viewModelCImpl, 9);
      this.splashViewModelProvider = new SwitchingProvider<>(singletonCImpl, activityRetainedCImpl, viewModelCImpl, 10);
      this.userProfileViewModelProvider = new SwitchingProvider<>(singletonCImpl, activityRetainedCImpl, viewModelCImpl, 11);
    }

    @Override
    public Map<String, javax.inject.Provider<ViewModel>> getHiltViewModelMap() {
      return ImmutableMap.<String, javax.inject.Provider<ViewModel>>builderWithExpectedSize(12).put("com.example.aimusicplayer.viewmodel.CacheManagementViewModel", ((Provider) cacheManagementViewModelProvider)).put("com.example.aimusicplayer.viewmodel.CommentViewModel", ((Provider) commentViewModelProvider)).put("com.example.aimusicplayer.viewmodel.DrivingModeViewModel", ((Provider) drivingModeViewModelProvider)).put("com.example.aimusicplayer.viewmodel.ExampleViewModel", ((Provider) exampleViewModelProvider)).put("com.example.aimusicplayer.ui.intelligence.IntelligenceViewModel", ((Provider) intelligenceViewModelProvider)).put("com.example.aimusicplayer.viewmodel.LoginViewModel", ((Provider) loginViewModelProvider)).put("com.example.aimusicplayer.viewmodel.MainViewModel", ((Provider) mainViewModelProvider)).put("com.example.aimusicplayer.viewmodel.MusicLibraryViewModel", ((Provider) musicLibraryViewModelProvider)).put("com.example.aimusicplayer.viewmodel.PlayerViewModel", ((Provider) playerViewModelProvider)).put("com.example.aimusicplayer.viewmodel.SettingsViewModel", ((Provider) settingsViewModelProvider)).put("com.example.aimusicplayer.viewmodel.SplashViewModel", ((Provider) splashViewModelProvider)).put("com.example.aimusicplayer.viewmodel.UserProfileViewModel", ((Provider) userProfileViewModelProvider)).build();
    }

    @Override
    public Map<String, Object> getHiltViewModelAssistedMap() {
      return ImmutableMap.<String, Object>of();
    }

    private static final class SwitchingProvider<T> implements Provider<T> {
      private final SingletonCImpl singletonCImpl;

      private final ActivityRetainedCImpl activityRetainedCImpl;

      private final ViewModelCImpl viewModelCImpl;

      private final int id;

      SwitchingProvider(SingletonCImpl singletonCImpl, ActivityRetainedCImpl activityRetainedCImpl,
          ViewModelCImpl viewModelCImpl, int id) {
        this.singletonCImpl = singletonCImpl;
        this.activityRetainedCImpl = activityRetainedCImpl;
        this.viewModelCImpl = viewModelCImpl;
        this.id = id;
      }

      @SuppressWarnings("unchecked")
      @Override
      public T get() {
        switch (id) {
          case 0: // com.example.aimusicplayer.viewmodel.CacheManagementViewModel 
          return (T) new CacheManagementViewModel(singletonCImpl.musicFileCacheProvider.get());

          case 1: // com.example.aimusicplayer.viewmodel.CommentViewModel 
          return (T) new CommentViewModel(singletonCImpl.musicRepositoryProvider.get(), singletonCImpl.commentRepositoryProvider.get());

          case 2: // com.example.aimusicplayer.viewmodel.DrivingModeViewModel 
          return (T) new DrivingModeViewModel(singletonCImpl.musicRepositoryProvider.get());

          case 3: // com.example.aimusicplayer.viewmodel.ExampleViewModel 
          return (T) new ExampleViewModel(singletonCImpl.musicRepositoryProvider.get(), singletonCImpl.userRepositoryProvider.get());

          case 4: // com.example.aimusicplayer.ui.intelligence.IntelligenceViewModel 
          return (T) new IntelligenceViewModel(singletonCImpl.provideMusicDataSourceProvider.get());

          case 5: // com.example.aimusicplayer.viewmodel.LoginViewModel 
          return (T) new LoginViewModel(singletonCImpl.userRepositoryProvider.get(), singletonCImpl.userServiceImplProvider.get());

          case 6: // com.example.aimusicplayer.viewmodel.MainViewModel 
          return (T) new MainViewModel(singletonCImpl.musicRepositoryProvider.get());

          case 7: // com.example.aimusicplayer.viewmodel.MusicLibraryViewModel 
          return (T) new MusicLibraryViewModel(singletonCImpl.musicRepositoryProvider.get());

          case 8: // com.example.aimusicplayer.viewmodel.PlayerViewModel 
          return (T) new PlayerViewModel(singletonCImpl.musicRepositoryProvider.get(), singletonCImpl.musicFileCacheProvider.get());

          case 9: // com.example.aimusicplayer.viewmodel.SettingsViewModel 
          return (T) new SettingsViewModel();

          case 10: // com.example.aimusicplayer.viewmodel.SplashViewModel 
          return (T) new SplashViewModel(singletonCImpl.userRepositoryProvider.get());

          case 11: // com.example.aimusicplayer.viewmodel.UserProfileViewModel 
          return (T) new UserProfileViewModel(singletonCImpl.userRepositoryProvider.get());

          default: throw new AssertionError(id);
        }
      }
    }
  }

  private static final class ActivityRetainedCImpl extends MusicApplication_HiltComponents.ActivityRetainedC {
    private final SingletonCImpl singletonCImpl;

    private final ActivityRetainedCImpl activityRetainedCImpl = this;

    private Provider<ActivityRetainedLifecycle> provideActivityRetainedLifecycleProvider;

    private ActivityRetainedCImpl(SingletonCImpl singletonCImpl,
        SavedStateHandleHolder savedStateHandleHolderParam) {
      this.singletonCImpl = singletonCImpl;

      initialize(savedStateHandleHolderParam);

    }

    @SuppressWarnings("unchecked")
    private void initialize(final SavedStateHandleHolder savedStateHandleHolderParam) {
      this.provideActivityRetainedLifecycleProvider = DoubleCheck.provider(new SwitchingProvider<ActivityRetainedLifecycle>(singletonCImpl, activityRetainedCImpl, 0));
    }

    @Override
    public ActivityComponentBuilder activityComponentBuilder() {
      return new ActivityCBuilder(singletonCImpl, activityRetainedCImpl);
    }

    @Override
    public ActivityRetainedLifecycle getActivityRetainedLifecycle() {
      return provideActivityRetainedLifecycleProvider.get();
    }

    private static final class SwitchingProvider<T> implements Provider<T> {
      private final SingletonCImpl singletonCImpl;

      private final ActivityRetainedCImpl activityRetainedCImpl;

      private final int id;

      SwitchingProvider(SingletonCImpl singletonCImpl, ActivityRetainedCImpl activityRetainedCImpl,
          int id) {
        this.singletonCImpl = singletonCImpl;
        this.activityRetainedCImpl = activityRetainedCImpl;
        this.id = id;
      }

      @SuppressWarnings("unchecked")
      @Override
      public T get() {
        switch (id) {
          case 0: // dagger.hilt.android.ActivityRetainedLifecycle 
          return (T) ActivityRetainedComponentManager_LifecycleModule_ProvideActivityRetainedLifecycleFactory.provideActivityRetainedLifecycle();

          default: throw new AssertionError(id);
        }
      }
    }
  }

  private static final class ServiceCImpl extends MusicApplication_HiltComponents.ServiceC {
    private final SingletonCImpl singletonCImpl;

    private final ServiceCImpl serviceCImpl = this;

    private ServiceCImpl(SingletonCImpl singletonCImpl, Service serviceParam) {
      this.singletonCImpl = singletonCImpl;


    }

    @Override
    public void injectUnifiedPlaybackService(UnifiedPlaybackService unifiedPlaybackService) {
      injectUnifiedPlaybackService2(unifiedPlaybackService);
    }

    @CanIgnoreReturnValue
    private UnifiedPlaybackService injectUnifiedPlaybackService2(UnifiedPlaybackService instance) {
      UnifiedPlaybackService_MembersInjector.injectMusicDataSource(instance, singletonCImpl.provideMusicDataSourceProvider.get());
      return instance;
    }
  }

  private static final class SingletonCImpl extends MusicApplication_HiltComponents.SingletonC {
    private final ApplicationContextModule applicationContextModule;

    private final SingletonCImpl singletonCImpl = this;

    private Provider<AlbumArtCache> provideAlbumArtCacheProvider;

    private Provider<NetworkStateManager> networkStateManagerProvider;

    private Provider<OkHttpClient> provideOkHttpClientProvider;

    private Provider<MusicFileCache> musicFileCacheProvider;

    private Provider<AppDatabase> provideAppDatabaseProvider;

    private Provider<SongDao> provideSongDaoProvider;

    private Provider<PlaylistDao> providePlaylistDaoProvider;

    private Provider<MusicDataSource> provideMusicDataSourceProvider;

    private Provider<ApiCacheDao> provideApiCacheDaoProvider;

    private Provider<Gson> provideGsonProvider;

    private Provider<ApiCacheManager> apiCacheManagerProvider;

    private Provider<ApiCallStrategy> apiCallStrategyProvider;

    private Provider<MusicRepository> musicRepositoryProvider;

    private Provider<Retrofit> provideRetrofitProvider;

    private Provider<ApiService> provideApiServiceProvider;

    private Provider<CommentRepository> commentRepositoryProvider;

    private Provider<Context> provideContextProvider;

    private Provider<SharedPreferences> provideSharedPreferencesProvider;

    private Provider<UserDao> provideUserDaoProvider;

    private Provider<UserRepository> userRepositoryProvider;

    private Provider<UserServiceImpl> userServiceImplProvider;

    private SingletonCImpl(ApplicationContextModule applicationContextModuleParam) {
      this.applicationContextModule = applicationContextModuleParam;
      initialize(applicationContextModuleParam);

    }

    @SuppressWarnings("unchecked")
    private void initialize(final ApplicationContextModule applicationContextModuleParam) {
      this.provideAlbumArtCacheProvider = DoubleCheck.provider(new SwitchingProvider<AlbumArtCache>(singletonCImpl, 0));
      this.networkStateManagerProvider = DoubleCheck.provider(new SwitchingProvider<NetworkStateManager>(singletonCImpl, 1));
      this.provideOkHttpClientProvider = DoubleCheck.provider(new SwitchingProvider<OkHttpClient>(singletonCImpl, 3));
      this.musicFileCacheProvider = DoubleCheck.provider(new SwitchingProvider<MusicFileCache>(singletonCImpl, 2));
      this.provideAppDatabaseProvider = DoubleCheck.provider(new SwitchingProvider<AppDatabase>(singletonCImpl, 7));
      this.provideSongDaoProvider = DoubleCheck.provider(new SwitchingProvider<SongDao>(singletonCImpl, 6));
      this.providePlaylistDaoProvider = DoubleCheck.provider(new SwitchingProvider<PlaylistDao>(singletonCImpl, 8));
      this.provideMusicDataSourceProvider = DoubleCheck.provider(new SwitchingProvider<MusicDataSource>(singletonCImpl, 5));
      this.provideApiCacheDaoProvider = DoubleCheck.provider(new SwitchingProvider<ApiCacheDao>(singletonCImpl, 10));
      this.provideGsonProvider = DoubleCheck.provider(new SwitchingProvider<Gson>(singletonCImpl, 11));
      this.apiCacheManagerProvider = DoubleCheck.provider(new SwitchingProvider<ApiCacheManager>(singletonCImpl, 9));
      this.apiCallStrategyProvider = DoubleCheck.provider(new SwitchingProvider<ApiCallStrategy>(singletonCImpl, 12));
      this.musicRepositoryProvider = DoubleCheck.provider(new SwitchingProvider<MusicRepository>(singletonCImpl, 4));
      this.provideRetrofitProvider = DoubleCheck.provider(new SwitchingProvider<Retrofit>(singletonCImpl, 15));
      this.provideApiServiceProvider = DoubleCheck.provider(new SwitchingProvider<ApiService>(singletonCImpl, 14));
      this.commentRepositoryProvider = DoubleCheck.provider(new SwitchingProvider<CommentRepository>(singletonCImpl, 13));
      this.provideContextProvider = DoubleCheck.provider(new SwitchingProvider<Context>(singletonCImpl, 17));
      this.provideSharedPreferencesProvider = DoubleCheck.provider(new SwitchingProvider<SharedPreferences>(singletonCImpl, 18));
      this.provideUserDaoProvider = DoubleCheck.provider(new SwitchingProvider<UserDao>(singletonCImpl, 19));
      this.userRepositoryProvider = DoubleCheck.provider(new SwitchingProvider<UserRepository>(singletonCImpl, 16));
      this.userServiceImplProvider = DoubleCheck.provider(new SwitchingProvider<UserServiceImpl>(singletonCImpl, 20));
    }

    @Override
    public void injectMusicApplication(MusicApplication musicApplication) {
    }

    @Override
    public Set<Boolean> getDisableFragmentGetContextFix() {
      return ImmutableSet.<Boolean>of();
    }

    @Override
    public ActivityRetainedComponentBuilder retainedComponentBuilder() {
      return new ActivityRetainedCBuilder(singletonCImpl);
    }

    @Override
    public ServiceComponentBuilder serviceComponentBuilder() {
      return new ServiceCBuilder(singletonCImpl);
    }

    @CanIgnoreReturnValue
    private MusicRepository injectMusicRepository(MusicRepository instance) {
      BaseRepository_MembersInjector.injectApiCacheManager(instance, apiCacheManagerProvider.get());
      BaseRepository_MembersInjector.injectApiCallStrategy(instance, apiCallStrategyProvider.get());
      return instance;
    }

    @CanIgnoreReturnValue
    private CommentRepository injectCommentRepository(CommentRepository instance) {
      BaseRepository_MembersInjector.injectApiCacheManager(instance, apiCacheManagerProvider.get());
      BaseRepository_MembersInjector.injectApiCallStrategy(instance, apiCallStrategyProvider.get());
      return instance;
    }

    @CanIgnoreReturnValue
    private UserRepository injectUserRepository(UserRepository instance) {
      BaseRepository_MembersInjector.injectApiCacheManager(instance, apiCacheManagerProvider.get());
      BaseRepository_MembersInjector.injectApiCallStrategy(instance, apiCallStrategyProvider.get());
      return instance;
    }

    private static final class SwitchingProvider<T> implements Provider<T> {
      private final SingletonCImpl singletonCImpl;

      private final int id;

      SwitchingProvider(SingletonCImpl singletonCImpl, int id) {
        this.singletonCImpl = singletonCImpl;
        this.id = id;
      }

      @SuppressWarnings("unchecked")
      @Override
      public T get() {
        switch (id) {
          case 0: // com.example.aimusicplayer.utils.AlbumArtCache 
          return (T) AppModule_ProvideAlbumArtCacheFactory.provideAlbumArtCache(ApplicationContextModule_ProvideContextFactory.provideContext(singletonCImpl.applicationContextModule));

          case 1: // com.example.aimusicplayer.network.NetworkStateManager 
          return (T) new NetworkStateManager(ApplicationContextModule_ProvideContextFactory.provideContext(singletonCImpl.applicationContextModule));

          case 2: // com.example.aimusicplayer.data.cache.MusicFileCache 
          return (T) new MusicFileCache(ApplicationContextModule_ProvideContextFactory.provideContext(singletonCImpl.applicationContextModule), singletonCImpl.provideOkHttpClientProvider.get());

          case 3: // okhttp3.OkHttpClient 
          return (T) NetworkModule_ProvideOkHttpClientFactory.provideOkHttpClient(ApplicationContextModule_ProvideContextFactory.provideContext(singletonCImpl.applicationContextModule));

          case 4: // com.example.aimusicplayer.data.repository.MusicRepository 
          return (T) singletonCImpl.injectMusicRepository(MusicRepository_Factory.newInstance(singletonCImpl.provideMusicDataSourceProvider.get()));

          case 5: // com.example.aimusicplayer.data.source.MusicDataSource 
          return (T) AppModule_ProvideMusicDataSourceFactory.provideMusicDataSource(ApplicationContextModule_ProvideContextFactory.provideContext(singletonCImpl.applicationContextModule), singletonCImpl.provideOkHttpClientProvider.get(), singletonCImpl.provideSongDaoProvider.get(), singletonCImpl.providePlaylistDaoProvider.get());

          case 6: // com.example.aimusicplayer.data.db.dao.SongDao 
          return (T) DatabaseModule_ProvideSongDaoFactory.provideSongDao(singletonCImpl.provideAppDatabaseProvider.get());

          case 7: // com.example.aimusicplayer.data.db.AppDatabase 
          return (T) DatabaseModule_ProvideAppDatabaseFactory.provideAppDatabase(ApplicationContextModule_ProvideContextFactory.provideContext(singletonCImpl.applicationContextModule));

          case 8: // com.example.aimusicplayer.data.db.dao.PlaylistDao 
          return (T) DatabaseModule_ProvidePlaylistDaoFactory.providePlaylistDao(singletonCImpl.provideAppDatabaseProvider.get());

          case 9: // com.example.aimusicplayer.data.cache.ApiCacheManager 
          return (T) new ApiCacheManager(singletonCImpl.provideApiCacheDaoProvider.get(), singletonCImpl.provideGsonProvider.get());

          case 10: // com.example.aimusicplayer.data.db.dao.ApiCacheDao 
          return (T) DatabaseModule_ProvideApiCacheDaoFactory.provideApiCacheDao(singletonCImpl.provideAppDatabaseProvider.get());

          case 11: // com.google.gson.Gson 
          return (T) NetworkModule_ProvideGsonFactory.provideGson();

          case 12: // com.example.aimusicplayer.network.ApiCallStrategy 
          return (T) new ApiCallStrategy();

          case 13: // com.example.aimusicplayer.data.repository.CommentRepository 
          return (T) singletonCImpl.injectCommentRepository(CommentRepository_Factory.newInstance(singletonCImpl.provideApiServiceProvider.get()));

          case 14: // com.example.aimusicplayer.data.source.ApiService 
          return (T) NetworkModule_ProvideApiServiceFactory.provideApiService(singletonCImpl.provideRetrofitProvider.get());

          case 15: // retrofit2.Retrofit 
          return (T) NetworkModule_ProvideRetrofitFactory.provideRetrofit(singletonCImpl.provideOkHttpClientProvider.get(), singletonCImpl.provideGsonProvider.get());

          case 16: // com.example.aimusicplayer.data.repository.UserRepository 
          return (T) singletonCImpl.injectUserRepository(UserRepository_Factory.newInstance(singletonCImpl.provideContextProvider.get(), singletonCImpl.provideApiServiceProvider.get(), singletonCImpl.provideSharedPreferencesProvider.get(), singletonCImpl.provideUserDaoProvider.get()));

          case 17: // android.content.Context 
          return (T) AppModule_ProvideContextFactory.provideContext(ApplicationContextModule_ProvideContextFactory.provideContext(singletonCImpl.applicationContextModule));

          case 18: // android.content.SharedPreferences 
          return (T) AppModule_ProvideSharedPreferencesFactory.provideSharedPreferences(ApplicationContextModule_ProvideContextFactory.provideContext(singletonCImpl.applicationContextModule));

          case 19: // com.example.aimusicplayer.data.db.dao.UserDao 
          return (T) DatabaseModule_ProvideUserDaoFactory.provideUserDao(singletonCImpl.provideAppDatabaseProvider.get());

          case 20: // com.example.aimusicplayer.service.UserServiceImpl 
          return (T) new UserServiceImpl(singletonCImpl.userRepositoryProvider.get(), singletonCImpl.provideApiServiceProvider.get());

          default: throw new AssertionError(id);
        }
      }
    }
  }
}
