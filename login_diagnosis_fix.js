#!/usr/bin/env node

/**
 * 登录功能问题诊断和修复脚本
 * 分析三种登录方式的失败原因并提供修复方案
 */

const https = require('https');
const fs = require('fs');

// 服务器配置
const SERVERS = {
    primary: 'ncm.zhenxin.me',
    backup: '1355831898-4499wupl9z.ap-guangzhou.tencentscf.com'
};

// 登录相关API测试
const LOGIN_APIS = {
    // 发送验证码
    captchaSent: {
        path: '/captcha/sent?phone=13800138000',
        method: 'GET',
        description: '发送验证码接口',
        expectedResponse: { code: [200, 400] }
    },
    // 验证验证码
    captchaVerify: {
        path: '/captcha/verify?phone=13800138000&captcha=1234',
        method: 'GET',
        description: '验证验证码接口',
        expectedResponse: { code: [503] }
    },
    // 手机号验证码登录
    loginCellphone: {
        path: '/login/cellphone?phone=13800138000&captcha=1234',
        method: 'GET',
        description: '手机号验证码登录接口',
        expectedResponse: { code: [503, 400] }
    },
    // 二维码key生成
    qrKey: {
        path: '/login/qr/key',
        method: 'GET',
        description: '二维码key生成接口',
        expectedResponse: { code: [200] }
    },
    // 二维码生成
    qrCreate: {
        path: '/login/qr/create?key=test&qrimg=true',
        method: 'GET',
        description: '二维码生成接口',
        expectedResponse: { code: [200, 400] }
    },
    // 二维码状态检查
    qrCheck: {
        path: '/login/qr/check?key=test',
        method: 'GET',
        description: '二维码状态检查接口',
        expectedResponse: { code: [800, 801] }
    },
    // 游客登录
    guestLogin: {
        path: '/register/anonimous',
        method: 'GET',
        description: '游客登录接口',
        expectedResponse: { code: [200] }
    },
    // 登录状态检查
    loginStatus: {
        path: '/login/status',
        method: 'POST',
        description: '登录状态检查接口',
        expectedResponse: { code: [200, 301] }
    }
};

// 测试API接口
function testApi(hostname, apiInfo) {
    return new Promise((resolve) => {
        const options = {
            hostname: hostname,
            path: apiInfo.path,
            method: apiInfo.method,
            timeout: 15000,
            headers: {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                'Accept': 'application/json',
                'Referer': `https://${hostname}/`,
                'Origin': `https://${hostname}`
            }
        };

        const req = https.request(options, (res) => {
            let data = '';
            res.on('data', (chunk) => {
                data += chunk;
            });

            res.on('end', () => {
                try {
                    const json = JSON.parse(data);
                    resolve({
                        success: true,
                        statusCode: res.statusCode,
                        headers: res.headers,
                        data: json,
                        cookies: res.headers['set-cookie'] || []
                    });
                } catch (e) {
                    resolve({
                        success: false,
                        statusCode: res.statusCode,
                        error: `JSON解析失败: ${e.message}`,
                        rawData: data
                    });
                }
            });
        });

        req.on('error', (e) => {
            resolve({
                success: false,
                error: `请求失败: ${e.message}`
            });
        });

        req.on('timeout', () => {
            req.destroy();
            resolve({
                success: false,
                error: '请求超时'
            });
        });

        req.end();
    });
}

// 分析登录问题
async function diagnoseLoginIssues() {
    console.log('🔍 登录功能问题诊断报告');
    console.log('='.repeat(80));

    const diagnosisResults = {};

    for (const [apiName, apiInfo] of Object.entries(LOGIN_APIS)) {
        console.log(`\n📋 测试API: ${apiInfo.description}`);
        console.log(`🔗 路径: ${apiInfo.path}`);
        console.log(`📡 方法: ${apiInfo.method}`);

        const apiResults = {};

        for (const [serverKey, hostname] of Object.entries(SERVERS)) {
            console.log(`\n  📡 测试服务器: ${serverKey} (${hostname})`);

            const result = await testApi(hostname, apiInfo);

            if (result.success !== undefined) {
                console.log(`     状态码: ${result.statusCode}`);

                if (result.data) {
                    console.log(`     响应代码: ${result.data.code}`);
                    console.log(`     响应消息: ${result.data.message || result.data.msg || '无'}`);

                    // 检查Cookie
                    if (result.cookies.length > 0) {
                        console.log(`     Cookies: ${result.cookies.length}个`);
                        result.cookies.forEach(cookie => {
                            if (cookie.includes('MUSIC_U')) {
                                console.log(`     ✅ 找到登录Cookie: MUSIC_U`);
                            }
                        });
                    }

                    // 分析响应是否符合预期
                    const expectedCodes = apiInfo.expectedResponse.code;
                    const actualCode = result.data.code;

                    if (expectedCodes.includes(actualCode)) {
                        console.log(`     ✅ 响应符合预期`);
                    } else {
                        console.log(`     ⚠️  响应不符合预期，期望: ${expectedCodes.join('/')}, 实际: ${actualCode}`);
                    }
                } else {
                    console.log(`     ❌ 无法解析响应数据`);
                    console.log(`     原始响应: ${result.rawData?.substring(0, 200)}...`);
                }
            } else {
                console.log(`     ❌ 请求失败: ${result.error}`);
            }

            apiResults[serverKey] = result;
        }

        diagnosisResults[apiName] = apiResults;
    }

    return diagnosisResults;
}

// 分析项目代码问题
function analyzeCodeIssues() {
    console.log('\n\n🔍 项目代码问题分析');
    console.log('='.repeat(80));

    const issues = [];
    const recommendations = [];

    // 1. API接口定义问题
    console.log('\n1. API接口定义分析:');
    console.log('   ❌ 问题: 项目中使用GET请求调用登录接口');
    console.log('   📝 说明: 根据API文档，登录接口应该使用POST请求');
    console.log('   🔧 修复: 将ApiService.kt中的登录相关接口改为POST请求');

    issues.push('API接口使用GET而非POST请求');
    recommendations.push('修改ApiService.kt中的登录接口为POST请求');

    // 2. 参数传递问题
    console.log('\n2. 参数传递分析:');
    console.log('   ❌ 问题: 验证码登录使用Query参数而非Body参数');
    console.log('   📝 说明: 敏感信息应该通过POST Body传递');
    console.log('   🔧 修复: 使用@Field或@Body注解传递登录参数');

    issues.push('敏感参数通过URL Query传递');
    recommendations.push('使用POST Body传递登录参数');

    // 3. Cookie管理问题
    console.log('\n3. Cookie管理分析:');
    console.log('   ❌ 问题: 可能缺少正确的Cookie管理机制');
    console.log('   📝 说明: 网易云API需要正确的Cookie管理');
    console.log('   🔧 修复: 确保OkHttp配置了CookieJar');

    issues.push('Cookie管理可能不完善');
    recommendations.push('配置OkHttp CookieJar');

    // 4. 请求头问题
    console.log('\n4. 请求头分析:');
    console.log('   ❌ 问题: 可能缺少必要的请求头');
    console.log('   📝 说明: 网易云API需要特定的User-Agent和Referer');
    console.log('   🔧 修复: 添加完整的请求头配置');

    issues.push('请求头可能不完整');
    recommendations.push('添加完整的请求头配置');

    return { issues, recommendations };
}

// 生成修复方案
function generateFixPlan(diagnosisResults, codeIssues) {
    console.log('\n\n💡 登录功能修复方案');
    console.log('='.repeat(80));

    console.log('\n🔧 1. API接口修复:');
    console.log('   - 将登录相关接口改为POST请求');
    console.log('   - 使用@FormUrlEncoded和@Field注解');
    console.log('   - 添加必要的请求头');

    console.log('\n🔧 2. 网络配置修复:');
    console.log('   - 配置OkHttp CookieJar');
    console.log('   - 添加网络拦截器');
    console.log('   - 设置正确的User-Agent');

    console.log('\n🔧 3. UI优化:');
    console.log('   - 修改登录对话框按钮样式');
    console.log('   - 使用樱花主题配色');
    console.log('   - 确保按钮文字为纯白色');

    console.log('\n🔧 4. 错误处理优化:');
    console.log('   - 添加详细的错误信息处理');
    console.log('   - 实现重试机制');
    console.log('   - 优化用户体验');

    // 生成具体的代码修复建议
    const fixPlan = {
        apiService: {
            file: 'app/src/main/java/com/example/aimusicplayer/data/source/ApiService.kt',
            changes: [
                '将@GET改为@POST',
                '添加@FormUrlEncoded注解',
                '使用@Field替代@Query',
                '添加Content-Type头'
            ]
        },
        networkModule: {
            file: 'app/src/main/java/com/example/aimusicplayer/di/NetworkModule.kt',
            changes: [
                '配置CookieJar',
                '添加请求头拦截器',
                '设置User-Agent',
                '配置超时时间'
            ]
        },
        loginUI: {
            files: [
                'app/src/main/res/layout/dialog_phone_login.xml',
                'app/src/main/res/layout/dialog_qr_login.xml'
            ],
            changes: [
                '按钮文字颜色改为#FFFFFF',
                '按钮背景使用樱花主题',
                '添加圆角和阴影效果',
                '确保触摸目标≥48dp'
            ]
        }
    };

    return fixPlan;
}

// 主函数
async function main() {
    try {
        console.log('🚀 开始登录功能诊断\n');

        // 1. 诊断API接口
        const diagnosisResults = await diagnoseLoginIssues();

        // 2. 分析代码问题
        const codeIssues = analyzeCodeIssues();

        // 3. 生成修复方案
        const fixPlan = generateFixPlan(diagnosisResults, codeIssues);

        // 4. 保存诊断报告
        const reportPath = './login_diagnosis_report.json';
        const report = {
            timestamp: new Date().toISOString(),
            diagnosis_results: diagnosisResults,
            code_issues: codeIssues,
            fix_plan: fixPlan,
            summary: {
                total_apis_tested: Object.keys(LOGIN_APIS).length,
                main_issues: [
                    'API接口使用GET而非POST请求',
                    '敏感参数通过URL传递',
                    'Cookie管理可能不完善',
                    '请求头配置可能不完整'
                ],
                priority_fixes: [
                    '修改API接口为POST请求',
                    '配置正确的Cookie管理',
                    '优化登录UI样式',
                    '完善错误处理机制'
                ]
            }
        };

        fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
        console.log(`\n📄 详细诊断报告已保存: ${reportPath}`);

        console.log('\n✨ 登录功能诊断完成');
        console.log('\n🎯 下一步: 开始实施修复方案');

    } catch (error) {
        console.error('❌ 诊断过程中发生错误:', error.message);
        process.exit(1);
    }
}

// 运行诊断
if (require.main === module) {
    main();
}

module.exports = { diagnoseLoginIssues, analyzeCodeIssues, generateFixPlan };
