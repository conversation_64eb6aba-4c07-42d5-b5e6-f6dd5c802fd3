<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="item_search_result" modulePackage="com.example.aimusicplayer" filePath="app\src\main\res\layout\item_search_result.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.LinearLayout"><Targets><Target tag="layout/item_search_result_0" view="LinearLayout"><Expressions/><location startLine="1" startOffset="0" endLine="105" endOffset="14"/></Target><Target id="@+id/album_cover" view="ImageView"><Expressions/><location startLine="10" startOffset="4" endLine="16" endOffset="57"/></Target><Target id="@+id/song_title" view="TextView"><Expressions/><location startLine="25" startOffset="8" endLine="33" endOffset="37"/></Target><Target id="@+id/artist_name" view="TextView"><Expressions/><location startLine="42" startOffset="12" endLine="49" endOffset="41"/></Target><Target id="@+id/album_name" view="TextView"><Expressions/><location startLine="58" startOffset="12" endLine="66" endOffset="41"/></Target><Target id="@+id/vip_label" view="TextView"><Expressions/><location startLine="69" startOffset="12" endLine="81" endOffset="43"/></Target><Target id="@+id/song_duration" view="TextView"><Expressions/><location startLine="84" startOffset="12" endLine="91" endOffset="38"/></Target></Targets></Layout>