package com.example.aimusicplayer.viewmodel

import android.util.Log
import androidx.lifecycle.ViewModel
import androidx.lifecycle.asLiveData
import androidx.lifecycle.viewModelScope
import com.example.aimusicplayer.data.repository.UserRepository
import com.example.aimusicplayer.utils.toUnMutable
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * 启动页ViewModel
 * 负责启动页的初始化逻辑和导航控制
 */
@HiltViewModel
class SplashViewModel @Inject constructor(
    private val userRepository: UserRepository,
) : ViewModel() {

    companion object {
        private const val TAG = "SplashViewModel"

        // 导航常量
        const val NAVIGATE_TO_LOGIN = 1
        const val NAVIGATE_TO_MAIN = 2

        // 启动页显示时间
        private const val SPLASH_DELAY = 2000L
    }

    // 导航状态 - 使用懒加载初始化
    private val _navigationEvent by lazy { MutableStateFlow<Int?>(null) }
    val navigationEvent by lazy { _navigationEvent.toUnMutable() }

    // 加载状态 - 使用懒加载初始化
    private val _isLoading by lazy { MutableStateFlow(true) }
    val isLoading by lazy { _isLoading.toUnMutable() }

    // 错误信息 - 使用懒加载初始化
    private val _errorMessage by lazy { MutableStateFlow<String?>(null) }
    val errorMessage by lazy { _errorMessage.toUnMutable() }

    init {
        // 延迟初始化，确保所有StateFlow都已准备好
        viewModelScope.launch {
            delay(50) // 短暂延迟确保初始化完成
            initializeSplash()
        }
    }

    /**
     * 初始化启动页
     * 使用懒加载StateFlow，确保安全初始化
     */
    fun initializeSplash() {
        viewModelScope.launch {
            try {
                Log.d(TAG, "开始初始化启动页")

                // 设置加载状态
                _isLoading.value = true

                // 显示启动页动画
                delay(SPLASH_DELAY)

                // 检查登录状态
                val isLoggedIn = checkLoginStatus()
                Log.d(TAG, "登录状态检查完成: $isLoggedIn")

                // 根据登录状态导航
                if (isLoggedIn) {
                    _navigationEvent.value = NAVIGATE_TO_MAIN
                    Log.d(TAG, "导航到主页面")
                } else {
                    _navigationEvent.value = NAVIGATE_TO_LOGIN
                    Log.d(TAG, "导航到登录页面")
                }
            } catch (e: Exception) {
                Log.e(TAG, "启动页初始化失败", e)
                _errorMessage.value = "初始化失败: ${e.message}"
                // 出错时默认跳转到登录页
                _navigationEvent.value = NAVIGATE_TO_LOGIN
            } finally {
                _isLoading.value = false
                Log.d(TAG, "启动页初始化完成")
            }
        }
    }

    /**
     * 检查登录状态
     */
    private suspend fun checkLoginStatus(): Boolean {
        return try {
            userRepository.isLoggedIn()
        } catch (e: Exception) {
            Log.e(TAG, "检查登录状态失败", e)
            false
        }
    }

    /**
     * 清除导航事件
     */
    fun clearNavigationEvent() {
        _navigationEvent.value = null
    }

    /**
     * 清除错误信息
     */
    fun clearError() {
        _errorMessage.value = null
    }

    /**
     * 获取导航事件 (兼容Java代码)
     */
    fun getNavigationAction() = navigationEvent.asLiveData()
}
