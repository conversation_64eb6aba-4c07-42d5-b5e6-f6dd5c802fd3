package com.example.aimusicplayer.ui.adapter

import android.graphics.Color
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.TextView
import androidx.media3.common.MediaItem
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.ListAdapter
import androidx.recyclerview.widget.RecyclerView
import com.example.aimusicplayer.R
import com.example.aimusicplayer.utils.TimeUtils

/**
 * 播放队列适配器
 * 适配Android Automotive车载环境
 */
class PlayQueueAdapter(
    private val onItemClick: (Int, MediaItem) -> Unit,
    private val onItemLongClick: (Int, MediaItem) -> Unit,
) : ListAdapter<MediaItem, PlayQueueAdapter.ViewHolder>(MediaItemDiffCallback()) {

    private var currentPlayingIndex = -1

    /**
     * 设置当前播放的歌曲索引
     */
    fun setCurrentPlayingIndex(index: Int) {
        val oldIndex = currentPlayingIndex
        currentPlayingIndex = index

        // 只刷新变化的项
        if (oldIndex != -1) {
            notifyItemChanged(oldIndex)
        }
        if (index != -1) {
            notifyItemChanged(index)
        }
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
        val view = LayoutInflater.from(parent.context)
            .inflate(R.layout.item_play_queue, parent, false)
        return ViewHolder(view)
    }

    override fun onBindViewHolder(holder: ViewHolder, position: Int) {
        val mediaItem = getItem(position)
        holder.bind(mediaItem, position, position == currentPlayingIndex)
    }

    inner class ViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        private val indexText: TextView = itemView.findViewById(R.id.tv_song_index)
        private val titleText: TextView = itemView.findViewById(R.id.tv_song_title)
        private val artistText: TextView = itemView.findViewById(R.id.tv_song_artist)
        private val durationText: TextView = itemView.findViewById(R.id.tv_song_duration)
        private val playingIndicator: ImageView = itemView.findViewById(R.id.iv_playing_indicator)
        private val moreButton: ImageView = itemView.findViewById(R.id.iv_more)

        fun bind(mediaItem: MediaItem, position: Int, isCurrentPlaying: Boolean) {
            // 设置索引
            indexText.text = (position + 1).toString()

            // 设置歌曲信息
            titleText.text = mediaItem.mediaMetadata.title ?: "未知歌曲"
            artistText.text = mediaItem.mediaMetadata.artist ?: "未知艺术家"

            // 设置时长
            val duration = mediaItem.mediaMetadata.extras?.getLong("duration") ?: 0L
            durationText.text = if (duration > 0) {
                TimeUtils.formatTime(duration)
            } else {
                "--:--"
            }

            // 设置当前播放状态
            if (isCurrentPlaying) {
                // 当前播放的歌曲
                indexText.visibility = View.GONE
                playingIndicator.visibility = View.VISIBLE
                titleText.setTextColor(Color.parseColor("#1976D2")) // 蓝色
                artistText.setTextColor(Color.parseColor("#1976D2"))
                durationText.setTextColor(Color.parseColor("#1976D2"))

                // 设置播放动画
                playingIndicator.setImageResource(R.drawable.ic_volume_up)
            } else {
                // 普通歌曲
                indexText.visibility = View.VISIBLE
                playingIndicator.visibility = View.GONE
                titleText.setTextColor(Color.parseColor("#FFFFFF")) // 白色
                artistText.setTextColor(Color.parseColor("#CCCCCC")) // 灰色
                durationText.setTextColor(Color.parseColor("#CCCCCC"))
            }

            // 设置点击事件
            itemView.setOnClickListener {
                val currentPosition = bindingAdapterPosition
                if (currentPosition != RecyclerView.NO_POSITION) {
                    onItemClick(currentPosition, mediaItem)
                }
            }

            // 设置长按事件
            itemView.setOnLongClickListener {
                val currentPosition = bindingAdapterPosition
                if (currentPosition != RecyclerView.NO_POSITION) {
                    onItemLongClick(currentPosition, mediaItem)
                }
                true
            }

            // 设置更多按钮点击事件
            moreButton.setOnClickListener {
                val currentPosition = bindingAdapterPosition
                if (currentPosition != RecyclerView.NO_POSITION) {
                    onItemLongClick(currentPosition, mediaItem)
                }
            }

            // 车载适配：增大触摸目标
            itemView.minimumHeight = itemView.context.resources.getDimensionPixelSize(R.dimen.touch_target_size)
        }
    }

    /**
     * DiffUtil回调
     */
    private class MediaItemDiffCallback : DiffUtil.ItemCallback<MediaItem>() {
        override fun areItemsTheSame(oldItem: MediaItem, newItem: MediaItem): Boolean {
            return oldItem.mediaId == newItem.mediaId
        }

        override fun areContentsTheSame(oldItem: MediaItem, newItem: MediaItem): Boolean {
            return oldItem.mediaMetadata.title == newItem.mediaMetadata.title &&
                oldItem.mediaMetadata.artist == newItem.mediaMetadata.artist
        }
    }
}
