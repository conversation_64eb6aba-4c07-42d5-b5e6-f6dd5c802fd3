{"timestamp": "2025-05-26T12:58:19.910Z", "summary": {"total": 18, "passed": 12, "failed": 6, "primarySuccess": 6, "backupSuccess": 6}, "loginTests": {"guestLogin": {"primary": {"success": false, "responseTime": 42, "statusCode": 502, "error": "HTTP状态码错误: 502", "data": null, "dataSize": 556, "rawData": "<html>\r\n<head><title>502 Bad Gateway</title></head>\r\n<body>\r\n<center><h1>502 Bad Gateway</h1></center>\r\n<hr><center>openresty</center>\r\n</body>\r\n</html>\r\n<!-- a padding to disable MSIE and Chrome frie", "validation": {"valid": false, "message": "HTTP状态码错误: 502"}, "server": "primary", "hostname": "ncm.zhenxin.me", "apiKey": "guest<PERSON><PERSON><PERSON>", "description": "游客登录接口"}, "backup": {"success": true, "responseTime": 3695, "statusCode": 200, "error": null, "data": {"code": 200, "userId": 9861600993, "createTime": 1714442358306, "cookie": "MUSIC_R_T=0; Max-Age=2147483647; Expires=Sat, 13 Jun 2093 16:12:37 GMT; Path=/wapi/clientlog; HTTPOnly;MUSIC_A=0048F8378FE5F149101F60A7AF7682822A9578C6A9B5A1CB1B229E94C077547192E4616B667026D10447C3A9E2AAAD5D845600ACE9091FA476BCA7AE98756F2D057E3C1176D48583097796AF1889D8BAA3A19850B0E690E4E9BD55FDAA7D4F398F17E70212761D98531B15D5FE26FA8F8CB92FB73E705DFC6EF3DEB45F96F013FBF734886D99C9346D0E476D29185A058E5475CC502AF7BB9AAA16ED1B31EDC5B4719C92588ABB85937CDFA5BEF83AFFD111560243AE97B032A2D63CC7B34D71D8D70DB5DF0CF2A5A4A6DE8EC2DE05A34401EF02DBAD0CA8F8B487C2F43675C744EFB75650A00C3EBEFA12BD987C498F52FF00D69020B8BE510A530FC07DB4CE25EB289F7993987883BC63778012F9D1B21D30690FCA50DDC15F71CA579F50BC179B32CF57024F22A21DEC220717A3E3D2ACECE967065FEEF7D22427FDC25B6044386DDB8C275B742ADB147FEDBFE2A440E250FF57B22EBB9958E8CB44E881A578C7E549D9AE3A8D1A95A4C044325A65E8439688ACB7F1E1AEFD5212F6F4C041696DC3421D3B0B68201379BAA5EFC22CCC85DB7CE85DC7EBD55A5DDE568BBB8764A1A76A3970F4A98DC75A632BD8BC993F; Max-Age=2147483647; Expires=Sat, 13 Jun 2093 16:12:37 GMT; Path=/; HTTPOnly;MUSIC_R_T=0; Max-Age=2147483647; Expires=Sat, 13 Jun 2093 16:12:37 GMT; Path=/neapi/feedback; HTTPOnly;MUSIC_A_T=1714442358306; Max-Age=2147483647; Expires=Sat, 13 Jun 2093 16:12:37 GMT; Path=/api/feedback; HTTPOnly;__csrf=5e44f8e14611ebdb67ea7f0f11a0f80c; Max-Age=1296010; Expires=Tue, 10 Jun 2025 12:58:40 GMT; Path=/;;MUSIC_R_T=0; Max-Age=2147483647; Expires=Sat, 13 Jun 2093 16:12:37 GMT; Path=/api/clientlog; HTTPOnly;MUSIC_A_T=1714442358306; Max-Age=2147483647; Expires=Sat, 13 Jun 2093 16:12:37 GMT; Path=/eapi/clientlog; HTTPOnly;MUSIC_A_T=1714442358306; Max-Age=2147483647; Expires=Sat, 13 Jun 2093 16:12:37 GMT; Path=/wapi/feedback; HTTPOnly;MUSIC_A_T=1714442358306; Max-Age=2147483647; Expires=Sat, 13 Jun 2093 16:12:37 GMT; Path=/api/clientlog; HTTPOnly;MUSIC_A_T=1714442358306; Max-Age=2147483647; Expires=Sat, 13 Jun 2093 16:12:37 GMT; Path=/eapi/feedback; HTTPOnly;MUSIC_A_T=1714442358306; Max-Age=2147483647; Expires=Sat, 13 Jun 2093 16:12:37 GMT; Path=/neapi/clientlog; HTTPOnly;MUSIC_R_T=0; Max-Age=2147483647; Expires=Sat, 13 Jun 2093 16:12:37 GMT; Path=/openapi/clientlog; HTTPOnly;MUSIC_R_T=0; Max-Age=2147483647; Expires=Sat, 13 Jun 2093 16:12:37 GMT; Path=/wapi/feedback; HTTPOnly;MUSIC_A_T=1714442358306; Max-Age=2147483647; Expires=Sat, 13 Jun 2093 16:12:37 GMT; Path=/weapi/feedback; HTTPOnly;MUSIC_R_T=0; Max-Age=2147483647; Expires=Sat, 13 Jun 2093 16:12:37 GMT; Path=/eapi/feedback; HTTPOnly;MUSIC_A_T=1714442358306; Max-Age=2147483647; Expires=Sat, 13 Jun 2093 16:12:37 GMT; Path=/wapi/clientlog; HTTPOnly;MUSIC_R_T=0; Max-Age=2147483647; Expires=Sat, 13 Jun 2093 16:12:37 GMT; Path=/eapi/clientlog; HTTPOnly;MUSIC_A_T=1714442358306; Max-Age=2147483647; Expires=Sat, 13 Jun 2093 16:12:37 GMT; Path=/openapi/clientlog; HTTPOnly;MUSIC_R_T=0; Max-Age=2147483647; Expires=Sat, 13 Jun 2093 16:12:37 GMT; Path=/api/feedback; HTTPOnly;MUSIC_R_T=0; Max-Age=2147483647; Expires=Sat, 13 Jun 2093 16:12:37 GMT; Path=/neapi/clientlog; HTTPOnly;MUSIC_A_T=1714442358306; Max-Age=2147483647; Expires=Sat, 13 Jun 2093 16:12:37 GMT; Path=/neapi/feedback; HTTPOnly;MUSIC_SNS=; Max-Age=0; Expires=Mon, 26 May 2025 12:58:30 GMT; Path=/;MUSIC_R_T=0; Max-Age=2147483647; Expires=Sat, 13 Jun 2093 16:12:37 GMT; Path=/weapi/feedback; HTTPOnly;MUSIC_A_T=1714442358306; Max-Age=2147483647; Expires=Sat, 13 Jun 2093 16:12:37 GMT; Path=/weapi/clientlog; HTTPOnly;MUSIC_R_T=0; Max-Age=2147483647; Expires=Sat, 13 Jun 2093 16:12:37 GMT; Path=/weapi/clientlog; HTTPOnly"}, "dataSize": 3594, "structureValid": true, "validation": {"valid": true, "message": "游客登录成功", "hasCookie": true}, "server": "backup", "hostname": "**********-4499wupl9z.ap-guangzhou.tencentscf.com", "apiKey": "guest<PERSON><PERSON><PERSON>", "description": "游客登录接口"}}, "qrKey": {"primary": {"success": true, "responseTime": 166, "statusCode": 200, "error": null, "data": {"data": {"code": 200, "unikey": "37ce2b3c-543b-48fa-8d44-d9d5fe14a566"}, "code": 200}, "dataSize": 80, "structureValid": true, "validation": {"valid": true, "message": "获取二维码key成功", "key": "37ce2b3c-543b-48fa-8d44-d9d5fe14a566"}, "server": "primary", "hostname": "ncm.zhenxin.me", "apiKey": "qr<PERSON><PERSON>", "description": "获取二维码key"}, "backup": {"success": false, "responseTime": 243, "statusCode": 400, "error": "HTTP状态码错误: 400", "data": {"code": -462, "data": {"actionCode": null, "verifyType": 40, "verifyId": 1007602, "verifyUrl": "https://st.music.163.com/encrypt-pages", "blockText": "验证成功后，可进行下一步操作哦~", "verifyToken": "00.40.e8273fbdcb0bfc44bc17faef4f1fb569.339028226", "btnText": "", "orpheusUrl": "", "frontRuleIds": null, "params": {"event_id": "00635097416954302488", "sign": "b500a2731f2577cf015a9290c4efed21"}, "url": "https://st.music.163.com/encrypt-pages?params=%7B%22sign%22%3A%22b500a2731f2577cf015a9290c4efed21%22%2C%22event_id%22%3A%2200635097416954302488%22%7D&verifyType=40&verifyId=1007602&verifyToken=00.40.e8273fbdcb0bfc44bc17faef4f1fb569.339028226", "urlAutoOpen": 0, "orpheusUrlParamAppend": null}, "message": "请完成验证操作"}, "dataSize": 675, "validation": {"valid": false, "message": "HTTP状态码错误: 400"}, "server": "backup", "hostname": "**********-4499wupl9z.ap-guangzhou.tencentscf.com", "apiKey": "qr<PERSON><PERSON>", "description": "获取二维码key"}}, "qrCreate": {"primary": {"success": true, "responseTime": 48, "statusCode": 200, "error": null, "data": {"code": 200, "data": {"qrurl": "https://music.163.com/login?codekey=test", "qrimg": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAJQAAACUCAYAAAB1PADUAAAAAklEQVR4AewaftIAAATfSURBVO3BQY4cSRIEQdNA/f/Lun30UwCJ9GpyuCaCP1K15KRq0UnVopOqRSdVi06qFp1ULTqpWnRSteikatFJ1aKTqkUnVYtOqhadVC06qVr0yUtAfpOaGyA3aiYgk5oJyKRmArJJzQTkN6l546Rq0UnVopOqRZ8sU7MJyBtqJiCTmieA3Kh5AsgTajYB2XRSteikatFJ1aJPvgzIE2qeAHID5AbIpGZSMwF5Asg3AXlCzTedVC06qVp0UrXok3+MmgnIjZoJyI2aCcgEZFIzAZnU/EtOqhadVC06qVr0yT9OzQTkRs0TaiYgE5D/JydVi06qFp1ULfrky9T8JiCTmhsgk5oJyBtqboBMap5Q8zc5qVp0UrXopGrRJ8uA/ElqJiCTmgnIE2omIJOaCcik5g0gf7OTqkUnVYtOqhbhj/yHAXlDzQ2QGzVvAJnU/JedVC06qVp0UrXok5eATGomIJvUTGomIG8AuVEzAblRMwGZ1NwA2aTmm06qFp1ULTqpWvTJMiBvqHkCyBtAJjUTkCfUTEBugGxS8wSQSc0bJ1WLTqoWnVQtwh9ZBORGzQ2Qb1JzA2RSMwF5Qs0NkBs1E5AbNX/SSdWik6pFJ1WLPnkJyKRmAnIDZFLzBJBJzQTkBsikZgLyhJobIDdqJiCTmhsgk5oJyKRm00nVopOqRSdVi/BHXgDym9TcALlRcwPkCTUTkEnNBGRSMwGZ1DwB5EbNN51ULTqpWnRStQh/5A8CcqPmBsikZgIyqXkCyKRmAvI3UfMEkEnNGydVi06qFp1ULfrkJSCTmgnIpOYJIJOaN4DcqLkBcqNmAjKpmYBMaiYgN2omIDdqJjWbTqoWnVQtOqla9MkvAzKpeQLIpGYCMql5Q80baiYgk5oJyI2aCcik5gbIpGbTSdWik6pFJ1WLPlkGZBOQSc0EZFIzAblRswnIpGZS84SaCcikZgJyo+abTqoWnVQtOqla9MkyNTdAnlDzBJAbNTdAJjU3QG6APKFmAnIDZFIzAbkBMql546Rq0UnVopOqRZ/8MjU3QG7UPKHmDSBvqHkCyKRmArJJzaaTqkUnVYtOqhZ98mVA3lBzo2YCMgGZ1ExAbtRMQG7UTECeULNJzQRkUrPppGrRSdWik6pFn3yZmieATEDeUPOEmgnIjZoJyBtANgG5ATKpeeOkatFJ1aKTqkWfvKRmk5ongExqJiCTmifU3AC5UXMDZFLzBJAJyJ90UrXopGrRSdWiT14C8pvUTGpu1GwCMqmZgNwAeQLIpOYNNd90UrXopGrRSdWiT5ap2QTkBsik5gbIpOYGyA2QSc2NmgnIjZon1NwAmdRsOqladFK16KRq0SdfBuQJNb8JyKRmUjMBmdRMQCY1TwDZBOQGyKTmjZOqRSdVi06qFn1SV0AmNROQSc0E5A01E5BJzQTkTzqpWnRSteikatEn/xggN2pugExqJiA3QG7UPAHkBsgbajadVC06qVp0UrXoky9T801qboA8oeZGzQRkUnMD5EbNpGYC8oSaCcg3nVQtOqladFK1CH/kBSC/Sc0EZFKzCcik5gbIpOYJIE+ouQEyqfmmk6pFJ1WLTqoW4Y9ULTmpWnRSteikatFJ1aKTqkUnVYtOqhadVC06qVp0UrXopGrRSdWik6pFJ1WLTqoW/Q+bjjZXr3S9BwAAAABJRU5ErkJggg=="}}, "dataSize": 1865, "structureValid": true, "validation": {"valid": true, "message": "生成二维码成功", "hasQrImg": true}, "server": "primary", "hostname": "ncm.zhenxin.me", "apiKey": "qrCreate", "description": "生成二维码"}, "backup": {"success": true, "responseTime": 106, "statusCode": 200, "error": null, "data": {"code": 200, "data": {"qrurl": "https://music.163.com/login?codekey=test", "qrimg": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAJQAAACUCAYAAAB1PADUAAAAAklEQVR4AewaftIAAATfSURBVO3BQY4cSRIEQdNA/f/Lun30UwCJ9GpyuCaCP1K15KRq0UnVopOqRSdVi06qFp1ULTqpWnRSteikatFJ1aKTqkUnVYtOqhadVC06qVr0yUtAfpOaGyA3aiYgk5oJyKRmArJJzQTkN6l546Rq0UnVopOqRZ8sU7MJyBtqJiCTmieA3Kh5AsgTajYB2XRSteikatFJ1aJPvgzIE2qeAHID5AbIpGZSMwF5Asg3AXlCzTedVC06qVp0UrXok3+MmgnIjZoJyI2aCcgEZFIzAZnU/EtOqhadVC06qVr0yT9OzQTkRs0TaiYgE5D/JydVi06qFp1ULfrky9T8JiCTmhsgk5oJyBtqboBMap5Q8zc5qVp0UrXopGrRJ8uA/ElqJiCTmgnIE2omIJOaCcik5g0gf7OTqkUnVYtOqhbhj/yHAXlDzQ2QGzVvAJnU/JedVC06qVp0UrXok5eATGomIJvUTGomIG8AuVEzAblRMwGZ1NwA2aTmm06qFp1ULTqpWvTJMiBvqHkCyBtAJjUTkCfUTEBugGxS8wSQSc0bJ1WLTqoWnVQtwh9ZBORGzQ2Qb1JzA2RSMwF5Qs0NkBs1E5AbNX/SSdWik6pFJ1WLPnkJyKRmAnIDZFLzBJBJzQTkBsikZgLyhJobIDdqJiCTmhsgk5oJyKRm00nVopOqRSdVi/BHXgDym9TcALlRcwPkCTUTkEnNBGRSMwGZ1DwB5EbNN51ULTqpWnRStQh/5A8CcqPmBsikZgIyqXkCyKRmAvI3UfMEkEnNGydVi06qFp1ULfrkJSCTmgnIpOYJIJOaN4DcqLkBcqNmAjKpmYBMaiYgN2omIDdqJjWbTqoWnVQtOqla9MkvAzKpeQLIpGYCMql5Q80baiYgk5oJyI2aCcik5gbIpGbTSdWik6pFJ1WLPlkGZBOQSc0EZFIzAblRswnIpGZS84SaCcikZgJyo+abTqoWnVQtOqla9MkyNTdAnlDzBJAbNTdAJjU3QG6APKFmAnIDZFIzAbkBMql546Rq0UnVopOqRZ/8MjU3QG7UPKHmDSBvqHkCyKRmArJJzaaTqkUnVYtOqhZ98mVA3lBzo2YCMgGZ1ExAbtRMQG7UTECeULNJzQRkUrPppGrRSdWik6pFn3yZmieATEDeUPOEmgnIjZoJyBtANgG5ATKpeeOkatFJ1aKTqkWfvKRmk5ongExqJiCTmifU3AC5UXMDZFLzBJAJyJ90UrXopGrRSdWiT14C8pvUTGpu1GwCMqmZgNwAeQLIpOYNNd90UrXopGrRSdWiT5ap2QTkBsik5gbIpOYGyA2QSc2NmgnIjZon1NwAmdRsOqladFK16KRq0SdfBuQJNb8JyKRmUjMBmdRMQCY1TwDZBOQGyKTmjZOqRSdVi06qFn1SV0AmNROQSc0E5A01E5BJzQTkTzqpWnRSteikatEn/xggN2pugExqJiA3QG7UPAHkBsgbajadVC06qVp0UrXoky9T801qboA8oeZGzQRkUnMD5EbNpGYC8oSaCcg3nVQtOqladFK1CH/kBSC/Sc0EZFKzCcik5gbIpOYJIE+ouQEyqfmmk6pFJ1WLTqoW4Y9ULTmpWnRSteikatFJ1aKTqkUnVYtOqhadVC06qVp0UrXopGrRSdWik6pFJ1WLTqoW/Q+bjjZXr3S9BwAAAABJRU5ErkJggg=="}}, "dataSize": 1865, "structureValid": true, "validation": {"valid": true, "message": "生成二维码成功", "hasQrImg": true}, "server": "backup", "hostname": "**********-4499wupl9z.ap-guangzhou.tencentscf.com", "apiKey": "qrCreate", "description": "生成二维码"}}, "qrCheck": {"primary": {"success": true, "responseTime": 162, "statusCode": 200, "error": null, "data": {"code": 800, "message": "二维码不存在或已过期", "cookie": "NMTID=00O-LOeUymBR208KkgMuzuaog2czswAAAGXDKw59A; Max-Age=315360000; Expires=Thu, 24 May 2035 12:58:23 GMT; Path=/;"}, "dataSize": 161, "structureValid": true, "validation": {"valid": true, "message": "二维码过期", "status": 800}, "server": "primary", "hostname": "ncm.zhenxin.me", "apiKey": "qr<PERSON><PERSON><PERSON>", "description": "检查二维码状态"}, "backup": {"success": true, "responseTime": 237, "statusCode": 200, "error": null, "data": {"code": 800, "message": "二维码不存在或已过期", "cookie": "NMTID=00O9O0moP7vbAbchEyxnETUSmlUxhoAAAGXDKxdDA; Max-Age=315360000; Expires=Thu, 24 May 2035 12:58:32 GMT; Path=/;"}, "dataSize": 161, "structureValid": true, "validation": {"valid": true, "message": "二维码过期", "status": 800}, "server": "backup", "hostname": "**********-4499wupl9z.ap-guangzhou.tencentscf.com", "apiKey": "qr<PERSON><PERSON><PERSON>", "description": "检查二维码状态"}}, "captchaSent": {"primary": {"success": true, "responseTime": 225, "statusCode": 200, "error": null, "data": {"code": 400, "message": "当天发送验证码的条数超过限制", "data": false}, "dataSize": 52, "structureValid": true, "validation": {"valid": true, "message": "验证码发送限制（正常业务逻辑）", "businessLogic": true}, "server": "primary", "hostname": "ncm.zhenxin.me", "apiKey": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "发送验证码"}, "backup": {"success": true, "responseTime": 313, "statusCode": 200, "error": null, "data": {"code": 400, "message": "当天发送验证码的条数超过限制", "data": false}, "dataSize": 52, "structureValid": true, "validation": {"valid": true, "message": "验证码发送限制（正常业务逻辑）", "businessLogic": true}, "server": "backup", "hostname": "**********-4499wupl9z.ap-guangzhou.tencentscf.com", "apiKey": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "发送验证码"}}, "captchaVerify": {"primary": {"success": false, "responseTime": 174, "statusCode": 503, "error": "HTTP状态码错误: 503", "data": {"message": "验证码错误", "code": 503, "data": false}, "dataSize": 43, "validation": {"valid": false, "message": "HTTP状态码错误: 503"}, "server": "primary", "hostname": "ncm.zhenxin.me", "apiKey": "captchaVerify", "description": "验证验证码"}, "backup": {"success": false, "responseTime": 140, "statusCode": 503, "error": "HTTP状态码错误: 503", "data": {"message": "验证码错误", "code": 503, "data": false}, "dataSize": 43, "validation": {"valid": false, "message": "HTTP状态码错误: 503"}, "server": "backup", "hostname": "**********-4499wupl9z.ap-guangzhou.tencentscf.com", "apiKey": "captchaVerify", "description": "验证验证码"}}, "loginCellphone": {"primary": {"success": false, "responseTime": 162, "statusCode": 503, "error": "HTTP状态码错误: 503", "data": {"msg": "验证码错误", "code": 503, "message": "验证码错误"}, "dataSize": 44, "validation": {"valid": false, "message": "HTTP状态码错误: 503"}, "server": "primary", "hostname": "ncm.zhenxin.me", "apiKey": "loginCellphone", "description": "手机号登录"}, "backup": {"success": false, "responseTime": 171, "statusCode": 400, "error": "HTTP状态码错误: 400", "data": {"code": -462, "data": {"actionCode": null, "verifyType": 40, "verifyId": 1007602, "verifyUrl": "https://st.music.163.com/encrypt-pages", "blockText": "验证成功后，可进行下一步操作哦~", "verifyToken": "00.40.b7028052c3df6f4595763633a5d6b7e2.**********", "btnText": "", "orpheusUrl": "", "frontRuleIds": null, "params": {"event_id": "00635097435749277723", "sign": "4298f26e8cd6f77309ec95d1341d7efe"}, "url": "https://st.music.163.com/encrypt-pages?params=%7B%22sign%22%3A%224298f26e8cd6f77309ec95d1341d7efe%22%2C%22event_id%22%3A%2200635097435749277723%22%7D&verifyType=40&verifyId=1007602&verifyToken=00.40.b7028052c3df6f4595763633a5d6b7e2.**********", "urlAutoOpen": 0, "orpheusUrlParamAppend": null}, "message": "请完成验证操作"}, "dataSize": 677, "validation": {"valid": false, "message": "HTTP状态码错误: 400"}, "server": "backup", "hostname": "**********-4499wupl9z.ap-guangzhou.tencentscf.com", "apiKey": "loginCellphone", "description": "手机号登录"}}, "loginStatus": {"primary": {"success": true, "responseTime": 168, "statusCode": 200, "error": null, "data": {"data": {"code": 200, "account": null, "profile": null}}, "dataSize": 51, "structureValid": true, "validation": {"valid": true, "message": "登录状态检查成功", "hasAccount": false}, "server": "primary", "hostname": "ncm.zhenxin.me", "apiKey": "loginStatus", "description": "检查登录状态"}, "backup": {"success": true, "responseTime": 152, "statusCode": 200, "error": null, "data": {"data": {"code": 200, "account": null, "profile": null}}, "dataSize": 51, "structureValid": true, "validation": {"valid": true, "message": "登录状态检查成功", "hasAccount": false}, "server": "backup", "hostname": "**********-4499wupl9z.ap-guangzhou.tencentscf.com", "apiKey": "loginStatus", "description": "检查登录状态"}}, "userAccount": {"primary": {"success": true, "responseTime": 166, "statusCode": 200, "error": null, "data": {"code": 200, "account": null, "profile": null}, "dataSize": 42, "structureValid": true, "validation": {"valid": true, "message": "获取用户账号信息成功", "hasAccount": false}, "server": "primary", "hostname": "ncm.zhenxin.me", "apiKey": "userAccount", "description": "获取用户账号信息"}, "backup": {"success": true, "responseTime": 147, "statusCode": 200, "error": null, "data": {"code": 200, "account": null, "profile": null}, "dataSize": 42, "structureValid": true, "validation": {"valid": true, "message": "获取用户账号信息成功", "hasAccount": false}, "server": "backup", "hostname": "**********-4499wupl9z.ap-guangzhou.tencentscf.com", "apiKey": "userAccount", "description": "获取用户账号信息"}}}}