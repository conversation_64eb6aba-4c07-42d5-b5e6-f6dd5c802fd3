<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginHorizontal="4dp"
    android:layout_marginVertical="2dp"
    android:clickable="true"
    android:focusable="true"
    android:foreground="?attr/selectableItemBackground"
    app:cardCornerRadius="8dp"
    app:cardElevation="2dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:padding="16dp">

        <!-- 选择框 -->
        <CheckBox
            android:id="@+id/checkboxSelect"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:layout_marginEnd="12dp"
            android:visibility="gone" />

        <!-- 歌曲信息 -->
        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="vertical">

            <!-- 歌曲名称 -->
            <TextView
                android:id="@+id/textSongName"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:ellipsize="end"
                android:maxLines="1"
                android:textColor="?android:attr/textColorPrimary"
                android:textSize="16sp"
                android:textStyle="bold"
                tools:text="歌曲名称" />

            <!-- 文件大小和缓存时间 -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="4dp"
                android:orientation="horizontal">

                <TextView
                    android:id="@+id/textFileSize"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:textColor="?android:attr/textColorSecondary"
                    android:textSize="14sp"
                    tools:text="4.2MB" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginHorizontal="8dp"
                    android:text="•"
                    android:textColor="?android:attr/textColorSecondary"
                    android:textSize="14sp" />

                <TextView
                    android:id="@+id/textCacheTime"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:textColor="?android:attr/textColorSecondary"
                    android:textSize="14sp"
                    tools:text="2小时前" />

            </LinearLayout>

            <!-- 最后访问时间 -->
            <TextView
                android:id="@+id/textLastAccess"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="2dp"
                android:textColor="?android:attr/textColorTertiary"
                android:textSize="12sp"
                tools:text="最后播放: 1小时前" />

        </LinearLayout>

        <!-- 删除按钮 -->
        <com.google.android.material.button.MaterialButton
            android:id="@+id/buttonDelete"
            style="@style/Widget.Material3.Button.IconButton"
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:layout_gravity="center_vertical"
            android:layout_marginStart="8dp"
            app:icon="@android:drawable/ic_menu_delete"
            app:iconSize="20dp"
            app:iconTint="?android:attr/textColorSecondary" />

    </LinearLayout>

</com.google.android.material.card.MaterialCardView>
