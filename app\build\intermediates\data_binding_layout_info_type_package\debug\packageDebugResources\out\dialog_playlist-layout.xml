<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="dialog_playlist" modulePackage="com.example.aimusicplayer" filePath="app\src\main\res\layout\dialog_playlist.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.LinearLayout"><Targets><Target tag="layout/dialog_playlist_0" view="LinearLayout"><Expressions/><location startLine="1" startOffset="0" endLine="143" endOffset="14"/></Target><Target id="@+id/title_container" view="LinearLayout"><Expressions/><location startLine="18" startOffset="8" endLine="43" endOffset="22"/></Target><Target id="@+id/text_playlist_title" view="TextView"><Expressions/><location startLine="26" startOffset="12" endLine="33" endOffset="57"/></Target><Target id="@+id/text_playlist_count" view="TextView"><Expressions/><location startLine="35" startOffset="12" endLine="42" endOffset="37"/></Target><Target id="@+id/button_playlist_close" view="ImageButton"><Expressions/><location startLine="45" startOffset="8" endLine="55" endOffset="42"/></Target><Target id="@+id/recycler_view_playlist" view="androidx.recyclerview.widget.RecyclerView"><Expressions/><location startLine="59" startOffset="4" endLine="69" endOffset="48"/></Target><Target id="@+id/text_empty_playlist" view="LinearLayout"><Expressions/><location startLine="72" startOffset="4" endLine="105" endOffset="18"/></Target><Target id="@+id/button_clear_playlist" view="Button"><Expressions/><location startLine="115" startOffset="8" endLine="126" endOffset="37"/></Target><Target id="@+id/button_shuffle_playlist" view="Button"><Expressions/><location startLine="128" startOffset="8" endLine="139" endOffset="37"/></Target></Targets></Layout>