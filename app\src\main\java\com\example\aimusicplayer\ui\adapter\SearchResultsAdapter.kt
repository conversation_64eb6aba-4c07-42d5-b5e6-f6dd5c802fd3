package com.example.aimusicplayer.ui.adapter

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.TextView
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.ListAdapter
import androidx.recyclerview.widget.RecyclerView
import com.bumptech.glide.Glide
import com.example.aimusicplayer.R
import com.example.aimusicplayer.data.model.Song

/**
 * 搜索结果适配器
 */
class SearchResultsAdapter(
    private val onSongClick: (Song) -> Unit,
) : ListAdapter<Song, SearchResultsAdapter.SearchResultViewHolder>(DiffCallback) {

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): SearchResultViewHolder {
        val view = LayoutInflater.from(parent.context)
            .inflate(R.layout.item_search_result, parent, false)
        return SearchResultViewHolder(view)
    }

    override fun onBindViewHolder(holder: SearchResultViewHolder, position: Int) {
        holder.bind(getItem(position))
    }

    inner class SearchResultViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        private val albumCover: ImageView = itemView.findViewById(R.id.album_cover)
        private val songTitle: TextView = itemView.findViewById(R.id.song_title)
        private val artistName: TextView = itemView.findViewById(R.id.artist_name)
        private val albumName: TextView = itemView.findViewById(R.id.album_name)
        private val vipLabel: TextView = itemView.findViewById(R.id.vip_label)
        private val songDuration: TextView = itemView.findViewById(R.id.song_duration)

        fun bind(song: Song) {
            songTitle.text = song.name
            artistName.text = song.getArtistNames()
            albumName.text = song.al?.name ?: ""

            // 显示歌曲时长
            songDuration.text = formatDuration(song.dt)

            // 显示VIP标签（根据歌曲信息判断）
            val isVip = song.fee == 1 || song.fee == 4 // 1=VIP歌曲，4=付费歌曲
            vipLabel.visibility = if (isVip) View.VISIBLE else View.GONE

            // 加载专辑封面
            val coverUrl = song.getAlbumCoverUrl()
            Glide.with(itemView.context)
                .load(coverUrl)
                .placeholder(R.drawable.default_album_art)
                .error(R.drawable.default_album_art)
                .centerCrop()
                .into(albumCover)

            itemView.setOnClickListener {
                onSongClick(song)
            }
        }

        /**
         * 格式化时长
         * @param duration 时长（毫秒）
         * @return 格式化后的时长字符串（mm:ss）
         */
        private fun formatDuration(duration: Long): String {
            val totalSeconds = duration / 1000
            val minutes = totalSeconds / 60
            val seconds = totalSeconds % 60
            return String.format("%02d:%02d", minutes, seconds)
        }
    }

    companion object {
        private val DiffCallback = object : DiffUtil.ItemCallback<Song>() {
            override fun areItemsTheSame(oldItem: Song, newItem: Song): Boolean {
                return oldItem.id == newItem.id
            }

            override fun areContentsTheSame(oldItem: Song, newItem: Song): Boolean {
                return oldItem == newItem
            }
        }
    }
}
