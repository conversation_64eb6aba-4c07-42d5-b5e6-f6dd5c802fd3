<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/dialog_background"
    android:orientation="vertical"
    android:padding="0dp">

    <!-- 标题栏 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/dialog_header_background"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        android:paddingHorizontal="20dp"
        android:paddingVertical="16dp">

        <!-- 标题 -->
        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="播放列表"
            android:textColor="#FFFFFF"
            android:textSize="18sp"
            android:textStyle="bold" />

        <!-- 歌曲数量 -->
        <TextView
            android:id="@+id/tv_song_count"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="16dp"
            android:textColor="#CCCCCC"
            android:textSize="14sp"
            tools:text="(15首)" />

        <!-- 关闭按钮 -->
        <ImageView
            android:id="@+id/iv_close"
            android:layout_width="@dimen/touch_target_size"
            android:layout_height="@dimen/touch_target_size"
            android:background="?android:attr/selectableItemBackgroundBorderless"
            android:padding="12dp"
            android:src="@drawable/ic_close"
            android:tint="#FFFFFF" />

    </LinearLayout>

    <!-- 操作按钮栏 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/dialog_action_background"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        android:paddingHorizontal="20dp"
        android:paddingVertical="12dp">

        <!-- 播放模式按钮 -->
        <LinearLayout
            android:id="@+id/ll_play_mode"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:background="?android:attr/selectableItemBackground"
            android:gravity="center"
            android:minHeight="@dimen/touch_target_size"
            android:orientation="horizontal"
            android:padding="8dp">

            <ImageView
                android:id="@+id/iv_play_mode"
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:src="@drawable/ic_repeat"
                android:tint="#FFFFFF" />

            <TextView
                android:id="@+id/tv_play_mode"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="8dp"
                android:text="列表循环"
                android:textColor="#FFFFFF"
                android:textSize="14sp" />

        </LinearLayout>

        <!-- 随机播放按钮 -->
        <TextView
            android:id="@+id/btn_shuffle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="16dp"
            android:background="@drawable/button_primary_background"
            android:drawableStart="@drawable/ic_shuffle"
            android:drawablePadding="8dp"
            android:drawableTint="#FFFFFF"
            android:gravity="center"
            android:minHeight="@dimen/touch_target_size"
            android:paddingHorizontal="16dp"
            android:paddingVertical="8dp"
            android:text="随机播放"
            android:textColor="#FFFFFF"
            android:textSize="14sp" />

        <!-- 清空列表按钮 -->
        <TextView
            android:id="@+id/btn_clear"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="12dp"
            android:background="@drawable/button_secondary_background"
            android:drawableStart="@drawable/ic_clear_all"
            android:drawablePadding="8dp"
            android:drawableTint="#FFFFFF"
            android:gravity="center"
            android:minHeight="@dimen/touch_target_size"
            android:paddingHorizontal="16dp"
            android:paddingVertical="8dp"
            android:text="清空"
            android:textColor="#FFFFFF"
            android:textSize="14sp" />

    </LinearLayout>

    <!-- 播放列表 -->
    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1">

        <!-- 列表 -->
        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rv_play_queue"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@color/dialog_content_background"
            android:clipToPadding="false"
            android:paddingBottom="16dp"
            android:scrollbars="vertical"
            tools:listitem="@layout/item_play_queue" />

        <!-- 空状态 -->
        <LinearLayout
            android:id="@+id/ll_empty_state"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:gravity="center"
            android:orientation="vertical"
            android:visibility="gone"
            tools:visibility="visible">

            <ImageView
                android:layout_width="64dp"
                android:layout_height="64dp"
                android:alpha="0.5"
                android:src="@drawable/ic_queue_music"
                android:tint="#CCCCCC" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:text="播放列表为空"
                android:textColor="#CCCCCC"
                android:textSize="16sp" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:text="去搜索一些喜欢的歌曲吧"
                android:textColor="#999999"
                android:textSize="14sp" />

        </LinearLayout>

    </FrameLayout>

</LinearLayout>
