package com.example.aimusicplayer.data.cache

import android.content.Context
import android.util.Log
import com.example.aimusicplayer.data.model.Song
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import okhttp3.OkHttpClient
import okhttp3.Request
import java.io.File
import java.io.FileInputStream
import java.io.FileOutputStream
import java.security.MessageDigest
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 音乐文件缓存管理器
 * 实现LRU缓存策略，支持自动缓存和手动缓存
 *
 * 功能特性：
 * - LRU缓存策略，限制缓存大小（默认500MB）
 * - 自动缓存播放中的歌曲
 * - 手动缓存指定歌曲
 * - 缓存文件完整性校验
 * - 缓存统计和管理
 */
@Singleton
class MusicFileCache @Inject constructor(
    @ApplicationContext private val context: Context,
    private val okHttpClient: OkHttpClient,
) {
    companion object {
        private const val TAG = "MusicFileCache"
        private const val CACHE_DIR_NAME = "music_cache"
        private const val DEFAULT_MAX_CACHE_SIZE = 500L * 1024 * 1024 // 500MB
        private const val CACHE_INFO_FILE = "cache_info.json"
        private const val CHUNK_SIZE = 8192 // 8KB chunks for downloading
    }

    // 缓存目录
    private val cacheDir: File by lazy {
        File(context.cacheDir, CACHE_DIR_NAME).apply {
            if (!exists()) {
                mkdirs()
            }
        }
    }

    // 内存中的缓存信息（使用LinkedHashMap实现LRU）
    private val cacheInfoMap = object : LinkedHashMap<String, CacheInfo>(16, 0.75f, true) {
        override fun removeEldestEntry(eldest: MutableMap.MutableEntry<String, CacheInfo>?): Boolean {
            return size > 1000
        }
    }

    // 缓存状态流
    private val _cacheStats = MutableStateFlow(CacheStats())
    val cacheStats: StateFlow<CacheStats> = _cacheStats

    // 缓存设置
    private var maxCacheSize = DEFAULT_MAX_CACHE_SIZE
    private var wifiOnlyCache = true

    /**
     * 缓存信息数据类
     */
    data class CacheInfo(
        val songId: Long,
        val fileName: String,
        val fileSize: Long,
        val checksum: String,
        val cacheTime: Long,
        val lastAccessTime: Long,
    )

    /**
     * 缓存统计数据类
     */
    data class CacheStats(
        val totalSize: Long = 0,
        val totalFiles: Int = 0,
        val maxSize: Long = DEFAULT_MAX_CACHE_SIZE,
        val usagePercentage: Float = 0f,
    )

    /**
     * 缓存下载进度数据类
     */
    data class DownloadProgress(
        val songId: Long,
        val progress: Float,
        val isCompleted: Boolean,
        val error: String? = null,
    )

    init {
        // 初始化时加载缓存信息
        loadCacheInfo()
        // 在协程中更新缓存统计
        CoroutineScope(Dispatchers.IO).launch {
            updateCacheStats()
        }
    }

    /**
     * 检查歌曲是否已缓存
     * @param songId 歌曲ID
     * @return 是否已缓存
     */
    suspend fun isCached(songId: Long): Boolean = withContext(Dispatchers.IO) {
        val cacheInfo = cacheInfoMap[songId.toString()]
        if (cacheInfo != null) {
            val file = File(cacheDir, cacheInfo.fileName)
            if (file.exists() && file.length() == cacheInfo.fileSize) {
                // 验证文件完整性
                return@withContext verifyFileIntegrity(file, cacheInfo.checksum)
            } else {
                // 文件不存在或大小不匹配，移除缓存信息
                removeCacheInfo(songId.toString())
            }
        }
        false
    }

    /**
     * 获取缓存文件路径
     * @param songId 歌曲ID
     * @return 缓存文件路径，如果未缓存则返回null
     */
    suspend fun getCachedFilePath(songId: Long): String? = withContext(Dispatchers.IO) {
        if (isCached(songId)) {
            val cacheInfo = cacheInfoMap[songId.toString()]
            cacheInfo?.let {
                val file = File(cacheDir, it.fileName)
                // 更新访问时间
                updateAccessTime(songId.toString())
                file.absolutePath
            }
        } else {
            null
        }
    }

    /**
     * 自动缓存歌曲（播放时触发）
     * @param song 歌曲信息
     * @return 缓存是否成功
     */
    suspend fun autoCacheSong(song: Song): Boolean = withContext(Dispatchers.IO) {
        if (isCached(song.id)) {
            Log.d(TAG, "歌曲已缓存: ${song.name}")
            return@withContext true
        }

        // 检查网络条件
        if (wifiOnlyCache && !isWifiConnected()) {
            Log.d(TAG, "仅WiFi缓存模式，当前非WiFi网络，跳过缓存: ${song.name}")
            return@withContext false
        }

        // 检查缓存空间
        if (!hasEnoughSpace(estimateFileSize(song))) {
            Log.d(TAG, "缓存空间不足，清理旧缓存")
            cleanOldCache()
        }

        return@withContext downloadAndCache(song, isManual = false)
    }

    /**
     * 手动缓存歌曲
     * @param song 歌曲信息
     * @param progressCallback 进度回调
     * @return 缓存是否成功
     */
    suspend fun manualCacheSong(
        song: Song,
        progressCallback: ((DownloadProgress) -> Unit)? = null,
    ): Boolean = withContext(Dispatchers.IO) {
        if (isCached(song.id)) {
            progressCallback?.invoke(DownloadProgress(song.id, 1.0f, true))
            return@withContext true
        }

        // 检查缓存空间
        if (!hasEnoughSpace(estimateFileSize(song))) {
            cleanOldCache()
            if (!hasEnoughSpace(estimateFileSize(song))) {
                val error = "缓存空间不足"
                progressCallback?.invoke(DownloadProgress(song.id, 0f, false, error))
                return@withContext false
            }
        }

        return@withContext downloadAndCache(song, isManual = true, progressCallback)
    }

    /**
     * 下载并缓存歌曲
     */
    private suspend fun downloadAndCache(
        song: Song,
        isManual: Boolean,
        progressCallback: ((DownloadProgress) -> Unit)? = null,
    ): Boolean = withContext(Dispatchers.IO) {
        val url = song.playUrl
        if (url.isNullOrEmpty()) {
            Log.e(TAG, "歌曲播放URL为空: ${song.name}")
            progressCallback?.invoke(DownloadProgress(song.id, 0f, false, "播放URL为空"))
            return@withContext false
        }

        try {
            Log.d(TAG, "开始${if (isManual) "手动" else "自动"}缓存歌曲: ${song.name}")

            val request = Request.Builder()
                .url(url)
                .build()

            val response = okHttpClient.newCall(request).execute()
            if (!response.isSuccessful) {
                val error = "下载失败: ${response.code}"
                Log.e(TAG, error)
                progressCallback?.invoke(DownloadProgress(song.id, 0f, false, error))
                return@withContext false
            }

            val body = response.body ?: run {
                val error = "响应体为空"
                Log.e(TAG, error)
                progressCallback?.invoke(DownloadProgress(song.id, 0f, false, error))
                return@withContext false
            }

            val contentLength = body.contentLength()
            val fileName = generateFileName(song)
            val tempFile = File(cacheDir, "$fileName.tmp")
            val finalFile = File(cacheDir, fileName)

            // 下载文件
            val inputStream = body.byteStream()
            val outputStream = FileOutputStream(tempFile)
            val buffer = ByteArray(CHUNK_SIZE)
            var totalBytesRead = 0L
            var bytesRead: Int

            val messageDigest = MessageDigest.getInstance("MD5")

            while (inputStream.read(buffer).also { bytesRead = it } != -1) {
                outputStream.write(buffer, 0, bytesRead)
                messageDigest.update(buffer, 0, bytesRead)
                totalBytesRead += bytesRead

                // 更新进度
                if (contentLength > 0) {
                    val progress = totalBytesRead.toFloat() / contentLength
                    progressCallback?.invoke(DownloadProgress(song.id, progress, false))
                }
            }

            inputStream.close()
            outputStream.close()

            // 计算校验和
            val checksum = messageDigest.digest().joinToString("") { "%02x".format(it) }

            // 重命名临时文件
            if (tempFile.renameTo(finalFile)) {
                // 保存缓存信息
                val cacheInfo = CacheInfo(
                    songId = song.id,
                    fileName = fileName,
                    fileSize = totalBytesRead,
                    checksum = checksum,
                    cacheTime = System.currentTimeMillis(),
                    lastAccessTime = System.currentTimeMillis(),
                )

                saveCacheInfo(song.id.toString(), cacheInfo)
                updateCacheStats()

                Log.d(TAG, "缓存完成: ${song.name}, 大小: ${totalBytesRead}字节")
                progressCallback?.invoke(DownloadProgress(song.id, 1.0f, true))
                return@withContext true
            } else {
                tempFile.delete()
                val error = "文件重命名失败"
                Log.e(TAG, error)
                progressCallback?.invoke(DownloadProgress(song.id, 0f, false, error))
                return@withContext false
            }
        } catch (e: Exception) {
            Log.e(TAG, "缓存歌曲失败: ${song.name}", e)
            progressCallback?.invoke(DownloadProgress(song.id, 0f, false, e.message))
            return@withContext false
        }
    }

    /**
     * 生成缓存文件名
     */
    private fun generateFileName(song: Song): String {
        val extension = getFileExtension(song.playUrl ?: "")
        return "${song.id}_${song.name.replace(Regex("[^a-zA-Z0-9\\u4e00-\\u9fa5]"), "_")}.$extension"
    }

    /**
     * 获取文件扩展名
     */
    private fun getFileExtension(url: String): String {
        return when {
            url.contains(".mp3", ignoreCase = true) -> "mp3"
            url.contains(".flac", ignoreCase = true) -> "flac"
            url.contains(".m4a", ignoreCase = true) -> "m4a"
            url.contains(".aac", ignoreCase = true) -> "aac"
            else -> "mp3" // 默认扩展名
        }
    }

    /**
     * 估算文件大小（用于空间检查）
     */
    private fun estimateFileSize(@Suppress("UNUSED_PARAMETER") song: Song): Long {
        // 根据歌曲时长估算文件大小（假设128kbps）
        // 由于Song模型可能没有duration字段，使用默认值
        val durationMs = 240000L // 默认4分钟
        return (durationMs / 1000) * 16 * 1024 // 128kbps ≈ 16KB/s
    }

    /**
     * 检查是否有足够的缓存空间
     */
    private fun hasEnoughSpace(estimatedSize: Long): Boolean {
        val currentStats = _cacheStats.value
        return (currentStats.totalSize + estimatedSize) <= maxCacheSize
    }

    /**
     * 清理旧缓存（LRU策略）
     */
    private suspend fun cleanOldCache() = withContext(Dispatchers.IO) {
        Log.d(TAG, "开始清理旧缓存")

        // 获取所有缓存信息并按访问时间排序
        val allCacheInfo = mutableListOf<Pair<String, CacheInfo>>()
        for ((key, value) in cacheInfoMap) {
            allCacheInfo.add(key to value)
        }

        // 按最后访问时间排序（最旧的在前）
        allCacheInfo.sortBy { it.second.lastAccessTime }

        // 删除最旧的缓存，直到释放足够空间
        val targetSize = maxCacheSize * 0.8 // 清理到80%使用率
        var currentSize = _cacheStats.value.totalSize
        var deletedCount = 0

        for ((key, cacheInfo) in allCacheInfo) {
            if (currentSize <= targetSize) break

            val file = File(cacheDir, cacheInfo.fileName)
            if (file.exists() && file.delete()) {
                removeCacheInfo(key)
                currentSize -= cacheInfo.fileSize
                deletedCount++
                Log.d(TAG, "删除旧缓存: ${cacheInfo.fileName}")
            }
        }

        updateCacheStats()
        Log.d(TAG, "清理完成，删除了${deletedCount}个缓存文件")
    }

    /**
     * 验证文件完整性
     */
    private suspend fun verifyFileIntegrity(file: File, expectedChecksum: String): Boolean = withContext(Dispatchers.IO) {
        try {
            val messageDigest = MessageDigest.getInstance("MD5")
            FileInputStream(file).use { inputStream ->
                val buffer = ByteArray(CHUNK_SIZE)
                var bytesRead: Int
                while (inputStream.read(buffer).also { bytesRead = it } != -1) {
                    messageDigest.update(buffer, 0, bytesRead)
                }
            }
            val actualChecksum = messageDigest.digest().joinToString("") { "%02x".format(it) }
            return@withContext actualChecksum == expectedChecksum
        } catch (e: Exception) {
            Log.e(TAG, "验证文件完整性失败: ${file.name}", e)
            return@withContext false
        }
    }

    /**
     * 检查是否为WiFi网络
     */
    private fun isWifiConnected(): Boolean {
        // TODO: 实现WiFi检查逻辑
        return true // 临时实现
    }

    /**
     * 保存缓存信息
     */
    private fun saveCacheInfo(key: String, cacheInfo: CacheInfo) {
        cacheInfoMap[key] = cacheInfo
        // TODO: 持久化到文件
    }

    /**
     * 移除缓存信息
     */
    private fun removeCacheInfo(key: String) {
        cacheInfoMap.remove(key)
        // TODO: 从持久化文件中移除
    }

    /**
     * 更新访问时间
     */
    private fun updateAccessTime(key: String) {
        val cacheInfo = cacheInfoMap[key]
        if (cacheInfo != null) {
            val updatedInfo = cacheInfo.copy(lastAccessTime = System.currentTimeMillis())
            cacheInfoMap[key] = updatedInfo
        }
    }

    /**
     * 加载缓存信息
     */
    private fun loadCacheInfo() {
        // TODO: 从持久化文件加载缓存信息
        Log.d(TAG, "加载缓存信息")
    }

    /**
     * 更新缓存统计
     */
    private suspend fun updateCacheStats() = withContext(Dispatchers.IO) {
        var totalSize = 0L
        var totalFiles = 0

        // 扫描缓存目录
        cacheDir.listFiles()?.forEach { file ->
            if (file.isFile && !file.name.endsWith(".tmp")) {
                totalSize += file.length()
                totalFiles++
            }
        }

        val usagePercentage = if (maxCacheSize > 0) {
            (totalSize.toFloat() / maxCacheSize) * 100
        } else {
            0f
        }

        _cacheStats.value = CacheStats(
            totalSize = totalSize,
            totalFiles = totalFiles,
            maxSize = maxCacheSize,
            usagePercentage = usagePercentage,
        )

        Log.d(TAG, "缓存统计更新: ${totalFiles}个文件, ${totalSize / 1024 / 1024}MB, ${usagePercentage.toInt()}%")
    }

    /**
     * 获取已缓存的歌曲列表
     */
    suspend fun getCachedSongs(): List<CacheInfo> = withContext(Dispatchers.IO) {
        val cachedSongs = mutableListOf<CacheInfo>()
        for ((_, value) in cacheInfoMap) {
            cachedSongs.add(value)
        }
        cachedSongs.sortByDescending { it.lastAccessTime }
        return@withContext cachedSongs
    }

    /**
     * 删除指定歌曲的缓存
     */
    suspend fun deleteCachedSong(songId: Long): Boolean = withContext(Dispatchers.IO) {
        val cacheInfo = cacheInfoMap[songId.toString()]
        if (cacheInfo != null) {
            val file = File(cacheDir, cacheInfo.fileName)
            val deleted = if (file.exists()) file.delete() else true
            if (deleted) {
                removeCacheInfo(songId.toString())
                updateCacheStats()
                Log.d(TAG, "删除缓存成功: ${cacheInfo.fileName}")
            }
            return@withContext deleted
        }
        return@withContext false
    }

    /**
     * 清空所有缓存
     */
    suspend fun clearAllCache(): Boolean = withContext(Dispatchers.IO) {
        try {
            cacheDir.listFiles()?.forEach { file ->
                if (file.isFile) {
                    file.delete()
                }
            }
            cacheInfoMap.clear()
            updateCacheStats()
            Log.d(TAG, "清空所有缓存成功")
            return@withContext true
        } catch (e: Exception) {
            Log.e(TAG, "清空缓存失败", e)
            return@withContext false
        }
    }

    /**
     * 设置缓存配置
     */
    fun setCacheConfig(maxSize: Long, wifiOnly: Boolean) {
        maxCacheSize = maxSize
        wifiOnlyCache = wifiOnly
        Log.d(TAG, "缓存配置更新: 最大大小=${maxSize / 1024 / 1024}MB, 仅WiFi=$wifiOnly")
    }

    /**
     * 获取缓存配置
     */
    fun getCacheConfig(): Pair<Long, Boolean> {
        return maxCacheSize to wifiOnlyCache
    }
}
