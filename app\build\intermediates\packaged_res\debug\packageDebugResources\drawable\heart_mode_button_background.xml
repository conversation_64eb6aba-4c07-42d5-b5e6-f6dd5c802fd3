<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">
    <!-- 激活状态 - 红色背景 -->
    <item android:state_selected="true">
        <shape android:shape="oval">
            <solid android:color="#FFE91E63" />
            <stroke android:width="2dp" android:color="#FFFF4081" />
        </shape>
    </item>
    
    <!-- 按下状态 - 浅红色背景 -->
    <item android:state_pressed="true">
        <shape android:shape="oval">
            <solid android:color="#80E91E63" />
            <stroke android:width="2dp" android:color="#FFFF4081" />
        </shape>
    </item>
    
    <!-- 默认状态 - 半透明背景 -->
    <item>
        <shape android:shape="oval">
            <solid android:color="#80FFFFFF" />
            <stroke android:width="1dp" android:color="#CCFFFFFF" />
        </shape>
    </item>
</selector>
