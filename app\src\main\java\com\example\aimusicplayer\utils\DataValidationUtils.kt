package com.example.aimusicplayer.utils

import android.util.Log
import com.example.aimusicplayer.data.model.Album
import com.example.aimusicplayer.data.model.Artist
import com.example.aimusicplayer.data.model.BaseResponse
import com.example.aimusicplayer.data.model.PlayList
import com.example.aimusicplayer.data.model.Song

/**
 * 数据验证工具类
 * 实现API响应数据的完整性验证、格式验证和类型检查
 * 严格按照ponymusic-master项目数据质量标准
 */
object DataValidationUtils {

    private const val TAG = "DataValidationUtils"

    // 验证统计
    private var totalValidations = 0
    private var failedValidations = 0

    /**
     * 验证BaseResponse的基本结构
     * @param response API响应对象
     * @return 验证结果
     */
    fun validateBaseResponse(response: BaseResponse?): ValidationResult {
        totalValidations++

        if (response == null) {
            failedValidations++
            return ValidationResult.failure("响应对象为空")
        }

        val errors = mutableListOf<String>()

        // 验证状态码
        if (response.code !in 200..299) {
            errors.add("无效的状态码: ${response.code}")
        }

        // 验证消息字段
        if (response.code != 200 && response.message.isNullOrBlank()) {
            errors.add("错误响应缺少错误消息")
        }

        return if (errors.isEmpty()) {
            ValidationResult.success()
        } else {
            failedValidations++
            ValidationResult.failure(errors.joinToString("; "))
        }
    }

    /**
     * 验证Song对象的完整性
     * @param song 歌曲对象
     * @return 验证结果
     */
    fun validateSong(song: Song?): ValidationResult {
        totalValidations++

        if (song == null) {
            failedValidations++
            return ValidationResult.failure("歌曲对象为空")
        }

        val errors = mutableListOf<String>()

        // 验证必需字段
        if (song.id <= 0) {
            errors.add("无效的歌曲ID: ${song.id}")
        }

        if (song.name.isBlank()) {
            errors.add("歌曲名称为空")
        }

        if (song.dt <= 0) {
            errors.add("无效的歌曲时长: ${song.dt}")
        }

        // 验证艺术家信息
        if (song.ar.isNullOrEmpty()) {
            errors.add("艺术家信息为空")
        } else {
            song.ar.forEachIndexed { index, artist ->
                val artistValidation = validateArtist(artist)
                if (!artistValidation.isValid) {
                    errors.add("艺术家[$index]: ${artistValidation.errorMessage}")
                }
            }
        }

        // 验证专辑信息
        song.al?.let { album ->
            val albumValidation = validateAlbum(album)
            if (!albumValidation.isValid) {
                errors.add("专辑信息: ${albumValidation.errorMessage}")
            }
        }

        return if (errors.isEmpty()) {
            ValidationResult.success()
        } else {
            failedValidations++
            ValidationResult.failure(errors.joinToString("; "))
        }
    }

    /**
     * 验证Artist对象的完整性
     * @param artist 艺术家对象
     * @return 验证结果
     */
    fun validateArtist(artist: Artist?): ValidationResult {
        if (artist == null) {
            return ValidationResult.failure("艺术家对象为空")
        }

        val errors = mutableListOf<String>()

        if (artist.id <= 0) {
            errors.add("无效的艺术家ID: ${artist.id}")
        }

        if (artist.name.isBlank()) {
            errors.add("艺术家名称为空")
        }

        return if (errors.isEmpty()) {
            ValidationResult.success()
        } else {
            ValidationResult.failure(errors.joinToString("; "))
        }
    }

    /**
     * 验证Album对象的完整性
     * @param album 专辑对象
     * @return 验证结果
     */
    fun validateAlbum(album: Album?): ValidationResult {
        if (album == null) {
            return ValidationResult.failure("专辑对象为空")
        }

        val errors = mutableListOf<String>()

        if (album.id.isBlank()) {
            errors.add("专辑ID为空")
        }

        if (album.name.isBlank()) {
            errors.add("专辑名称为空")
        }

        // 验证图片URL格式（可选）
        album.picUrl?.let { picUrl ->
            if (picUrl.isNotBlank() && !isValidUrl(picUrl)) {
                errors.add("无效的专辑封面URL: $picUrl")
            }
        }

        return if (errors.isEmpty()) {
            ValidationResult.success()
        } else {
            ValidationResult.failure(errors.joinToString("; "))
        }
    }

    /**
     * 验证PlayList对象的完整性
     * @param playlist 歌单对象
     * @return 验证结果
     */
    fun validatePlayList(playlist: PlayList?): ValidationResult {
        totalValidations++

        if (playlist == null) {
            failedValidations++
            return ValidationResult.failure("歌单对象为空")
        }

        val errors = mutableListOf<String>()

        if (playlist.id.isBlank()) {
            errors.add("歌单ID为空")
        }

        if (playlist.name.isBlank()) {
            errors.add("歌单名称为空")
        }

        if (playlist.songCount < 0) {
            errors.add("无效的歌曲数量: ${playlist.songCount}")
        }

        if (playlist.playCount < 0) {
            errors.add("无效的播放次数: ${playlist.playCount}")
        }

        // 验证图片URL格式（可选）
        if (playlist.coverImgUrl.isNotBlank() && !isValidUrl(playlist.coverImgUrl)) {
            errors.add("无效的歌单封面URL: ${playlist.coverImgUrl}")
        }

        return if (errors.isEmpty()) {
            ValidationResult.success()
        } else {
            failedValidations++
            ValidationResult.failure(errors.joinToString("; "))
        }
    }

    /**
     * 批量验证歌曲列表
     * @param songs 歌曲列表
     * @return 验证结果
     */
    fun validateSongList(songs: List<Song>?): ValidationResult {
        if (songs == null) {
            return ValidationResult.failure("歌曲列表为空")
        }

        if (songs.isEmpty()) {
            return ValidationResult.success("歌曲列表为空但有效")
        }

        val errors = mutableListOf<String>()
        var validSongs = 0

        songs.forEachIndexed { index, song ->
            val validation = validateSong(song)
            if (validation.isValid) {
                validSongs++
            } else {
                errors.add("歌曲[$index]: ${validation.errorMessage}")
            }
        }

        val validRate = validSongs.toDouble() / songs.size

        return if (validRate >= 0.8) { // 80%以上的歌曲有效则认为列表有效
            if (errors.isNotEmpty()) {
                ValidationResult.warning("歌曲列表部分有效 ($validSongs/${songs.size}): ${errors.take(3).joinToString("; ")}")
            } else {
                ValidationResult.success("歌曲列表完全有效 ($validSongs/${songs.size})")
            }
        } else {
            ValidationResult.failure("歌曲列表有效率过低 ($validSongs/${songs.size}): ${errors.take(5).joinToString("; ")}")
        }
    }

    /**
     * 批量验证歌单列表
     * @param playlists 歌单列表
     * @return 验证结果
     */
    fun validatePlayListList(playlists: List<PlayList>?): ValidationResult {
        if (playlists == null) {
            return ValidationResult.failure("歌单列表为空")
        }

        if (playlists.isEmpty()) {
            return ValidationResult.success("歌单列表为空但有效")
        }

        val errors = mutableListOf<String>()
        var validPlaylists = 0

        playlists.forEachIndexed { index, playlist ->
            val validation = validatePlayList(playlist)
            if (validation.isValid) {
                validPlaylists++
            } else {
                errors.add("歌单[$index]: ${validation.errorMessage}")
            }
        }

        val validRate = validPlaylists.toDouble() / playlists.size

        return if (validRate >= 0.8) { // 80%以上的歌单有效则认为列表有效
            if (errors.isNotEmpty()) {
                ValidationResult.warning("歌单列表部分有效 ($validPlaylists/${playlists.size}): ${errors.take(3).joinToString("; ")}")
            } else {
                ValidationResult.success("歌单列表完全有效 ($validPlaylists/${playlists.size})")
            }
        } else {
            ValidationResult.failure("歌单列表有效率过低 ($validPlaylists/${playlists.size}): ${errors.take(5).joinToString("; ")}")
        }
    }

    /**
     * 验证URL格式
     * @param url URL字符串
     * @return 是否为有效URL
     */
    private fun isValidUrl(url: String): Boolean {
        return try {
            url.startsWith("http://") || url.startsWith("https://")
        } catch (e: Exception) {
            false
        }
    }

    /**
     * 获取验证统计信息
     * @return 验证统计
     */
    fun getValidationStats(): ValidationStats {
        val successRate = if (totalValidations > 0) {
            (totalValidations - failedValidations).toDouble() / totalValidations
        } else {
            1.0
        }

        return ValidationStats(
            totalValidations = totalValidations,
            failedValidations = failedValidations,
            successRate = successRate,
        )
    }

    /**
     * 重置验证统计
     */
    fun resetValidationStats() {
        totalValidations = 0
        failedValidations = 0
    }

    /**
     * 验证结果数据类
     */
    data class ValidationResult(
        val isValid: Boolean,
        val errorMessage: String? = null,
        val level: Level = Level.ERROR,
    ) {
        enum class Level {
            SUCCESS, WARNING, ERROR
        }

        companion object {
            fun success(message: String? = null): ValidationResult {
                return ValidationResult(true, message, Level.SUCCESS)
            }

            fun warning(message: String): ValidationResult {
                return ValidationResult(true, message, Level.WARNING)
            }

            fun failure(message: String): ValidationResult {
                return ValidationResult(false, message, Level.ERROR)
            }
        }

        fun logResult(tag: String = TAG) {
            when (level) {
                Level.SUCCESS -> if (errorMessage != null) Log.d(tag, errorMessage)
                Level.WARNING -> Log.w(tag, errorMessage ?: "验证警告")
                Level.ERROR -> Log.e(tag, errorMessage ?: "验证失败")
            }
        }
    }

    /**
     * 验证统计数据类
     */
    data class ValidationStats(
        val totalValidations: Int,
        val failedValidations: Int,
        val successRate: Double,
    ) {
        fun getFormattedStats(): String {
            return """
                数据验证统计:
                - 总验证次数: $totalValidations
                - 失败次数: $failedValidations
                - 成功率: ${String.format("%.2f", successRate * 100)}%
            """.trimIndent()
        }
    }
}

/**
 * 扩展函数：为Song提供验证
 */
fun Song.validate(): DataValidationUtils.ValidationResult {
    return DataValidationUtils.validateSong(this)
}

/**
 * 扩展函数：为PlayList提供验证
 */
fun PlayList.validate(): DataValidationUtils.ValidationResult {
    return DataValidationUtils.validatePlayList(this)
}

/**
 * 扩展函数：为List<Song>提供验证
 */
fun List<Song>.validateSongs(): DataValidationUtils.ValidationResult {
    return DataValidationUtils.validateSongList(this)
}

/**
 * 扩展函数：为List<PlayList>提供验证
 */
fun List<PlayList>.validatePlaylists(): DataValidationUtils.ValidationResult {
    return DataValidationUtils.validatePlayListList(this)
}
