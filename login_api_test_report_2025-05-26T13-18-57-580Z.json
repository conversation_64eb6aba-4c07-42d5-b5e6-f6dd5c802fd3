{"timestamp": "2025-05-26T13:18:45.871Z", "summary": {"total": 18, "passed": 11, "failed": 7, "primarySuccess": 6, "backupSuccess": 5}, "loginTests": {"guestLogin": {"primary": {"success": false, "responseTime": 42, "statusCode": 502, "error": "HTTP状态码错误: 502", "data": null, "dataSize": 556, "rawData": "<html>\r\n<head><title>502 Bad Gateway</title></head>\r\n<body>\r\n<center><h1>502 Bad Gateway</h1></center>\r\n<hr><center>openresty</center>\r\n</body>\r\n</html>\r\n<!-- a padding to disable MSIE and Chrome frie", "validation": {"valid": false, "message": "HTTP状态码错误: 502"}, "server": "primary", "hostname": "ncm.zhenxin.me", "apiKey": "guest<PERSON><PERSON><PERSON>", "description": "游客登录接口"}, "backup": {"success": true, "responseTime": 253, "statusCode": 200, "error": null, "data": {"code": 200, "userId": 9862922285, "createTime": 1714449380791, "cookie": "MUSIC_R_T=0; Max-Age=2147483647; Expires=Sat, 13 Jun 2093 16:32:59 GMT; Path=/weapi/clientlog; HTTPOnly;MUSIC_A_T=1714449380791; Max-Age=2147483647; Expires=Sat, 13 Jun 2093 16:32:59 GMT; Path=/wapi/feedback; HTTPOnly;MUSIC_R_T=0; Max-Age=2147483647; Expires=Sat, 13 Jun 2093 16:32:59 GMT; Path=/wapi/feedback; HTTPOnly;MUSIC_A_T=1714449380791; Max-Age=2147483647; Expires=Sat, 13 Jun 2093 16:32:59 GMT; Path=/eapi/feedback; HTTPOnly;MUSIC_A_T=1714449380791; Max-Age=2147483647; Expires=Sat, 13 Jun 2093 16:32:59 GMT; Path=/neapi/feedback; HTTPOnly;MUSIC_A_T=1714449380791; Max-Age=2147483647; Expires=Sat, 13 Jun 2093 16:32:59 GMT; Path=/api/clientlog; HTTPOnly;MUSIC_R_T=0; Max-Age=2147483647; Expires=Sat, 13 Jun 2093 16:32:59 GMT; Path=/api/feedback; HTTPOnly;MUSIC_R_T=0; Max-Age=2147483647; Expires=Sat, 13 Jun 2093 16:32:59 GMT; Path=/weapi/feedback; HTTPOnly;MUSIC_A_T=1714449380791; Max-Age=2147483647; Expires=Sat, 13 Jun 2093 16:32:59 GMT; Path=/weapi/feedback; HTTPOnly;MUSIC_A_T=1714449380791; Max-Age=2147483647; Expires=Sat, 13 Jun 2093 16:32:59 GMT; Path=/weapi/clientlog; HTTPOnly;MUSIC_R_T=0; Max-Age=2147483647; Expires=Sat, 13 Jun 2093 16:32:59 GMT; Path=/wapi/clientlog; HTTPOnly;MUSIC_R_T=0; Max-Age=2147483647; Expires=Sat, 13 Jun 2093 16:32:59 GMT; Path=/api/clientlog; HTTPOnly;MUSIC_R_T=0; Max-Age=2147483647; Expires=Sat, 13 Jun 2093 16:32:59 GMT; Path=/eapi/clientlog; HTTPOnly;MUSIC_R_T=0; Max-Age=2147483647; Expires=Sat, 13 Jun 2093 16:32:59 GMT; Path=/neapi/clientlog; HTTPOnly;__csrf=6e82d83cb2d178459c1f49828f7115a1; Max-Age=1296010; Expires=Tue, 10 Jun 2025 13:19:02 GMT; Path=/;;MUSIC_R_T=0; Max-Age=2147483647; Expires=Sat, 13 Jun 2093 16:32:59 GMT; Path=/eapi/feedback; HTTPOnly;MUSIC_A=0067F835D521F012D3103F2B15AFD6605AC11BD869113593BC2038F9BAC2CCD9D63D1A8C0206F8C8D54584963F0B493FF941BA83C6B2A72709B1CF0990CE4CCFBC339DAA558915F6D34BF30E83AA8EE3722156F8EC5074F599A193A4E46920793DDE87895EC402622699355B83C92F4E1FDEBA4E40639FD7C94968267064F2DE62951C0B1DACFB5C39B06954A1EE806C35D930DA37FFF93F06F3781FD4AF050463468353A33CE7152ECB95F5F314A105E2B817E65305FA6AD32EEC2EE0586F87839D0031070FFDCBF5418876919D95FC530BBC8E283CA5EB8C2CBD8AE8EFCE78D93CEDE83E2359A95BE3476F3BDBCC3ED740986D6D3DD305A7450E26DC6D5DC758290EB3D899562D5760DF200E5DB77BB0095E2BA07A0F86D6440A2C7B66C01276B30FC5D7C67ABB46D3D589F14BA5113ED6EA3250BE399AE3E867E06C3870BDAC65CCCA6C3EC780F0C438970C81722586B48E80CCFF72A97A712C2CC13680614236F687697903AEC8078D34517C22D1DCD5A20A12995885B7E2517AE782F0773FC73169C7691BF89E539997440333895FEA91BB50B6FA3D85EC5C22ACE0B4347C90E3C0C902A527661CE52E6706BCE034; Max-Age=2147483647; Expires=Sat, 13 Jun 2093 16:32:59 GMT; Path=/; HTTPOnly;MUSIC_R_T=0; Max-Age=2147483647; Expires=Sat, 13 Jun 2093 16:32:59 GMT; Path=/openapi/clientlog; HTTPOnly;MUSIC_A_T=1714449380791; Max-Age=2147483647; Expires=Sat, 13 Jun 2093 16:32:59 GMT; Path=/openapi/clientlog; HTTPOnly;MUSIC_A_T=1714449380791; Max-Age=2147483647; Expires=Sat, 13 Jun 2093 16:32:59 GMT; Path=/wapi/clientlog; HTTPOnly;MUSIC_A_T=1714449380791; Max-Age=2147483647; Expires=Sat, 13 Jun 2093 16:32:59 GMT; Path=/neapi/clientlog; HTTPOnly;MUSIC_SNS=; Max-Age=0; Expires=Mon, 26 May 2025 13:18:52 GMT; Path=/;MUSIC_A_T=1714449380791; Max-Age=2147483647; Expires=Sat, 13 Jun 2093 16:32:59 GMT; Path=/eapi/clientlog; HTTPOnly;MUSIC_R_T=0; Max-Age=2147483647; Expires=Sat, 13 Jun 2093 16:32:59 GMT; Path=/neapi/feedback; HTTPOnly;MUSIC_A_T=1714449380791; Max-Age=2147483647; Expires=Sat, 13 Jun 2093 16:32:59 GMT; Path=/api/feedback; HTTPOnly"}, "dataSize": 3594, "structureValid": true, "validation": {"valid": true, "message": "游客登录成功", "hasCookie": true}, "server": "backup", "hostname": "**********-4499wupl9z.ap-guangzhou.tencentscf.com", "apiKey": "guest<PERSON><PERSON><PERSON>", "description": "游客登录接口"}}, "qrKey": {"primary": {"success": true, "responseTime": 165, "statusCode": 200, "error": null, "data": {"data": {"code": 200, "unikey": "b26c0d10-a4fb-4132-bcd3-1e044c7e6a27"}, "code": 200}, "dataSize": 80, "structureValid": true, "validation": {"valid": true, "message": "获取二维码key成功", "key": "b26c0d10-a4fb-4132-bcd3-1e044c7e6a27"}, "server": "primary", "hostname": "ncm.zhenxin.me", "apiKey": "qr<PERSON><PERSON>", "description": "获取二维码key"}, "backup": {"success": false, "responseTime": 138, "statusCode": 400, "error": "HTTP状态码错误: 400", "data": {"code": -462, "data": {"actionCode": null, "verifyType": 40, "verifyId": 1007602, "verifyUrl": "https://st.music.163.com/encrypt-pages", "blockText": "验证成功后，可进行下一步操作哦~", "verifyToken": "00.40.155b258a0f8b5d30d7b3514faad9be67.404403038", "btnText": "", "orpheusUrl": "", "frontRuleIds": null, "params": {"event_id": "00635102542930792451", "sign": "5b620863e39a2769c4c738cfed2fb8b3"}, "url": "https://st.music.163.com/encrypt-pages?params=%7B%22sign%22%3A%225b620863e39a2769c4c738cfed2fb8b3%22%2C%22event_id%22%3A%2200635102542930792451%22%7D&verifyType=40&verifyId=1007602&verifyToken=00.40.155b258a0f8b5d30d7b3514faad9be67.404403038", "urlAutoOpen": 0, "orpheusUrlParamAppend": null}, "message": "请完成验证操作"}, "dataSize": 675, "validation": {"valid": false, "message": "HTTP状态码错误: 400"}, "server": "backup", "hostname": "**********-4499wupl9z.ap-guangzhou.tencentscf.com", "apiKey": "qr<PERSON><PERSON>", "description": "获取二维码key"}}, "qrCreate": {"primary": {"success": true, "responseTime": 47, "statusCode": 200, "error": null, "data": {"code": 200, "data": {"qrurl": "https://music.163.com/login?codekey=test", "qrimg": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAJQAAACUCAYAAAB1PADUAAAAAklEQVR4AewaftIAAATfSURBVO3BQY4cSRIEQdNA/f/Lun30UwCJ9GpyuCaCP1K15KRq0UnVopOqRSdVi06qFp1ULTqpWnRSteikatFJ1aKTqkUnVYtOqhadVC06qVr0yUtAfpOaGyA3aiYgk5oJyKRmArJJzQTkN6l546Rq0UnVopOqRZ8sU7MJyBtqJiCTmieA3Kh5AsgTajYB2XRSteikatFJ1aJPvgzIE2qeAHID5AbIpGZSMwF5Asg3AXlCzTedVC06qVp0UrXok3+MmgnIjZoJyI2aCcgEZFIzAZnU/EtOqhadVC06qVr0yT9OzQTkRs0TaiYgE5D/JydVi06qFp1ULfrky9T8JiCTmhsgk5oJyBtqboBMap5Q8zc5qVp0UrXopGrRJ8uA/ElqJiCTmgnIE2omIJOaCcik5g0gf7OTqkUnVYtOqhbhj/yHAXlDzQ2QGzVvAJnU/JedVC06qVp0UrXok5eATGomIJvUTGomIG8AuVEzAblRMwGZ1NwA2aTmm06qFp1ULTqpWvTJMiBvqHkCyBtAJjUTkCfUTEBugGxS8wSQSc0bJ1WLTqoWnVQtwh9ZBORGzQ2Qb1JzA2RSMwF5Qs0NkBs1E5AbNX/SSdWik6pFJ1WLPnkJyKRmAnIDZFLzBJBJzQTkBsikZgLyhJobIDdqJiCTmhsgk5oJyKRm00nVopOqRSdVi/BHXgDym9TcALlRcwPkCTUTkEnNBGRSMwGZ1DwB5EbNN51ULTqpWnRStQh/5A8CcqPmBsikZgIyqXkCyKRmAvI3UfMEkEnNGydVi06qFp1ULfrkJSCTmgnIpOYJIJOaN4DcqLkBcqNmAjKpmYBMaiYgN2omIDdqJjWbTqoWnVQtOqla9MkvAzKpeQLIpGYCMql5Q80baiYgk5oJyI2aCcik5gbIpGbTSdWik6pFJ1WLPlkGZBOQSc0EZFIzAblRswnIpGZS84SaCcikZgJyo+abTqoWnVQtOqla9MkyNTdAnlDzBJAbNTdAJjU3QG6APKFmAnIDZFIzAbkBMql546Rq0UnVopOqRZ/8MjU3QG7UPKHmDSBvqHkCyKRmArJJzaaTqkUnVYtOqhZ98mVA3lBzo2YCMgGZ1ExAbtRMQG7UTECeULNJzQRkUrPppGrRSdWik6pFn3yZmieATEDeUPOEmgnIjZoJyBtANgG5ATKpeeOkatFJ1aKTqkWfvKRmk5ongExqJiCTmifU3AC5UXMDZFLzBJAJyJ90UrXopGrRSdWiT14C8pvUTGpu1GwCMqmZgNwAeQLIpOYNNd90UrXopGrRSdWiT5ap2QTkBsik5gbIpOYGyA2QSc2NmgnIjZon1NwAmdRsOqladFK16KRq0SdfBuQJNb8JyKRmUjMBmdRMQCY1TwDZBOQGyKTmjZOqRSdVi06qFn1SV0AmNROQSc0E5A01E5BJzQTkTzqpWnRSteikatEn/xggN2pugExqJiA3QG7UPAHkBsgbajadVC06qVp0UrXoky9T801qboA8oeZGzQRkUnMD5EbNpGYC8oSaCcg3nVQtOqladFK1CH/kBSC/Sc0EZFKzCcik5gbIpOYJIE+ouQEyqfmmk6pFJ1WLTqoW4Y9ULTmpWnRSteikatFJ1aKTqkUnVYtOqhadVC06qVp0UrXopGrRSdWik6pFJ1WLTqoW/Q+bjjZXr3S9BwAAAABJRU5ErkJggg=="}}, "dataSize": 1865, "structureValid": true, "validation": {"valid": true, "message": "生成二维码成功", "hasQrImg": true}, "server": "primary", "hostname": "ncm.zhenxin.me", "apiKey": "qrCreate", "description": "生成二维码"}, "backup": {"success": true, "responseTime": 112, "statusCode": 200, "error": null, "data": {"code": 200, "data": {"qrurl": "https://music.163.com/login?codekey=test", "qrimg": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAJQAAACUCAYAAAB1PADUAAAAAklEQVR4AewaftIAAATfSURBVO3BQY4cSRIEQdNA/f/Lun30UwCJ9GpyuCaCP1K15KRq0UnVopOqRSdVi06qFp1ULTqpWnRSteikatFJ1aKTqkUnVYtOqhadVC06qVr0yUtAfpOaGyA3aiYgk5oJyKRmArJJzQTkN6l546Rq0UnVopOqRZ8sU7MJyBtqJiCTmieA3Kh5AsgTajYB2XRSteikatFJ1aJPvgzIE2qeAHID5AbIpGZSMwF5Asg3AXlCzTedVC06qVp0UrXok3+MmgnIjZoJyI2aCcgEZFIzAZnU/EtOqhadVC06qVr0yT9OzQTkRs0TaiYgE5D/JydVi06qFp1ULfrky9T8JiCTmhsgk5oJyBtqboBMap5Q8zc5qVp0UrXopGrRJ8uA/ElqJiCTmgnIE2omIJOaCcik5g0gf7OTqkUnVYtOqhbhj/yHAXlDzQ2QGzVvAJnU/JedVC06qVp0UrXok5eATGomIJvUTGomIG8AuVEzAblRMwGZ1NwA2aTmm06qFp1ULTqpWvTJMiBvqHkCyBtAJjUTkCfUTEBugGxS8wSQSc0bJ1WLTqoWnVQtwh9ZBORGzQ2Qb1JzA2RSMwF5Qs0NkBs1E5AbNX/SSdWik6pFJ1WLPnkJyKRmAnIDZFLzBJBJzQTkBsikZgLyhJobIDdqJiCTmhsgk5oJyKRm00nVopOqRSdVi/BHXgDym9TcALlRcwPkCTUTkEnNBGRSMwGZ1DwB5EbNN51ULTqpWnRStQh/5A8CcqPmBsikZgIyqXkCyKRmAvI3UfMEkEnNGydVi06qFp1ULfrkJSCTmgnIpOYJIJOaN4DcqLkBcqNmAjKpmYBMaiYgN2omIDdqJjWbTqoWnVQtOqla9MkvAzKpeQLIpGYCMql5Q80baiYgk5oJyI2aCcik5gbIpGbTSdWik6pFJ1WLPlkGZBOQSc0EZFIzAblRswnIpGZS84SaCcikZgJyo+abTqoWnVQtOqla9MkyNTdAnlDzBJAbNTdAJjU3QG6APKFmAnIDZFIzAbkBMql546Rq0UnVopOqRZ/8MjU3QG7UPKHmDSBvqHkCyKRmArJJzaaTqkUnVYtOqhZ98mVA3lBzo2YCMgGZ1ExAbtRMQG7UTECeULNJzQRkUrPppGrRSdWik6pFn3yZmieATEDeUPOEmgnIjZoJyBtANgG5ATKpeeOkatFJ1aKTqkWfvKRmk5ongExqJiCTmifU3AC5UXMDZFLzBJAJyJ90UrXopGrRSdWiT14C8pvUTGpu1GwCMqmZgNwAeQLIpOYNNd90UrXopGrRSdWiT5ap2QTkBsik5gbIpOYGyA2QSc2NmgnIjZon1NwAmdRsOqladFK16KRq0SdfBuQJNb8JyKRmUjMBmdRMQCY1TwDZBOQGyKTmjZOqRSdVi06qFn1SV0AmNROQSc0E5A01E5BJzQTkTzqpWnRSteikatEn/xggN2pugExqJiA3QG7UPAHkBsgbajadVC06qVp0UrXoky9T801qboA8oeZGzQRkUnMD5EbNpGYC8oSaCcg3nVQtOqladFK1CH/kBSC/Sc0EZFKzCcik5gbIpOYJIE+ouQEyqfmmk6pFJ1WLTqoW4Y9ULTmpWnRSteikatFJ1aKTqkUnVYtOqhadVC06qVp0UrXopGrRSdWik6pFJ1WLTqoW/Q+bjjZXr3S9BwAAAABJRU5ErkJggg=="}}, "dataSize": 1865, "structureValid": true, "validation": {"valid": true, "message": "生成二维码成功", "hasQrImg": true}, "server": "backup", "hostname": "**********-4499wupl9z.ap-guangzhou.tencentscf.com", "apiKey": "qrCreate", "description": "生成二维码"}}, "qrCheck": {"primary": {"success": true, "responseTime": 240, "statusCode": 200, "error": null, "data": {"code": 800, "message": "二维码不存在或已过期", "cookie": "NMTID=00OHOyBvsfCf6ndRUrCtai3RJybQMYAAAGXDL7uFw; Max-Age=315360000; Expires=Thu, 24 May 2035 13:18:48 GMT; Path=/;"}, "dataSize": 161, "structureValid": true, "validation": {"valid": true, "message": "二维码过期", "status": 800}, "server": "primary", "hostname": "ncm.zhenxin.me", "apiKey": "qr<PERSON><PERSON><PERSON>", "description": "检查二维码状态"}, "backup": {"success": true, "responseTime": 142, "statusCode": 200, "error": null, "data": {"code": 800, "message": "二维码不存在或已过期", "cookie": "NMTID=00OWOmSRwfAp1jdc0xmuN7FBxG5XvgAAAGXDL8CqA; Max-Age=315360000; Expires=Thu, 24 May 2035 13:18:54 GMT; Path=/;"}, "dataSize": 161, "structureValid": true, "validation": {"valid": true, "message": "二维码过期", "status": 800}, "server": "backup", "hostname": "**********-4499wupl9z.ap-guangzhou.tencentscf.com", "apiKey": "qr<PERSON><PERSON><PERSON>", "description": "检查二维码状态"}}, "captchaSent": {"primary": {"success": true, "responseTime": 368, "statusCode": 200, "error": null, "data": {"code": 400, "message": "当天发送验证码的条数超过限制", "data": false}, "dataSize": 52, "structureValid": true, "validation": {"valid": true, "message": "验证码发送限制（正常业务逻辑）", "businessLogic": true}, "server": "primary", "hostname": "ncm.zhenxin.me", "apiKey": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "发送验证码"}, "backup": {"success": false, "responseTime": 286, "statusCode": 405, "error": "HTTP状态码错误: 405", "data": {"code": 405, "message": "发送验证码间隔过短", "data": false}, "dataSize": 47, "validation": {"valid": false, "message": "HTTP状态码错误: 405"}, "server": "backup", "hostname": "**********-4499wupl9z.ap-guangzhou.tencentscf.com", "apiKey": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "发送验证码"}}, "captchaVerify": {"primary": {"success": false, "responseTime": 168, "statusCode": 503, "error": "HTTP状态码错误: 503", "data": {"message": "验证码错误", "code": 503, "data": false}, "dataSize": 43, "validation": {"valid": false, "message": "HTTP状态码错误: 503"}, "server": "primary", "hostname": "ncm.zhenxin.me", "apiKey": "captchaVerify", "description": "验证验证码"}, "backup": {"success": false, "responseTime": 137, "statusCode": 503, "error": "HTTP状态码错误: 503", "data": {"message": "验证码错误", "code": 503, "data": false}, "dataSize": 43, "validation": {"valid": false, "message": "HTTP状态码错误: 503"}, "server": "backup", "hostname": "**********-4499wupl9z.ap-guangzhou.tencentscf.com", "apiKey": "captchaVerify", "description": "验证验证码"}}, "loginCellphone": {"primary": {"success": false, "responseTime": 175, "statusCode": 503, "error": "HTTP状态码错误: 503", "data": {"msg": "验证码错误", "code": 503, "message": "验证码错误"}, "dataSize": 44, "validation": {"valid": false, "message": "HTTP状态码错误: 503"}, "server": "primary", "hostname": "ncm.zhenxin.me", "apiKey": "loginCellphone", "description": "手机号登录"}, "backup": {"success": false, "responseTime": 198, "statusCode": 400, "error": "HTTP状态码错误: 400", "data": {"code": -462, "data": {"actionCode": null, "verifyType": 40, "verifyId": 1007602, "verifyUrl": "https://st.music.163.com/encrypt-pages", "blockText": "验证成功后，可进行下一步操作哦~", "verifyToken": "00.40.a7149b94af0c633cbf94c90572c68ab5.**********", "btnText": "", "orpheusUrl": "", "frontRuleIds": null, "params": {"event_id": "00635102561905913859", "sign": "0d17e8825b074de52e2022040f3d2989"}, "url": "https://st.music.163.com/encrypt-pages?params=%7B%22sign%22%3A%220d17e8825b074de52e2022040f3d2989%22%2C%22event_id%22%3A%2200635102561905913859%22%7D&verifyType=40&verifyId=1007602&verifyToken=00.40.a7149b94af0c633cbf94c90572c68ab5.**********", "urlAutoOpen": 0, "orpheusUrlParamAppend": null}, "message": "请完成验证操作"}, "dataSize": 677, "validation": {"valid": false, "message": "HTTP状态码错误: 400"}, "server": "backup", "hostname": "**********-4499wupl9z.ap-guangzhou.tencentscf.com", "apiKey": "loginCellphone", "description": "手机号登录"}}, "loginStatus": {"primary": {"success": true, "responseTime": 166, "statusCode": 200, "error": null, "data": {"data": {"code": 200, "account": null, "profile": null}}, "dataSize": 51, "structureValid": true, "validation": {"valid": true, "message": "登录状态检查成功", "hasAccount": false}, "server": "primary", "hostname": "ncm.zhenxin.me", "apiKey": "loginStatus", "description": "检查登录状态"}, "backup": {"success": true, "responseTime": 130, "statusCode": 200, "error": null, "data": {"data": {"code": 200, "account": null, "profile": null}}, "dataSize": 51, "structureValid": true, "validation": {"valid": true, "message": "登录状态检查成功", "hasAccount": false}, "server": "backup", "hostname": "**********-4499wupl9z.ap-guangzhou.tencentscf.com", "apiKey": "loginStatus", "description": "检查登录状态"}}, "userAccount": {"primary": {"success": true, "responseTime": 180, "statusCode": 200, "error": null, "data": {"code": 200, "account": null, "profile": null}, "dataSize": 42, "structureValid": true, "validation": {"valid": true, "message": "获取用户账号信息成功", "hasAccount": false}, "server": "primary", "hostname": "ncm.zhenxin.me", "apiKey": "userAccount", "description": "获取用户账号信息"}, "backup": {"success": true, "responseTime": 160, "statusCode": 200, "error": null, "data": {"code": 200, "account": null, "profile": null}, "dataSize": 42, "structureValid": true, "validation": {"valid": true, "message": "获取用户账号信息成功", "hasAccount": false}, "server": "backup", "hostname": "**********-4499wupl9z.ap-guangzhou.tencentscf.com", "apiKey": "userAccount", "description": "获取用户账号信息"}}}}