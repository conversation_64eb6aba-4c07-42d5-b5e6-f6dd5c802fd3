<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="item_play_queue" modulePackage="com.example.aimusicplayer" filePath="app\src\main\res\layout\item_play_queue.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.LinearLayout"><Targets><Target tag="layout/item_play_queue_0" view="LinearLayout"><Expressions/><location startLine="1" startOffset="0" endLine="90" endOffset="14"/></Target><Target id="@+id/tv_song_index" view="TextView"><Expressions/><location startLine="13" startOffset="4" endLine="20" endOffset="24"/></Target><Target id="@+id/iv_playing_indicator" view="ImageView"><Expressions/><location startLine="23" startOffset="4" endLine="31" endOffset="36"/></Target><Target id="@+id/tv_song_title" view="TextView"><Expressions/><location startLine="42" startOffset="8" endLine="51" endOffset="31"/></Target><Target id="@+id/tv_song_artist" view="TextView"><Expressions/><location startLine="54" startOffset="8" endLine="63" endOffset="32"/></Target><Target id="@+id/tv_song_duration" view="TextView"><Expressions/><location startLine="68" startOffset="4" endLine="77" endOffset="28"/></Target><Target id="@+id/iv_more" view="ImageView"><Expressions/><location startLine="80" startOffset="4" endLine="88" endOffset="32"/></Target></Targets></Layout>