package com.example.aimusicplayer.viewmodel

import android.util.Log
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.example.aimusicplayer.data.model.PlayList
import com.example.aimusicplayer.data.model.Song
import com.example.aimusicplayer.data.repository.MusicRepository
import com.example.aimusicplayer.utils.NetworkResult
import com.example.aimusicplayer.utils.toUnMutable
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * 音乐库ViewModel
 * 负责本地音乐库的管理和展示
 */
@HiltViewModel
class MusicLibraryViewModel @Inject constructor(
    private val musicRepository: MusicRepository,
) : ViewModel() {

    companion object {
        private const val TAG = "MusicLibraryViewModel"
    }

    // 本地歌曲列表 - 使用懒加载初始化
    private val _localSongs by lazy { MutableStateFlow<List<Song>>(emptyList()) }
    val localSongs by lazy { _localSongs.toUnMutable() }

    // 收藏的歌曲列表 - 使用懒加载初始化
    private val _favoriteSongs by lazy { MutableStateFlow<List<Song>>(emptyList()) }
    val favoriteSongs by lazy { _favoriteSongs.toUnMutable() }

    // 播放列表 - 使用懒加载初始化
    private val _playlists by lazy { MutableStateFlow<List<PlayList>>(emptyList()) }
    val playlists by lazy { _playlists.toUnMutable() }

    // 加载状态 - 使用懒加载初始化
    private val _isLoading by lazy { MutableStateFlow(false) }
    val isLoading by lazy { _isLoading.toUnMutable() }

    // 错误信息 - 使用懒加载初始化
    private val _errorMessage by lazy { MutableStateFlow<String?>(null) }
    val errorMessage by lazy { _errorMessage.toUnMutable() }

    // 当前选中的分类 - 使用懒加载初始化
    private val _selectedCategory by lazy { MutableStateFlow(0) } // 0: 本地音乐, 1: 收藏, 2: 播放列表
    val selectedCategory by lazy { _selectedCategory.toUnMutable() }

    /**
     * 加载本地音乐
     */
    fun loadLocalMusic() {
        viewModelScope.launch {
            try {
                _isLoading.value = true
                // TODO: 实现本地音乐扫描逻辑
                // 暂时使用空列表，实际应该扫描本地音乐文件
                val songs = emptyList<Song>()
                _localSongs.value = songs
                Log.d(TAG, "加载本地音乐完成，共${songs.size}首")
            } catch (e: Exception) {
                Log.e(TAG, "加载本地音乐失败", e)
                _errorMessage.value = "加载本地音乐失败: ${e.message}"
            } finally {
                _isLoading.value = false
            }
        }
    }

    /**
     * 加载收藏歌曲
     */
    fun loadFavoriteSongs() {
        viewModelScope.launch {
            try {
                _isLoading.value = true
                // TODO: 实现收藏歌曲获取逻辑
                val songs = emptyList<Song>()
                _favoriteSongs.value = songs
                Log.d(TAG, "加载收藏歌曲完成，共${songs.size}首")
            } catch (e: Exception) {
                Log.e(TAG, "加载收藏歌曲失败", e)
                _errorMessage.value = "加载收藏歌曲失败: ${e.message}"
            } finally {
                _isLoading.value = false
            }
        }
    }

    /**
     * 加载播放列表
     */
    fun loadPlaylists() {
        viewModelScope.launch {
            try {
                _isLoading.value = true
                val playlistsResult = musicRepository.getUserPlaylists()
                // 处理NetworkResult类型
                when (playlistsResult) {
                    is NetworkResult.Success -> {
                        _playlists.value = playlistsResult.data
                        Log.d(TAG, "加载播放列表完成，共${playlistsResult.data.size}个")
                    }
                    is NetworkResult.Error -> {
                        _errorMessage.value = "加载播放列表失败: ${playlistsResult.message}"
                        Log.e(TAG, "加载播放列表失败: ${playlistsResult.message}")
                    }
                    is NetworkResult.Loading -> {
                        // 保持加载状态
                    }
                }
            } catch (e: Exception) {
                Log.e(TAG, "加载播放列表失败", e)
                _errorMessage.value = "加载播放列表失败: ${e.message}"
            } finally {
                _isLoading.value = false
            }
        }
    }

    /**
     * 设置选中的分类
     */
    fun setSelectedCategory(category: Int) {
        _selectedCategory.value = category
        when (category) {
            0 -> loadLocalMusic()
            1 -> loadFavoriteSongs()
            2 -> loadPlaylists()
        }
    }

    /**
     * 刷新数据
     */
    fun refreshData() {
        when (_selectedCategory.value) {
            0 -> loadLocalMusic()
            1 -> loadFavoriteSongs()
            2 -> loadPlaylists()
        }
    }

    /**
     * 清除错误信息
     */
    fun clearError() {
        _errorMessage.value = null
    }
}
