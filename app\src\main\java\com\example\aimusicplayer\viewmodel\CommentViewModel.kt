package com.example.aimusicplayer.viewmodel

import android.util.Log
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.example.aimusicplayer.data.model.Comment
import com.example.aimusicplayer.data.model.CommentResponse
import com.example.aimusicplayer.data.repository.CommentRepository
import com.example.aimusicplayer.data.repository.MusicRepository
import com.example.aimusicplayer.utils.NetworkResult
import com.example.aimusicplayer.utils.toUnMutable
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.collect
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * 评论ViewModel
 * 严格按照ponymusic项目标准重构
 */
@HiltViewModel
class CommentViewModel @Inject constructor(
    private val musicRepository: MusicRepository,
    private val commentRepository: CommentRepository,
) : ViewModel() {

    companion object {
        private const val TAG = "CommentViewModel"

        // 评论类型
        const val TYPE_SONG = 0
        const val TYPE_MV = 1
        const val TYPE_PLAYLIST = 2
        const val TYPE_ALBUM = 3
        const val TYPE_RADIO = 4
        const val TYPE_VIDEO = 5
        const val TYPE_DYNAMIC = 6
    }

    // 严格按照ponymusic项目标准的StateFlow使用 - 使用懒加载初始化
    private val _commentsFlow by lazy { MutableStateFlow<List<Comment>>(emptyList()) }
    val commentsFlow by lazy { _commentsFlow.toUnMutable() }
    private val _hotCommentsFlow by lazy { MutableStateFlow<List<Comment>>(emptyList()) }
    val hotCommentsFlow by lazy { _hotCommentsFlow.toUnMutable() }
    private val _commentSentFlow by lazy { MutableStateFlow<Boolean>(false) }
    val commentSentFlow by lazy { _commentSentFlow.toUnMutable() }
    private val _hasMoreDataFlow by lazy { MutableStateFlow<Boolean>(true) }
    val hasMoreDataFlow by lazy { _hasMoreDataFlow.toUnMutable() }
    private val _isLoadingMoreFlow by lazy { MutableStateFlow<Boolean>(false) }
    val isLoadingMoreFlow by lazy { _isLoadingMoreFlow.toUnMutable() }
    private val _commentCountFlow by lazy { MutableStateFlow<Int>(0) }
    val commentCountFlow by lazy { _commentCountFlow.toUnMutable() }
    private val _resourceIdFlow by lazy { MutableStateFlow<Long>(0) }
    val resourceIdFlow by lazy { _resourceIdFlow.toUnMutable() }
    private val _resourceTypeFlow by lazy { MutableStateFlow<Int>(TYPE_SONG) }
    val resourceTypeFlow by lazy { _resourceTypeFlow.toUnMutable() }

    // UI兼容属性（为了兼容现有UI层代码） - 使用懒加载初始化
    val comments by lazy { commentsFlow }
    private val _loading by lazy { MutableStateFlow(false) }
    val loading by lazy { _loading.toUnMutable() }
    private val _errorMessage by lazy { MutableStateFlow<String?>(null) }
    val errorMessage by lazy { _errorMessage.toUnMutable() }
    val commentSent by lazy { commentSentFlow }
    val isLoadingMore by lazy { isLoadingMoreFlow }
    val hasMoreData by lazy { hasMoreDataFlow }

    // 当前页码
    private var currentPage = 0

    // 每页数量
    private val pageSize = 20

    /**
     * 加载评论
     * @param songId 歌曲ID
     * @param forceRefresh 是否强制刷新（暂未使用，保留以备将来扩展）
     */
    fun loadComments(songId: Long, @Suppress("UNUSED_PARAMETER") forceRefresh: Boolean = false) {
        viewModelScope.launch {
            try {
                // 重置分页状态
                _hasMoreDataFlow.value = true
                currentPage = 0

                // 更新资源信息
                _resourceIdFlow.value = songId
                _resourceTypeFlow.value = TYPE_SONG

                // 使用旧的API（兼容）
                val commentList = musicRepository.getComments(songId, 0, pageSize)
                _commentsFlow.value = commentList

                // 检查是否有更多数据
                _hasMoreDataFlow.value = commentList.size >= pageSize
            } catch (e: Exception) {
                Log.e(TAG, "加载评论失败", e)
            }
        }
    }

    /**
     * 加载评论（使用新的API）
     * @param type 资源类型
     * @param id 资源ID
     * @param forceRefresh 是否强制刷新
     */
    fun loadCommentsByType(type: Int, id: Long, forceRefresh: Boolean = false) {
        if (id == 0L) return

        // 重置状态
        if (forceRefresh || id != _resourceIdFlow.value || type != _resourceTypeFlow.value) {
            _commentsFlow.value = emptyList()
            _hotCommentsFlow.value = emptyList()
            _commentCountFlow.value = 0
            _hasMoreDataFlow.value = true
            currentPage = 0
        }

        // 更新资源信息
        _resourceIdFlow.value = id
        _resourceTypeFlow.value = type

        // 加载评论
        viewModelScope.launch {
            try {
                when (type) {
                    TYPE_SONG -> {
                        commentRepository.getSongComments(
                            id = id,
                            limit = pageSize,
                            offset = currentPage * pageSize,
                            forceRefresh = forceRefresh,
                        ).collect { result ->
                            try {
                                handleCommentResult(result, forceRefresh)
                            } catch (e: Exception) {
                                Log.e(TAG, "处理评论结果失败", e)
                                _errorMessage.value = "处理评论数据失败: ${e.message}"
                            }
                        }
                    }
                    TYPE_ALBUM -> {
                        commentRepository.getAlbumComments(
                            id = id,
                            limit = pageSize,
                            offset = currentPage * pageSize,
                            forceRefresh = forceRefresh,
                        ).collect { result ->
                            handleCommentResult(result, forceRefresh)
                        }
                    }
                    TYPE_PLAYLIST -> {
                        commentRepository.getPlaylistComments(
                            id = id,
                            limit = pageSize,
                            offset = currentPage * pageSize,
                            forceRefresh = forceRefresh,
                        ).collect { result ->
                            handleCommentResult(result, forceRefresh)
                        }
                    }
                    else -> {
                        // 暂不支持其他类型，使用旧的API
                        if (type == TYPE_SONG) {
                            loadComments(id, forceRefresh)
                        } else {
                            Log.e(TAG, "暂不支持该类型的评论")
                        }
                    }
                }
            } catch (e: Exception) {
                Log.e(TAG, "加载评论失败", e)
            }
        }
    }

    /**
     * 加载更多评论
     * @param songId 歌曲ID
     * @param page 页码
     * @param pageSize 每页数量
     */
    fun loadMoreComments(songId: Long, page: Int, pageSize: Int) {
        viewModelScope.launch {
            _isLoadingMoreFlow.value = true
            try {
                val offset = page * pageSize
                val newComments = musicRepository.getComments(songId, offset, pageSize)
                // 合并评论列表
                val currentList = _commentsFlow.value
                val mergedList = currentList + newComments
                _commentsFlow.value = mergedList

                // 检查是否有更多数据
                _hasMoreDataFlow.value = newComments.size >= pageSize
            } catch (e: Exception) {
                Log.e(TAG, "加载更多评论失败", e)
            } finally {
                _isLoadingMoreFlow.value = false
            }
        }
    }

    /**
     * 加载更多评论（使用新的API）
     */
    fun loadMoreCommentsByType() {
        if (!_hasMoreDataFlow.value || _isLoadingMoreFlow.value) return

        _isLoadingMoreFlow.value = true
        currentPage++

        viewModelScope.launch {
            try {
                when (_resourceTypeFlow.value) {
                    TYPE_SONG -> {
                        commentRepository.getSongComments(
                            id = _resourceIdFlow.value,
                            limit = pageSize,
                            offset = currentPage * pageSize,
                        ).collect { result ->
                            handleMoreCommentResult(result)
                        }
                    }
                    TYPE_ALBUM -> {
                        commentRepository.getAlbumComments(
                            id = _resourceIdFlow.value,
                            limit = pageSize,
                            offset = currentPage * pageSize,
                        ).collect { result ->
                            handleMoreCommentResult(result)
                        }
                    }
                    TYPE_PLAYLIST -> {
                        commentRepository.getPlaylistComments(
                            id = _resourceIdFlow.value,
                            limit = pageSize,
                            offset = currentPage * pageSize,
                        ).collect { result ->
                            handleMoreCommentResult(result)
                        }
                    }
                    else -> {
                        // 暂不支持其他类型，使用旧的API
                        if (_resourceTypeFlow.value == TYPE_SONG) {
                            loadMoreComments(_resourceIdFlow.value, currentPage, pageSize)
                        } else {
                            Log.e(TAG, "暂不支持该类型的评论")
                        }
                    }
                }
            } catch (e: Exception) {
                Log.e(TAG, "加载更多评论失败", e)
            } finally {
                _isLoadingMoreFlow.value = false
            }
        }
    }

    /**
     * 发送评论
     * @param songId 歌曲ID
     * @param content 评论内容
     */
    fun sendComment(songId: Long, content: String) {
        viewModelScope.launch {
            try {
                val success = musicRepository.sendComment(songId, content)
                if (success) {
                    // 设置评论发送成功状态
                    _commentSentFlow.value = true
                    // 重新加载评论列表
                    loadComments(songId, true)
                } else {
                    Log.e(TAG, "发送评论失败")
                }
            } catch (e: Exception) {
                Log.e(TAG, "发送评论失败", e)
            }
        }
    }

    /**
     * 发送评论（使用新的API）
     * @param content 评论内容
     * @param commentId 回复的评论ID（如果是回复评论）
     */
    fun sendCommentByType(content: String, commentId: Long? = null) {
        if (content.isBlank()) {
            Log.e(TAG, "评论内容不能为空")
            return
        }

        viewModelScope.launch {
            try {
                commentRepository.sendComment(
                    type = _resourceTypeFlow.value,
                    id = _resourceIdFlow.value,
                    content = content,
                    commentId = commentId,
                ).collect { result ->
                    when (result) {
                        is NetworkResult.Success -> {
                            // 设置评论发送成功状态
                            _commentSentFlow.value = true
                            // 重新加载评论列表
                            loadCommentsByType(_resourceTypeFlow.value, _resourceIdFlow.value, true)
                        }
                        is NetworkResult.Error -> {
                            Log.e(TAG, "发送评论失败: ${result.message}")
                        }
                        is NetworkResult.Loading -> {
                            // 已在外层处理
                        }
                    }
                }
            } catch (e: Exception) {
                Log.e(TAG, "发送评论失败", e)
            }
        }
    }

    /**
     * 重置评论发送状态
     */
    fun resetCommentSentState() {
        _commentSentFlow.value = false
    }

    /**
     * 点赞评论
     * @param commentId 评论ID
     */
    fun likeComment(commentId: Long) {
        viewModelScope.launch {
            try {
                val success = musicRepository.likeComment(commentId)
                if (success) {
                    // 更新评论列表中的点赞状态
                    val currentList = _commentsFlow.value.toMutableList()
                    val updatedList = currentList.map { comment ->
                        if (comment.commentId == commentId) {
                            comment.copy(
                                liked = !comment.liked,
                                likeCount = if (comment.liked) comment.likeCount - 1 else comment.likeCount + 1,
                            )
                        } else {
                            comment
                        }
                    }
                    _commentsFlow.value = updatedList
                }
            } catch (e: Exception) {
                Log.e(TAG, "点赞评论失败", e)
            }
        }
    }

    /**
     * 点赞评论（使用新的API）
     * @param commentId 评论ID
     * @param like 是否点赞
     */
    fun likeCommentByType(commentId: Long, like: Boolean) {
        viewModelScope.launch {
            try {
                commentRepository.likeComment(
                    type = _resourceTypeFlow.value,
                    id = _resourceIdFlow.value,
                    commentId = commentId,
                    like = like,
                ).collect { result ->
                    when (result) {
                        is NetworkResult.Success -> {
                            // 更新评论列表中的点赞状态
                            updateCommentLikeStatus(commentId, like)
                        }
                        is NetworkResult.Error -> {
                            Log.e(TAG, "点赞评论失败: ${result.message}")
                        }
                        is NetworkResult.Loading -> {
                            // 不显示加载状态
                        }
                    }
                }
            } catch (e: Exception) {
                Log.e(TAG, "点赞评论失败", e)
            }
        }
    }

    /**
     * 处理评论结果
     */
    private fun handleCommentResult(result: NetworkResult<CommentResponse>, forceRefresh: Boolean) {
        when (result) {
            is NetworkResult.Success -> {
                val response = result.data

                // 更新热门评论
                if (forceRefresh) {
                    _hotCommentsFlow.value = response.hotComments?.map { it.toComment() } ?: emptyList()
                }

                // 更新评论列表
                if (forceRefresh) {
                    _commentsFlow.value = response.comments?.map { it.toComment() } ?: emptyList()
                } else {
                    _commentsFlow.value = _commentsFlow.value + (response.comments?.map { it.toComment() } ?: emptyList())
                }

                // 更新评论总数
                _commentCountFlow.value = response.total

                // 更新是否还有更多
                _hasMoreDataFlow.value = response.hasMore
            }
            is NetworkResult.Error -> {
                Log.e(TAG, "加载评论失败: ${result.message}")
            }
            is NetworkResult.Loading -> {
                // 已在外层处理
            }
        }
    }

    /**
     * 处理加载更多评论结果
     */
    private fun handleMoreCommentResult(result: NetworkResult<CommentResponse>) {
        when (result) {
            is NetworkResult.Success -> {
                val response = result.data

                // 更新评论列表
                _commentsFlow.value = _commentsFlow.value + (response.comments?.map { it.toComment() } ?: emptyList())

                // 更新是否还有更多
                _hasMoreDataFlow.value = response.hasMore

                _isLoadingMoreFlow.value = false
            }
            is NetworkResult.Error -> {
                _isLoadingMoreFlow.value = false
                Log.e(TAG, "加载更多评论失败: ${result.message}")
            }
            is NetworkResult.Loading -> {
                // 已在外层处理
            }
        }
    }

    /**
     * 更新评论点赞状态
     */
    private fun updateCommentLikeStatus(commentId: Long, like: Boolean) {
        // 更新热门评论
        val updatedHotComments = _hotCommentsFlow.value.map { comment ->
            if (comment.commentId == commentId) {
                comment.copy(
                    liked = like,
                    likeCount = if (like) comment.likeCount + 1 else comment.likeCount - 1,
                )
            } else {
                comment
            }
        }
        _hotCommentsFlow.value = updatedHotComments

        // 更新普通评论
        val updatedComments = _commentsFlow.value.map { comment ->
            if (comment.commentId == commentId) {
                comment.copy(
                    liked = like,
                    likeCount = if (like) comment.likeCount + 1 else comment.likeCount - 1,
                )
            } else {
                comment
            }
        }
        _commentsFlow.value = updatedComments
    }
}
