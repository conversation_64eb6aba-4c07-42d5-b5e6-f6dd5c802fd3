# JUnit 5 配置文件
# 严格按照ponymusic-master项目测试标准配置

# 启用JUnit 5测试引擎
junit.jupiter.testinstance.lifecycle.default=per_class

# 并行执行配置
junit.jupiter.execution.parallel.enabled=true
junit.jupiter.execution.parallel.mode.default=concurrent
junit.jupiter.execution.parallel.mode.classes.default=concurrent

# 测试超时配置
junit.jupiter.execution.timeout.default=30s
junit.jupiter.execution.timeout.testable.method.default=10s

# 测试显示名称配置
junit.jupiter.displayname.generator.default=org.junit.jupiter.api.DisplayNameGenerator$ReplaceUnderscores

# 测试条件配置
junit.jupiter.conditions.deactivate=org.junit.*DisabledCondition

# 扩展自动检测
junit.jupiter.extensions.autodetection.enabled=true

# 测试实例后处理
junit.jupiter.testinstance.postconstruct.timeout=5s
junit.jupiter.testinstance.predestroy.timeout=5s
