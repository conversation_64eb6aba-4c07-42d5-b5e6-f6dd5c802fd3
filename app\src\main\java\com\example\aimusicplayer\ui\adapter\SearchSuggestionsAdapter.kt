package com.example.aimusicplayer.ui.adapter

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.TextView
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.ListAdapter
import androidx.recyclerview.widget.RecyclerView
import com.example.aimusicplayer.R
import com.example.aimusicplayer.data.model.SearchItem

/**
 * 搜索建议适配器 - 重构版本
 * 支持搜索历史和API建议的混合显示
 */
class SearchSuggestionsAdapter(
    private val onItemClick: (SearchItem) -> Unit,
    private val onHistoryLongClick: (SearchItem.HistoryItem) -> Unit = {},
) : ListAdapter<SearchItem, SearchSuggestionsAdapter.SearchItemViewHolder>(DiffCallback) {

    companion object {
        private val DiffCallback = object : DiffUtil.ItemCallback<SearchItem>() {
            override fun areItemsTheSame(oldItem: SearchItem, newItem: SearchItem): Boolean {
                return oldItem.id == newItem.id
            }

            override fun areContentsTheSame(oldItem: SearchItem, newItem: SearchItem): Boolean {
                return oldItem == newItem
            }
        }
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): SearchItemViewHolder {
        val view = LayoutInflater.from(parent.context)
            .inflate(R.layout.item_search_suggestion, parent, false)
        return SearchItemViewHolder(view)
    }

    override fun onBindViewHolder(holder: SearchItemViewHolder, position: Int) {
        holder.bind(getItem(position))
    }

    inner class SearchItemViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        private val suggestionText: TextView = itemView.findViewById(R.id.suggestion_text)
        private val iconView: ImageView = itemView.findViewById(R.id.iv_icon)

        fun bind(item: SearchItem) {
            suggestionText.text = item.text

            // 根据类型设置不同的图标和样式
            when (item) {
                is SearchItem.HistoryItem -> {
                    // 历史记录：使用历史图标
                    iconView.setImageDrawable(
                        ContextCompat.getDrawable(itemView.context, R.drawable.ic_history),
                    )
                    suggestionText.setTextColor(
                        ContextCompat.getColor(itemView.context, R.color.text_primary),
                    )

                    // 设置点击和长按事件
                    itemView.setOnClickListener {
                        onItemClick(item)
                    }
                    itemView.setOnLongClickListener {
                        onHistoryLongClick(item)
                        true
                    }
                }

                is SearchItem.SuggestionItem -> {
                    // API建议：使用搜索图标
                    iconView.setImageDrawable(
                        ContextCompat.getDrawable(itemView.context, R.drawable.ic_search),
                    )
                    suggestionText.setTextColor(
                        ContextCompat.getColor(itemView.context, R.color.text_secondary),
                    )

                    // 只设置点击事件
                    itemView.setOnClickListener {
                        onItemClick(item)
                    }
                    itemView.setOnLongClickListener(null)
                }
            }
        }
    }
}
