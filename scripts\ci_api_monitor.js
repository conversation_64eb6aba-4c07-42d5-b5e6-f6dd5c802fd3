#!/usr/bin/env node

/**
 * CI/CD API监控脚本 - 基于综合测试脚本
 * 自动监控API状态，适用于持续集成流程
 *
 * 使用方法：
 * node scripts/ci_api_monitor.js [--env=prod|dev] [--timeout=30] [--retry=3]
 *
 * 退出码：
 * 0 - 所有API正常
 * 1 - 部分API异常但在可接受范围内
 * 2 - 严重API异常，需要立即处理
 */

const path = require('path');
const { spawn } = require('child_process');

// 命令行参数解析
const args = process.argv.slice(2);
const config = {
    env: 'prod',
    timeout: 10,
    retry: 1,
    verbose: false
};

args.forEach(arg => {
    if (arg.startsWith('--env=')) {
        config.env = arg.split('=')[1];
    } else if (arg.startsWith('--timeout=')) {
        config.timeout = parseInt(arg.split('=')[1]);
    } else if (arg.startsWith('--retry=')) {
        config.retry = parseInt(arg.split('=')[1]);
    } else if (arg === '--verbose') {
        config.verbose = true;
    }
});

// 主函数 - 调用综合测试脚本
async function main() {
    console.log(`[${new Date().toISOString()}] [INFO] 开始API监控 - 环境: ${config.env}`);
    console.log(`[${new Date().toISOString()}] [INFO] 配置: 超时=${config.timeout}s, 重试=${config.retry}次`);

    const comprehensiveTestPath = path.join(__dirname, '..', 'comprehensive_test.js');

    // 构建参数
    const testArgs = [
        comprehensiveTestPath,
        '--mode=monitor',
        `--env=${config.env}`,
        `--timeout=${config.timeout}`,
        `--retry=${config.retry}`
    ];

    if (config.verbose) {
        testArgs.push('--verbose');
    }

    // 启动综合测试脚本
    const child = spawn('node', testArgs, {
        stdio: 'inherit',
        cwd: path.join(__dirname, '..')
    });

    child.on('close', (code) => {
        console.log(`[${new Date().toISOString()}] [INFO] API监控完成，退出码: ${code}`);
        process.exit(code);
    });

    child.on('error', (error) => {
        console.error(`[${new Date().toISOString()}] [ERROR] 启动综合测试脚本失败: ${error.message}`);
        process.exit(2);
    });
}

// 启动监控
if (require.main === module) {
    main();
}
