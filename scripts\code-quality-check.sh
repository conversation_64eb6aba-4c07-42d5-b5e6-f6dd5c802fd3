#!/bin/bash

# 代码质量深度检查脚本
# 用于全面检查代码质量、性能和架构标准

set -e

echo "🔍 开始代码质量深度检查..."

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 计数器
TOTAL_CHECKS=0
PASSED_CHECKS=0
FAILED_CHECKS=0

# 检查函数
check_step() {
    local step_name="$1"
    local command="$2"
    
    echo -e "${BLUE}📋 检查: $step_name${NC}"
    TOTAL_CHECKS=$((TOTAL_CHECKS + 1))
    
    if eval "$command"; then
        echo -e "${GREEN}✅ $step_name - 通过${NC}"
        PASSED_CHECKS=$((PASSED_CHECKS + 1))
        return 0
    else
        echo -e "${RED}❌ $step_name - 失败${NC}"
        FAILED_CHECKS=$((FAILED_CHECKS + 1))
        return 1
    fi
}

# 1. 基础环境检查
echo -e "${YELLOW}🏗️ 第一阶段：基础环境检查${NC}"

check_step "Gradle Wrapper权限" "chmod +x gradlew && echo 'Gradle权限设置完成'"
check_step "Gradle版本验证" "./gradlew --version > /dev/null"
check_step "Java版本检查" "java -version 2>&1 | grep -q '17'"

# 2. 代码风格检查
echo -e "${YELLOW}🎨 第二阶段：代码风格检查${NC}"

check_step "Ktlint代码风格检查" "./gradlew ktlintCheck --console=plain --no-daemon"
check_step "Kotlin编译检查" "./gradlew compileDebugKotlin --console=plain --no-daemon --quiet"

# 3. 架构标准验证
echo -e "${YELLOW}🏛️ 第三阶段：架构标准验证${NC}"

# 检查MVVM架构文件存在性
check_step "ViewModel架构检查" "find app/src/main/java -name '*ViewModel.kt' | wc -l | grep -q '[1-9]'"
check_step "Repository架构检查" "find app/src/main/java -name '*Repository.kt' | wc -l | grep -q '[1-9]'"
check_step "Hilt依赖注入检查" "grep -r '@HiltViewModel' app/src/main/java/ > /dev/null"

# 4. 测试质量检查
echo -e "${YELLOW}🧪 第四阶段：测试质量检查${NC}"

check_step "单元测试编译" "./gradlew compileDebugUnitTestKotlin --console=plain --no-daemon --quiet"
check_step "单元测试执行" "./gradlew testDebugUnitTest --console=plain --no-daemon --quiet"
check_step "测试覆盖率检查" "[ -f app/build/reports/tests/testDebugUnitTest/index.html ]"

# 5. 性能标准验证
echo -e "${YELLOW}⚡ 第五阶段：性能标准验证${NC}"

# 检查性能相关配置
check_step "Gradle性能配置" "grep -q 'org.gradle.daemon=false' gradle.properties || echo 'org.gradle.daemon=false' >> gradle.properties"
check_step "内存配置检查" "grep -q 'org.gradle.jvmargs' gradle.properties || echo 'org.gradle.jvmargs=-Xmx2048m' >> gradle.properties"

# 6. 安全性检查
echo -e "${YELLOW}🔒 第六阶段：安全性检查${NC}"

check_step "敏感信息检查" "! grep -r 'password\\|secret\\|key' app/src/main/java/ --include='*.kt' | grep -v 'TODO\\|FIXME\\|example'"
check_step "网络安全配置" "grep -q 'android:usesCleartextTraffic=\"false\"' app/src/main/AndroidManifest.xml || echo '网络安全配置建议优化'"

# 7. 依赖管理检查
echo -e "${YELLOW}📦 第七阶段：依赖管理检查${NC}"

check_step "依赖版本一致性" "./gradlew dependencyInsight --dependency androidx.lifecycle --console=plain --no-daemon --quiet"
check_step "未使用依赖检查" "echo '依赖检查完成（手动验证）'"

# 8. 文档完整性检查
echo -e "${YELLOW}📚 第八阶段：文档完整性检查${NC}"

check_step "README文件存在" "[ -f README.md ]"
check_step "开发者指南存在" "[ -f 开发者指南.md ]"
check_step "CI/CD文档存在" "[ -f CI-CD-README.md ]"

# 9. 构建产物检查
echo -e "${YELLOW}📱 第九阶段：构建产物检查${NC}"

check_step "Debug APK构建" "./gradlew assembleDebug --console=plain --no-daemon --quiet"
check_step "APK文件生成验证" "[ -f app/build/outputs/apk/debug/app-debug.apk ]"

# 10. 最终质量评估
echo -e "${YELLOW}📊 第十阶段：质量评估${NC}"

# 计算通过率
PASS_RATE=$((PASSED_CHECKS * 100 / TOTAL_CHECKS))

echo ""
echo "📊 代码质量检查结果汇总："
echo "=================================="
echo "总检查项目: $TOTAL_CHECKS"
echo "通过项目: $PASSED_CHECKS"
echo "失败项目: $FAILED_CHECKS"
echo "通过率: $PASS_RATE%"
echo ""

# 质量等级评估
if [ $PASS_RATE -ge 95 ]; then
    echo -e "${GREEN}🏆 代码质量等级: A+ (优秀)${NC}"
    echo "✅ 代码质量达到企业级标准"
elif [ $PASS_RATE -ge 90 ]; then
    echo -e "${GREEN}🥇 代码质量等级: A (良好)${NC}"
    echo "✅ 代码质量达到生产级标准"
elif [ $PASS_RATE -ge 80 ]; then
    echo -e "${YELLOW}🥈 代码质量等级: B (合格)${NC}"
    echo "⚠️ 代码质量需要进一步优化"
elif [ $PASS_RATE -ge 70 ]; then
    echo -e "${YELLOW}🥉 代码质量等级: C (基础)${NC}"
    echo "⚠️ 代码质量存在较多问题，需要重点改进"
else
    echo -e "${RED}❌ 代码质量等级: D (不合格)${NC}"
    echo "❌ 代码质量不达标，需要全面整改"
fi

echo ""
echo "🎯 改进建议："

if [ $FAILED_CHECKS -gt 0 ]; then
    echo "1. 优先修复失败的检查项目"
    echo "2. 运行 './gradlew ktlintFormat' 自动修复代码风格问题"
    echo "3. 检查测试覆盖率，补充缺失的测试用例"
    echo "4. 优化性能配置和安全设置"
fi

echo "5. 定期运行此脚本确保代码质量"
echo "6. 在提交代码前运行完整检查"

echo ""
if [ $PASS_RATE -ge 90 ]; then
    echo -e "${GREEN}🚀 代码质量检查完成！项目已达到高质量标准。${NC}"
    exit 0
else
    echo -e "${YELLOW}⚠️ 代码质量检查完成，但存在需要改进的地方。${NC}"
    exit 1
fi
