#!/usr/bin/env node

/**
 * 全面的登录API测试脚本
 * 测试三种登录方式的完整流程
 * 作者: AI Assistant
 * 日期: 2025-01-26
 */

const https = require('https');
const fs = require('fs');

// 服务器配置
const SERVERS = {
    primary: 'ncm.zhenxin.me',
    backup: '1355831898-4499wupl9z.ap-guangzhou.tencentscf.com'
};

// 测试配置
const CONFIG = {
    timeout: 15000,
    retry: 2,
    verbose: process.argv.includes('--verbose'),
    saveResults: true
};

// 颜色输出
const colors = {
    green: (text) => process.env.CI ? text : `\x1b[32m${text}\x1b[0m`,
    red: (text) => process.env.CI ? text : `\x1b[31m${text}\x1b[0m`,
    yellow: (text) => process.env.CI ? text : `\x1b[33m${text}\x1b[0m`,
    blue: (text) => process.env.CI ? text : `\x1b[34m${text}\x1b[0m`,
    cyan: (text) => process.env.CI ? text : `\x1b[36m${text}\x1b[0m`,
    bold: (text) => process.env.CI ? text : `\x1b[1m${text}\x1b[0m`
};

// 日志函数
function log(level, message, details = null) {
    const timestamp = new Date().toISOString();
    const prefix = `[${timestamp}] [${level.toUpperCase()}]`;

    switch (level) {
        case 'info':
            console.log(colors.blue(prefix), message);
            break;
        case 'warn':
            console.warn(colors.yellow(prefix), message);
            break;
        case 'error':
            console.error(colors.red(prefix), message);
            break;
        case 'success':
            console.log(colors.green(prefix), message);
            break;
        case 'debug':
            if (CONFIG.verbose) {
                console.log(colors.cyan(prefix), message);
            }
            break;
        default:
            console.log(prefix, message);
    }

    if (details && CONFIG.verbose) {
        console.log(colors.cyan('  详细信息:'), details);
    }
}

// 检查嵌套字段是否存在
function hasNestedProperty(obj, path) {
    try {
        const keys = path.split(/[\.\[\]]+/).filter(key => key !== '');
        let current = obj;

        for (const key of keys) {
            if (current && typeof current === 'object' && key in current) {
                current = current[key];
            } else {
                return false;
            }
        }
        return true;
    } catch (e) {
        return false;
    }
}

// 执行HTTP请求
function makeRequest(hostname, path, method = 'GET', timeout = CONFIG.timeout) {
    return new Promise((resolve) => {
        const startTime = Date.now();

        const options = {
            hostname: hostname,
            path: path,
            method: method,
            timeout: timeout,
            headers: {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
                'Accept': 'application/json, text/plain, */*',
                'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
                'Accept-Encoding': 'gzip, deflate, br',
                'Connection': 'keep-alive'
            }
        };

        log('debug', `请求 ${hostname}${path} (${method})`);

        const req = https.request(options, (res) => {
            let data = '';
            res.on('data', (chunk) => {
                data += chunk;
            });

            res.on('end', () => {
                const responseTime = Date.now() - startTime;
                let result = {
                    success: false,
                    responseTime,
                    statusCode: res.statusCode,
                    error: null,
                    data: null,
                    dataSize: data.length
                };

                try {
                    if (res.statusCode === 200) {
                        const json = JSON.parse(data);
                        result.success = true;
                        result.data = json;
                    } else {
                        result.error = `HTTP状态码错误: ${res.statusCode}`;
                        // 尝试解析错误响应
                        try {
                            result.data = JSON.parse(data);
                        } catch (e) {
                            result.rawData = data.substring(0, 200);
                        }
                    }
                } catch (e) {
                    result.error = `JSON解析失败: ${e.message}`;
                    result.rawData = data.substring(0, 200);
                }

                log('debug', `${hostname} 响应: ${result.success ? '成功' : '失败'} (${responseTime}ms)`);
                resolve(result);
            });
        });

        req.on('error', (e) => {
            const responseTime = Date.now() - startTime;
            log('debug', `${hostname} 请求错误: ${e.message}`);
            resolve({
                success: false,
                responseTime,
                error: `请求失败: ${e.message}`,
                data: null
            });
        });

        req.on('timeout', () => {
            req.destroy();
            const responseTime = Date.now() - startTime;
            log('debug', `${hostname} 请求超时`);
            resolve({
                success: false,
                responseTime,
                error: '请求超时',
                data: null
            });
        });

        req.end();
    });
}

// 重试机制
async function makeRequestWithRetry(hostname, path, method = 'GET', expectedFields = [], description = '') {
    let lastError = null;

    for (let attempt = 1; attempt <= CONFIG.retry; attempt++) {
        try {
            const result = await makeRequest(hostname, path, method);

            if (result.success) {
                // 检查数据结构
                if (expectedFields.length > 0 && result.data) {
                    const fieldCheck = expectedFields.every(field =>
                        hasNestedProperty(result.data, field)
                    );

                    if (!fieldCheck) {
                        result.structureValid = false;
                        result.error = `数据结构不匹配，缺少字段: ${expectedFields.join(', ')}`;
                    } else {
                        result.structureValid = true;
                    }
                } else {
                    result.structureValid = true;
                }

                if (result.structureValid) {
                    if (attempt > 1) {
                        log('info', `${description} 在第${attempt}次尝试后成功`);
                    }
                    return result;
                }
            }

            lastError = result;

            if (attempt < CONFIG.retry) {
                log('warn', `${description} 第${attempt}次尝试失败，${CONFIG.retry - attempt}次重试剩余`);
                await new Promise(resolve => setTimeout(resolve, 1000 * attempt));
            }
        } catch (error) {
            lastError = { error: error.message };
        }
    }

    return lastError;
}

// 测试结果存储
let testResults = {
    timestamp: new Date().toISOString(),
    summary: {
        total: 0,
        passed: 0,
        failed: 0,
        primarySuccess: 0,
        backupSuccess: 0
    },
    loginTests: {}
};

// 登录API测试定义
const LOGIN_API_TESTS = {
    // 1. 游客登录
    guestLogin: {
        path: '/register/anonimous',
        method: 'GET',
        expectedFields: ['code'],
        description: '游客登录接口',
        validateResponse: (data) => {
            if (data.code === 200) {
                return {
                    valid: true,
                    message: '游客登录成功',
                    hasCookie: !!data.cookie
                };
            }
            return {
                valid: false,
                message: `游客登录失败: ${data.msg || '未知错误'}`
            };
        }
    },

    // 2. 二维码登录流程
    qrKey: {
        path: '/login/qr/key',
        method: 'GET',
        expectedFields: ['code', 'data'],
        description: '获取二维码key',
        validateResponse: (data) => {
            if (data.code === 200 && data.data && data.data.unikey) {
                return {
                    valid: true,
                    message: '获取二维码key成功',
                    key: data.data.unikey
                };
            }
            return {
                valid: false,
                message: `获取二维码key失败: ${data.msg || '未知错误'}`
            };
        }
    },

    qrCreate: {
        path: '/login/qr/create?key=test&qrimg=true',
        method: 'GET',
        expectedFields: ['code', 'data'],
        description: '生成二维码',
        validateResponse: (data) => {
            if (data.code === 200 && data.data) {
                return {
                    valid: true,
                    message: '生成二维码成功',
                    hasQrImg: !!data.data.qrimg
                };
            }
            return {
                valid: false,
                message: `生成二维码失败: ${data.msg || '未知错误'}`
            };
        }
    },

    qrCheck: {
        path: '/login/qr/check?key=test',
        method: 'GET',
        expectedFields: ['code'],
        description: '检查二维码状态',
        validateResponse: (data) => {
            const validCodes = [800, 801, 802, 803]; // 过期、待扫、待确认、成功
            if (validCodes.includes(data.code)) {
                const statusMap = {
                    800: '二维码过期',
                    801: '等待扫码',
                    802: '待确认',
                    803: '授权登录成功'
                };
                return {
                    valid: true,
                    message: statusMap[data.code] || '未知状态',
                    status: data.code
                };
            }
            return {
                valid: false,
                message: `二维码状态检查失败: ${data.msg || '未知错误'}`
            };
        }
    },

    // 3. 手机号登录流程
    captchaSent: {
        path: '/captcha/sent?phone=***********',
        method: 'GET',
        expectedFields: ['code'],
        description: '发送验证码',
        validateResponse: (data) => {
            if (data.code === 200) {
                return {
                    valid: true,
                    message: '验证码发送成功'
                };
            } else if (data.code === 400) {
                return {
                    valid: true,
                    message: '验证码发送限制（正常业务逻辑）',
                    businessLogic: true
                };
            }
            return {
                valid: false,
                message: `发送验证码失败: ${data.msg || '未知错误'}`
            };
        }
    },

    captchaVerify: {
        path: '/captcha/verify?phone=***********&captcha=1234',
        method: 'GET',
        expectedFields: ['code'],
        description: '验证验证码',
        validateResponse: (data) => {
            if (data.code === 503) {
                return {
                    valid: true,
                    message: '验证码错误（正常业务逻辑）',
                    businessLogic: true
                };
            } else if (data.code === 200) {
                return {
                    valid: true,
                    message: '验证码验证成功'
                };
            }
            return {
                valid: false,
                message: `验证验证码失败: ${data.msg || '未知错误'}`
            };
        }
    },

    loginCellphone: {
        path: '/login/cellphone?phone=***********&captcha=1234',
        method: 'GET',
        expectedFields: ['code'],
        description: '手机号登录',
        validateResponse: (data) => {
            if (data.code === 503) {
                return {
                    valid: true,
                    message: '验证码错误（正常业务逻辑）',
                    businessLogic: true
                };
            } else if (data.code === 200) {
                return {
                    valid: true,
                    message: '手机号登录成功',
                    hasCookie: !!data.cookie
                };
            }
            return {
                valid: false,
                message: `手机号登录失败: ${data.msg || '未知错误'}`
            };
        }
    },

    // 4. 登录状态相关
    loginStatus: {
        path: '/login/status',
        method: 'GET',
        expectedFields: ['data'],
        description: '检查登录状态',
        validateResponse: (data) => {
            if (data.data) {
                return {
                    valid: true,
                    message: '登录状态检查成功',
                    hasAccount: !!data.data.account
                };
            }
            return {
                valid: false,
                message: `登录状态检查失败: ${data.msg || '未知错误'}`
            };
        }
    },

    userAccount: {
        path: '/user/account',
        method: 'GET',
        expectedFields: ['code'],
        description: '获取用户账号信息',
        validateResponse: (data) => {
            if (data.code === 200) {
                return {
                    valid: true,
                    message: '获取用户账号信息成功',
                    hasAccount: !!data.account
                };
            }
            return {
                valid: false,
                message: `获取用户账号信息失败: ${data.msg || '未知错误'}`
            };
        }
    }
};

// 执行单个API测试
async function testSingleApi(serverKey, hostname, apiKey, apiConfig) {
    log('info', `测试 ${apiConfig.description} (${serverKey})`);

    const path = apiConfig.path + (apiConfig.path.includes('?') ? '&' : '?') + 'timestamp=' + Date.now();
    const result = await makeRequestWithRetry(
        hostname,
        path,
        apiConfig.method,
        apiConfig.expectedFields,
        apiConfig.description
    );

    // 验证响应
    let validation = { valid: false, message: '未知错误' };
    if (result.success && result.data && apiConfig.validateResponse) {
        validation = apiConfig.validateResponse(result.data);
    } else if (result.error) {
        validation = { valid: false, message: result.error };
    }

    const testResult = {
        ...result,
        validation,
        server: serverKey,
        hostname,
        apiKey,
        description: apiConfig.description
    };

    // 记录结果
    if (!testResults.loginTests[apiKey]) {
        testResults.loginTests[apiKey] = {};
    }
    testResults.loginTests[apiKey][serverKey] = testResult;

    // 更新统计
    testResults.summary.total++;
    if (validation.valid) {
        testResults.summary.passed++;
        if (serverKey === 'primary') {
            testResults.summary.primarySuccess++;
        } else {
            testResults.summary.backupSuccess++;
        }

        const statusIcon = validation.businessLogic ? '⚠️' : '✅';
        log('success', `${statusIcon} ${apiConfig.description} - ${validation.message}`);
    } else {
        testResults.summary.failed++;
        log('error', `❌ ${apiConfig.description} - ${validation.message}`);
    }

    return testResult;
}

// 执行所有登录API测试
async function runLoginApiTests() {
    log('info', colors.bold('🚀 开始全面登录API测试'));
    console.log('================================================================================');

    const apiKeys = Object.keys(LOGIN_API_TESTS);
    const servers = Object.entries(SERVERS);

    for (const [serverKey, hostname] of servers) {
        log('info', colors.cyan(`🌐 测试服务器: ${serverKey} (${hostname})`));
        console.log('------------------------------------------------------------');

        for (const apiKey of apiKeys) {
            const apiConfig = LOGIN_API_TESTS[apiKey];
            await testSingleApi(serverKey, hostname, apiKey, apiConfig);
        }

        console.log('');
    }
}

// 生成测试报告
function generateReport() {
    log('info', colors.bold('📊 登录API测试报告'));
    console.log('================================================================================');

    // 总体统计
    const { total, passed, failed, primarySuccess, backupSuccess } = testResults.summary;
    const successRate = total > 0 ? ((passed / total) * 100).toFixed(1) : 0;
    const primaryRate = Object.keys(LOGIN_API_TESTS).length > 0 ?
        ((primarySuccess / Object.keys(LOGIN_API_TESTS).length) * 100).toFixed(1) : 0;
    const backupRate = Object.keys(LOGIN_API_TESTS).length > 0 ?
        ((backupSuccess / Object.keys(LOGIN_API_TESTS).length) * 100).toFixed(1) : 0;

    console.log(`📈 总体统计:`);
    console.log(`   总测试数: ${total}`);
    console.log(`   成功: ${colors.green(passed)} (${successRate}%)`);
    console.log(`   失败: ${colors.red(failed)}`);
    console.log(`   主服务器成功率: ${colors.blue(primaryRate)}%`);
    console.log(`   备用服务器成功率: ${colors.blue(backupRate)}%`);
    console.log('');

    // 按功能分组报告
    const groups = {
        '游客登录': ['guestLogin'],
        '二维码登录': ['qrKey', 'qrCreate', 'qrCheck'],
        '手机号登录': ['captchaSent', 'captchaVerify', 'loginCellphone'],
        '登录状态': ['loginStatus', 'userAccount']
    };

    Object.entries(groups).forEach(([groupName, apiKeys]) => {
        console.log(`🔍 ${groupName}:`);

        apiKeys.forEach(apiKey => {
            const tests = testResults.loginTests[apiKey];
            if (tests) {
                const primaryResult = tests.primary;
                const backupResult = tests.backup;

                const primaryStatus = primaryResult?.validation?.valid ?
                    (primaryResult.validation.businessLogic ? '⚠️' : '✅') : '❌';
                const backupStatus = backupResult?.validation?.valid ?
                    (backupResult.validation.businessLogic ? '⚠️' : '✅') : '❌';

                console.log(`   ${LOGIN_API_TESTS[apiKey].description}:`);
                console.log(`     主服务器: ${primaryStatus} ${primaryResult?.validation?.message || '未测试'}`);
                console.log(`     备用服务器: ${backupStatus} ${backupResult?.validation?.message || '未测试'}`);
            }
        });
        console.log('');
    });

    // 关键发现
    console.log(`💡 关键发现:`);

    // 检查游客登录
    const guestTests = testResults.loginTests.guestLogin;
    if (guestTests) {
        const primaryGuest = guestTests.primary;
        const backupGuest = guestTests.backup;

        if (primaryGuest?.validation?.valid && primaryGuest.validation.hasCookie) {
            console.log(`   ✅ 主服务器游客登录正常，可获取Cookie`);
        } else if (backupGuest?.validation?.valid && backupGuest.validation.hasCookie) {
            console.log(`   ⚠️  主服务器游客登录异常，备用服务器正常`);
        } else {
            console.log(`   ❌ 游客登录功能异常，需要检查`);
        }
    }

    // 检查二维码登录流程
    const qrApis = ['qrKey', 'qrCreate', 'qrCheck'];
    const qrResults = qrApis.map(api => testResults.loginTests[api]);
    const qrWorking = qrResults.some(result =>
        result?.primary?.validation?.valid || result?.backup?.validation?.valid
    );

    if (qrWorking) {
        console.log(`   ✅ 二维码登录流程API可用`);
    } else {
        console.log(`   ❌ 二维码登录流程API异常`);
    }

    // 检查手机号登录流程
    const phoneApis = ['captchaSent', 'captchaVerify', 'loginCellphone'];
    const phoneResults = phoneApis.map(api => testResults.loginTests[api]);
    const phoneWorking = phoneResults.some(result =>
        result?.primary?.validation?.valid || result?.backup?.validation?.valid
    );

    if (phoneWorking) {
        console.log(`   ✅ 手机号登录流程API可用`);
    } else {
        console.log(`   ❌ 手机号登录流程API异常`);
    }

    console.log('');
}

// 保存测试结果
function saveResults() {
    if (CONFIG.saveResults) {
        const filename = `login_api_test_report_${new Date().toISOString().replace(/[:.]/g, '-')}.json`;
        try {
            fs.writeFileSync(filename, JSON.stringify(testResults, null, 2));
            log('info', `📄 详细测试报告已保存: ${filename}`);
        } catch (error) {
            log('error', `保存测试报告失败: ${error.message}`);
        }
    }
}

// 主函数
async function main() {
    try {
        console.log(colors.bold('🔧 全面登录API测试脚本'));
        console.log('测试三种登录方式的完整流程');
        console.log('================================================================================');
        console.log('');

        await runLoginApiTests();
        generateReport();
        saveResults();

        log('success', colors.bold('✨ 登录API测试完成'));

        // 设置退出码
        const hasFailures = testResults.summary.failed > 0;
        process.exit(hasFailures ? 1 : 0);

    } catch (error) {
        log('error', `测试执行失败: ${error.message}`);
        console.error(error.stack);
        process.exit(1);
    }
}

// 执行主函数
if (require.main === module) {
    main();
}
