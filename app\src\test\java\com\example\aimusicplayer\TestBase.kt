package com.example.aimusicplayer

import androidx.arch.core.executor.testing.InstantTaskExecutorRule
import io.mockk.MockKAnnotations
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.test.TestDispatcher
import kotlinx.coroutines.test.UnconfinedTestDispatcher
import kotlinx.coroutines.test.resetMain
import kotlinx.coroutines.test.setMain
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.extension.ExtendWith

/**
 * 测试基础类
 * 提供通用的测试配置和工具方法
 *
 * 严格按照ponymusic-master项目测试标准实现
 */
@OptIn(ExperimentalCoroutinesApi::class)
@ExtendWith(InstantTaskExecutorExtension::class)
abstract class TestBase {

    // 确保LiveData和StateFlow在测试中同步执行
    @get:org.junit.Rule
    val instantTaskExecutorRule = InstantTaskExecutorRule()

    // 测试协程调度器
    protected val testDispatcher: TestDispatcher = UnconfinedTestDispatcher()

    @BeforeEach
    open fun setUp() {
        // 初始化MockK
        MockKAnnotations.init(this, relaxUnitFun = true)

        // 设置测试协程调度器
        Dispatchers.setMain(testDispatcher)
    }

    @AfterEach
    open fun tearDown() {
        // 重置协程调度器
        Dispatchers.resetMain()
    }
}

/**
 * JUnit 5扩展，用于InstantTaskExecutorRule
 */
class InstantTaskExecutorExtension :
    org.junit.jupiter.api.extension.BeforeEachCallback,
    org.junit.jupiter.api.extension.AfterEachCallback {

    private val rule = InstantTaskExecutorRule()

    override fun beforeEach(context: org.junit.jupiter.api.extension.ExtensionContext?) {
        rule.apply(
            object : org.junit.runners.model.Statement() {
                override fun evaluate() {
                    // no-op
                }
            },
            org.junit.runner.Description.createTestDescription(
                context?.testClass?.orElse(null)?.name ?: "Unknown",
                context?.displayName ?: "Unknown",
            ),
        ).evaluate()
    }

    override fun afterEach(context: org.junit.jupiter.api.extension.ExtensionContext?) {
        // InstantTaskExecutorRule doesn't need explicit cleanup
    }
}

/**
 * 测试工具类
 * 提供常用的测试辅助方法
 */
object TestUtils {

    /**
     * 创建测试用的Song对象
     */
    fun createTestSong(
        id: Long = 1L,
        name: String = "Test Song",
        artistName: String = "Test Artist",
        albumName: String = "Test Album",
        duration: Long = 180000L,
    ): com.example.aimusicplayer.data.model.Song {
        return com.example.aimusicplayer.data.model.Song(
            id = id,
            name = name,
            ar = listOf(
                com.example.aimusicplayer.data.model.Artist(
                    id = 1L,
                    name = artistName,
                ),
            ),
            al = com.example.aimusicplayer.data.model.Album(
                id = "1",
                name = albumName,
                picUrl = "https://example.com/album.jpg",
            ),
            dt = duration,
        )
    }

    /**
     * 创建测试用的PlayList对象
     */
    fun createTestPlaylist(
        id: String = "1",
        name: String = "Test Playlist",
        songCount: Int = 10,
    ): com.example.aimusicplayer.data.model.PlayList {
        return com.example.aimusicplayer.data.model.PlayList(
            id = id,
            name = name,
            songCount = songCount,
            coverImgUrl = "https://example.com/playlist.jpg",
        )
    }

    /**
     * 创建测试用的User对象
     */
    fun createTestUser(
        userId: String = "123456",
        username: String = "TestUser",
        token: String = "test_token",
        avatarUrl: String = "https://example.com/avatar.jpg",
    ): com.example.aimusicplayer.data.model.User {
        return com.example.aimusicplayer.data.model.User(
            userId = userId,
            username = username,
            token = token,
            avatarUrl = avatarUrl,
        )
    }

    /**
     * 创建测试用的LoginStatus对象
     */
    fun createTestLoginStatus(
        isLoggedIn: Boolean = true,
        userId: String = "123456",
    ): com.example.aimusicplayer.data.model.LoginStatus {
        return com.example.aimusicplayer.data.model.LoginStatus(
            isLoggedIn = isLoggedIn,
            userId = if (isLoggedIn) userId else null,
            nickname = if (isLoggedIn) "TestUser" else null,
            avatarUrl = if (isLoggedIn) "https://example.com/avatar.jpg" else null,
        )
    }
}
