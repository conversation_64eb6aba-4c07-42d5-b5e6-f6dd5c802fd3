<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:background="@android:color/transparent">

    <!-- 操作按钮区域 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:padding="12dp"
        android:gravity="center_vertical">

        <!-- 播放模式切换 -->
        <LinearLayout
            android:id="@+id/layout_play_mode"
            android:layout_width="0dp"
            android:layout_height="48dp"
            android:layout_weight="1"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            android:background="@drawable/control_button_no_ripple"
            android:clickable="true"
            android:focusable="true"
            android:paddingHorizontal="12dp">

            <ImageView
                android:id="@+id/image_play_mode"
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:src="@drawable/ic_repeat"
                app:tint="@color/text_light" />

            <TextView
                android:id="@+id/text_play_mode"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="8dp"
                android:text="列表循环"
                android:textColor="@color/text_light"
                android:textSize="14sp" />

        </LinearLayout>

        <!-- 歌曲数量 -->
        <TextView
            android:id="@+id/text_song_count"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="16dp"
            android:text="(0首)"
            android:textColor="@color/color_gray_500"
            android:textSize="14sp" />

        <!-- 随机播放按钮 -->
        <ImageButton
            android:id="@+id/btn_shuffle_playlist"
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:layout_marginEnd="8dp"
            android:background="@drawable/control_button_no_ripple"
            android:src="@drawable/ic_shuffle"
            android:contentDescription="随机播放"
            android:padding="12dp"
            android:clickable="true"
            android:focusable="true"
            app:tint="@color/text_light" />

        <!-- 清空播放列表按钮 -->
        <ImageButton
            android:id="@+id/btn_clear_playlist"
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:background="@drawable/control_button_no_ripple"
            android:src="@drawable/ic_clear"
            android:contentDescription="清空播放列表"
            android:padding="12dp"
            android:clickable="true"
            android:focusable="true"
            app:tint="@color/text_light" />

    </LinearLayout>

    <!-- 分隔线 -->
    <View
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:layout_marginHorizontal="12dp"
        android:background="@color/color_gray_700"
        android:alpha="0.5" />

    <!-- 播放列表内容 -->
    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1">

        <!-- 播放列表RecyclerView -->
        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/recycler_view_playlist"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:clipToPadding="false"
            android:paddingVertical="8dp"
            android:paddingHorizontal="12dp"
            android:scrollbars="vertical"
            android:fadeScrollbars="true"
            android:scrollbarStyle="outsideOverlay"
            tools:listitem="@layout/item_play_queue" />

        <!-- 空状态提示 -->
        <LinearLayout
            android:id="@+id/layout_empty_state"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:orientation="vertical"
            android:gravity="center"
            android:visibility="gone">

            <ImageView
                android:layout_width="64dp"
                android:layout_height="64dp"
                android:src="@drawable/ic_playlist"
                android:alpha="0.5"
                app:tint="@color/color_gray_500" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:text="播放列表为空"
                android:textColor="@color/color_gray_500"
                android:textSize="16sp" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:text="添加一些歌曲开始播放吧"
                android:textColor="@color/color_gray_600"
                android:textSize="14sp" />

        </LinearLayout>

    </FrameLayout>

</LinearLayout>
