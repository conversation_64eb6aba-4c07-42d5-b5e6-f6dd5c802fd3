package com.example.aimusicplayer.ui.widget

import android.animation.ValueAnimator
import android.content.Context
import android.graphics.*
import android.graphics.drawable.AnimationDrawable
import android.graphics.drawable.Drawable
import android.util.AttributeSet
import android.util.Log
import android.view.View
import android.view.animation.LinearInterpolator
import androidx.core.content.ContextCompat
import com.bumptech.glide.Glide
import com.bumptech.glide.request.target.CustomTarget
import com.bumptech.glide.request.transition.Transition
import com.example.aimusicplayer.R
import kotlin.math.min

/**
 * ValueAnimator扩展方法，模拟ponymusic项目的startOrResume方法
 */
private fun ValueAnimator.startOrResume() {
    if (isPaused) {
        resume()
    } else {
        start()
    }
}

/**
 * 黑胶唱片视图组件
 * 实现黑胶唱片旋转动画、唱臂动画和音波效果
 */
class VinylRecordView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0,
) : View(context, attrs, defStyleAttr) {

    companion object {
        private const val TAG = "VinylRecordView"
        private const val NEEDLE_PLAY_ANGLE = 0f
        private const val NEEDLE_PAUSE_ANGLE = -25f
        private const val ANIMATION_DURATION = 300L
    }

    // 绘制相关
    private val paint = Paint(Paint.ANTI_ALIAS_FLAG)
    private val matrix = Matrix()

    // 黑胶唱片背景
    private var vinylDiscDrawable: Drawable? = null

    // 封面边框 - 严格按照ponymusic项目标准
    private val coverBorder: Drawable by lazy {
        ContextCompat.getDrawable(context, R.drawable.bg_playing_cover_border)!!
    }

    // 唱臂 - 严格按照ponymusic项目标准
    private var needleDrawable: Drawable? = null
    private var needleAngle = NEEDLE_PAUSE_ANGLE

    // 分离的播放和暂停动画 - 严格参考ponymusic项目
    private val playAnimator by lazy {
        ValueAnimator.ofFloat(NEEDLE_PAUSE_ANGLE, NEEDLE_PLAY_ANGLE).apply {
            duration = ANIMATION_DURATION
            addUpdateListener(animationUpdateListener)
        }
    }
    private val pauseAnimator by lazy {
        ValueAnimator.ofFloat(NEEDLE_PLAY_ANGLE, NEEDLE_PAUSE_ANGLE).apply {
            duration = ANIMATION_DURATION
            addUpdateListener(animationUpdateListener)
        }
    }

    // 专辑封面
    private var albumCoverBitmap: Bitmap? = null
    private var albumCoverDrawable: Drawable? = null

    // 旋转动画 - 严格参考ponymusic项目
    private var rotationAnimator: ValueAnimator? = null
    private var discRotation = 0.0f

    // 音波动画
    private var soundWaveDrawable: AnimationDrawable? = null
    private var showSoundWave = false

    // 播放状态
    private var isPlaying = false

    // 尺寸相关
    private var centerX = 0f
    private var centerY = 0f
    private var vinylRadius = 0f
    private var coverRadius = 0f

    // 唱臂位置相关 - 参考ponymusic项目
    private var needleStartX = 0f
    private var needleStartY = 0f
    private var needleCenterX = 0f
    private var needleCenterY = 0f

    init {
        initDrawables()
        setupAnimations()
    }

    private fun initDrawables() {
        // 初始化黑胶唱片背景 - 使用指定资源文件
        vinylDiscDrawable = ContextCompat.getDrawable(context, R.drawable.bg_playing_disc)

        // 初始化唱臂 - 使用指定资源文件
        needleDrawable = ContextCompat.getDrawable(context, R.drawable.ic_playing_needle)

        // 初始化音波动画 - 使用指定资源文件
        soundWaveDrawable = ContextCompat.getDrawable(context, R.drawable.ic_sound_wave_animation) as? AnimationDrawable

        // 初始化默认专辑封面
        albumCoverDrawable = ContextCompat.getDrawable(context, R.drawable.ic_default_cover)
    }

    private fun setupAnimations() {
        // 设置旋转动画 - 严格参考ponymusic项目
        rotationAnimator = ValueAnimator.ofFloat(0f, 360f).apply {
            duration = 20000L // 20秒一圈
            repeatCount = ValueAnimator.INFINITE
            interpolator = LinearInterpolator()
            addUpdateListener(rotationUpdateListener)
        }
        // 注意：playAnimator和pauseAnimator已通过lazy初始化
    }

    override fun onSizeChanged(w: Int, h: Int, oldw: Int, oldh: Int) {
        super.onSizeChanged(w, h, oldw, oldh)

        centerX = w / 2f
        centerY = h / 2f

        // 基于ponymusic项目标准，增大黑胶唱片尺寸以获得更好的视觉效果
        val unit = min(w, h) / 8f
        vinylRadius = unit * 3.0f // 增大黑胶唱片半径，提升视觉效果
        coverRadius = vinylRadius * 0.65f // 参考ponymusic项目：封面为黑胶唱片的65%

        // 设置黑胶唱片背景尺寸
        vinylDiscDrawable?.setBounds(
            (centerX - vinylRadius).toInt(),
            (centerY - vinylRadius).toInt(),
            (centerX + vinylRadius).toInt(),
            (centerY + vinylRadius).toInt(),
        )

        // 设置唱臂尺寸和位置 - 严格按照ponymusic项目的精确计算
        needleDrawable?.let { needle ->
            val needleWidth = unit * 2f
            val needleHeight = unit * 3.33f

            // 基于ponymusic-master项目的精确计算
            // 参考：needleStartPoint.x = (width / 2 - needleBitmap.width / 5.5f).toInt()
            needleStartX = centerX - needleWidth / 5.5f

            // 唱臂位置上移：参考ponymusic项目中的discOffsetY计算
            // 参考：needleStartPoint.y = 0, discOffsetY = (needleBitmap.height / 1.5).toInt()
            val discOffsetY = (needleHeight / 1.5f).toInt()
            needleStartY = -2f // 向上移动2像素

            // 唱臂旋转中心 - 严格按照ponymusic项目
            // 参考：needleCenterPoint.x = width / 2, needleCenterPoint.y = (needleBitmap.width / 5.5f).toInt()
            needleCenterX = centerX
            needleCenterY = needleWidth / 5.5f

            // 调整黑胶唱片的垂直位置，与唱臂保持正确的相对位置
            // 参考ponymusic项目：discStartPoint.y = discOffsetY
            centerY = h / 2f + discOffsetY * 0.3f + 8f // 向下移动8像素

            // 设置唱臂的边界 - 使用正确的尺寸
            needle.setBounds(
                0,
                0,
                needleWidth.toInt(),
                needleHeight.toInt(),
            )
        }

        // 设置音波动画位置
        soundWaveDrawable?.let { soundWave ->
            val waveSize = vinylRadius * 0.2f
            soundWave.setBounds(
                (centerX - waveSize).toInt(),
                (centerY + vinylRadius * 0.7f - waveSize).toInt(),
                (centerX + waveSize).toInt(),
                (centerY + vinylRadius * 0.7f + waveSize).toInt(),
            )
        }
    }

    override fun onDraw(canvas: Canvas) {
        // 性能监控 - 严格按照ponymusic标准，确保>30fps
        val startTime = System.nanoTime()

        // 严格按照ponymusic项目标准的绘制顺序和方法
        // 参考：ponymusic-master/app/src/main/java/me/wcy/music/widget/AlbumCoverView.kt

        // 1.绘制封面 - 参考ponymusic项目
        drawAlbumCover(canvas)

        // 2.绘制黑胶唱片外侧半透明边框 - 参考ponymusic项目
        drawCoverBorder(canvas)

        // 3.绘制黑胶 - 参考ponymusic项目
        drawVinylDisc(canvas)

        // 4.绘制指针 - 参考ponymusic项目
        drawNeedle(canvas)

        // 5.绘制音波动画（额外功能）
        if (showSoundWave && isPlaying) {
            soundWaveDrawable?.draw(canvas)
        }

        // 性能监控 - 确保绘制时间<16.67ms (60fps)，符合Android Automotive标准
        val drawTime = (System.nanoTime() - startTime) / 1_000_000f
        if (drawTime > 16.67f) {
            Log.w("VinylRecordView", "绘制时间警告: ${drawTime}ms > 16.67ms (60fps)")
        }
    }

    private fun drawAlbumCover(canvas: Canvas) {
        // 严格参考ponymusic项目的封面绘制方法
        // 参考：AlbumCoverView.kt line 108-121
        val cover = albumCoverBitmap ?: return

        // 保存画布状态用于圆形裁剪
        val saveCount = canvas.save()

        // 创建圆形裁剪路径 - 严格按照ponymusic标准
        val clipPath = Path()
        clipPath.addCircle(centerX, centerY, coverRadius, Path.Direction.CW)
        canvas.clipPath(clipPath)

        // 设置旋转中心和旋转角度，setRotate和preTranslate顺序很重要
        matrix.reset()
        matrix.setRotate(
            discRotation,
            centerX,
            centerY,
        )
        matrix.preTranslate(
            centerX - coverRadius,
            centerY - coverRadius,
        )
        matrix.preScale(
            (coverRadius * 2f) / cover.width,
            (coverRadius * 2f) / cover.height,
        )
        canvas.drawBitmap(cover, matrix, null)

        // 恢复画布状态
        canvas.restoreToCount(saveCount)
    }

    private fun drawCoverBorder(canvas: Canvas) {
        // 严格参考ponymusic项目的边框绘制方法
        // 参考：AlbumCoverView.kt line 123-130
        val borderWidth = (6 * resources.displayMetrics.density).toInt() // 6dp转px

        // 绘制黑胶唱片外侧半透明边框 - 使用正确的coverBorder drawable
        coverBorder.setBounds(
            (centerX - vinylRadius - borderWidth).toInt(),
            (centerY - vinylRadius - borderWidth).toInt(),
            (centerX + vinylRadius + borderWidth).toInt(),
            (centerY + vinylRadius + borderWidth).toInt(),
        )
        coverBorder.draw(canvas)
    }

    private fun drawVinylDisc(canvas: Canvas) {
        // 严格参考ponymusic项目的黑胶绘制方法
        // 参考：AlbumCoverView.kt line 132-141
        val discBitmap = vinylDiscDrawable ?: return

        // 设置旋转中心和旋转角度，setRotate和preTranslate顺序很重要
        matrix.reset()
        matrix.setRotate(
            discRotation,
            centerX,
            centerY,
        )
        // 设置图片起始坐标
        matrix.preTranslate(
            centerX - vinylRadius,
            centerY - vinylRadius,
        )

        // 绘制黑胶唱片
        discBitmap.setBounds(
            0,
            0,
            (vinylRadius * 2).toInt(),
            (vinylRadius * 2).toInt(),
        )
        canvas.save()
        canvas.setMatrix(matrix)
        discBitmap.draw(canvas)
        canvas.restore()
    }

    private fun drawNeedle(canvas: Canvas) {
        // 严格参考ponymusic项目的指针绘制方法
        // 参考：AlbumCoverView.kt line 143-150
        needleDrawable?.let { needle ->
            // 设置旋转中心和旋转角度，setRotate和preTranslate顺序很重要
            // 参考ponymusic项目：needleMatrix.setRotate(needleRotation, needleCenterPoint.x.toFloat(), needleCenterPoint.y.toFloat())
            matrix.reset()
            matrix.setRotate(
                needleAngle,
                needleCenterX,
                needleCenterY,
            )
            // 参考ponymusic项目：needleMatrix.preTranslate(needleStartPoint.x.toFloat(), needleStartPoint.y.toFloat())
            matrix.preTranslate(needleStartX, needleStartY)

            canvas.save()
            canvas.setMatrix(matrix)
            needle.draw(canvas)
            canvas.restore()
        }
    }

    /**
     * 设置播放状态
     */
    fun setPlaying(playing: Boolean) {
        if (isPlaying == playing) return

        isPlaying = playing

        if (playing) {
            start()
        } else {
            pause()
        }
    }

    /**
     * 开始播放动画（兼容方法）
     */
    private fun startPlayingAnimation() {
        start()
    }

    /**
     * 停止播放动画（兼容方法）
     */
    private fun stopPlayingAnimation() {
        pause()
    }

    /**
     * 设置专辑封面
     */
    fun setAlbumCover(coverUrl: String?) {
        if (coverUrl.isNullOrBlank()) {
            albumCoverBitmap = null
            invalidate()
            return
        }

        Glide.with(context)
            .asBitmap()
            .load(coverUrl)
            .placeholder(R.drawable.ic_default_cover)
            .error(R.drawable.ic_default_cover)
            .into(object : CustomTarget<Bitmap>() {
                override fun onResourceReady(resource: Bitmap, transition: Transition<in Bitmap>?) {
                    albumCoverBitmap = resource
                    invalidate()
                }

                override fun onLoadCleared(placeholder: Drawable?) {
                    albumCoverBitmap = null
                    invalidate()
                }
            })
    }

    /**
     * 开始播放动画 - 严格参考ponymusic项目
     * 参考：AlbumCoverView.kt line 163-170
     */
    fun start() {
        if (isPlaying) {
            return
        }
        isPlaying = true
        rotationAnimator?.startOrResume() // 严格按照ponymusic项目标准

        // 使用专门的播放动画器
        playAnimator.start()

        // 启动音波动画
        showSoundWave = true
        soundWaveDrawable?.start()
    }

    /**
     * 暂停播放动画 - 严格参考ponymusic项目
     * 参考：AlbumCoverView.kt line 172-179
     */
    fun pause() {
        if (!isPlaying) {
            return
        }
        isPlaying = false
        rotationAnimator?.pause()

        // 使用专门的暂停动画器
        pauseAnimator.start()

        // 停止音波动画
        showSoundWave = false
        soundWaveDrawable?.stop()
    }

    /**
     * 重置动画 - 严格参考ponymusic项目
     * 参考：AlbumCoverView.kt line 181-186
     */
    fun reset() {
        isPlaying = false
        discRotation = 0.0f
        rotationAnimator?.cancel()
        invalidate()
    }

    /**
     * 严格参考ponymusic项目的动画监听器
     * 参考：AlbumCoverView.kt line 188-196
     */
    private val rotationUpdateListener = ValueAnimator.AnimatorUpdateListener { animation ->
        discRotation = animation.animatedValue as Float
        invalidate()
    }

    private val animationUpdateListener = ValueAnimator.AnimatorUpdateListener { animation ->
        needleAngle = animation.animatedValue as Float
        invalidate()
    }

    override fun onDetachedFromWindow() {
        super.onDetachedFromWindow()

        // 严格按照ponymusic项目标准清理资源
        rotationAnimator?.cancel()
        playAnimator.cancel()
        pauseAnimator.cancel()
        soundWaveDrawable?.stop()

        // 回收Bitmap资源
        albumCoverBitmap?.let { bitmap ->
            if (!bitmap.isRecycled) {
                bitmap.recycle()
            }
        }
        albumCoverBitmap = null
    }
}
