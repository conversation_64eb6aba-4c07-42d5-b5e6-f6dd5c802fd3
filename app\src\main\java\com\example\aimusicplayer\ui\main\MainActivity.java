package com.example.aimusicplayer.ui.main;

import android.Manifest;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.os.Build;
import android.os.Bundle;
import android.util.Log;
import android.view.View;
import android.view.WindowManager;
import android.widget.Toast;

import com.example.aimusicplayer.databinding.ActivityMainBinding;
import com.example.aimusicplayer.utils.ButtonAnimationUtils;

import androidx.activity.OnBackPressedCallback;
import androidx.annotation.NonNull;
import androidx.appcompat.app.AlertDialog;
import androidx.appcompat.app.AppCompatActivity;
import androidx.core.content.ContextCompat;
import androidx.lifecycle.ViewModelProvider;
import androidx.navigation.NavController;
import androidx.navigation.NavOptions;
import androidx.navigation.fragment.NavHostFragment;

import com.example.aimusicplayer.R;
import com.example.aimusicplayer.service.UnifiedPlaybackService;
import com.example.aimusicplayer.utils.NavigationUtils;
import com.example.aimusicplayer.utils.PerformanceUtils;
import com.example.aimusicplayer.utils.PermissionUtils;
import com.example.aimusicplayer.viewmodel.MainViewModel;

import dagger.hilt.android.AndroidEntryPoint;

/**
 * MainActivity作为应用的主界面容器，负责:
 * 1. 管理Navigation Component导航
 * 2. 处理导航交互
 * 3. 申请和管理应用所需权限
 * 4. 绑定后台播放服务
 * 使用MVVM架构和Hilt依赖注入
 */
@AndroidEntryPoint
public class MainActivity extends AppCompatActivity implements View.OnClickListener {

    private static final String TAG = "MainActivity";
    private static final int PERMISSIONS_REQUEST_CODE = 100;

    // ViewBinding
    private ActivityMainBinding binding;

    // ViewModel
    private MainViewModel viewModel;

    // Navigation Controller
    private NavController navController;

    // 导航栏按钮
    private View navPlayer;
    private View navLibrary;
    private View navDiscovery;
    private View navDriving;
    private View navProfile;
    private View navSettings;

    // 导航栏指示器
    private View navPlayerIndicator;
    private View navLibraryIndicator;
    private View navDiscoveryIndicator;
    private View navDrivingIndicator;
    private View navProfileIndicator;
    private View navSettingsIndicator;

    // 侧边栏状态
    private boolean isSidebarVisible = false;

    // 需要请求的核心权限
    private String[] requiredPermissions = {
        Manifest.permission.READ_EXTERNAL_STORAGE,
        Manifest.permission.RECORD_AUDIO,
        Manifest.permission.POST_NOTIFICATIONS
    };

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        try {
            Log.d(TAG, "MainActivity onCreate 开始");

            // 设置全屏模式 - 优先设置，确保正确的显示环境
            PerformanceUtils.setFullscreen(this, true);
            Log.d(TAG, "全屏模式设置完成");

            // 初始化ViewBinding - 核心UI组件
            binding = ActivityMainBinding.inflate(getLayoutInflater());
            setContentView(binding.getRoot());
            Log.d(TAG, "布局设置完成");

            // 初始化ViewModel - 确保在UI操作前完成
            try {
                viewModel = new ViewModelProvider(this).get(MainViewModel.class);
                Log.d(TAG, "ViewModel初始化成功");
            } catch (Exception e) {
                Log.e(TAG, "ViewModel初始化失败", e);
                // 如果ViewModel初始化失败，记录错误但不创建默认实例
                // 后续代码会检查viewModel是否为null
                Log.w(TAG, "ViewModel初始化失败，将在后续代码中处理");
            }

            // 初始化Navigation Controller - 添加重试机制
            boolean navControllerInitialized = false;
            for (int i = 0; i < 3; i++) {
                try {
                    NavHostFragment navHostFragment = (NavHostFragment) getSupportFragmentManager()
                            .findFragmentById(R.id.nav_host_fragment);
                    if (navHostFragment != null) {
                        navController = navHostFragment.getNavController();
                        navControllerInitialized = true;
                        Log.d(TAG, "Navigation Controller初始化成功");
                        break;
                    } else {
                        Log.w(TAG, "NavHostFragment未找到，重试 " + (i + 1) + "/3");
                        Thread.sleep(100); // 短暂等待
                    }
                } catch (Exception e) {
                    Log.w(TAG, "Navigation Controller初始化重试 " + (i + 1) + "/3", e);
                    try {
                        Thread.sleep(100);
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                    }
                }
            }

            if (!navControllerInitialized) {
                Log.e(TAG, "Navigation Controller初始化失败，使用降级模式");
                // 不抛出异常，继续初始化其他组件
            }

            // 初始化视图 - 核心UI组件
            try {
                initializeViews();
                Log.d(TAG, "视图初始化完成");
            } catch (Exception e) {
                Log.e(TAG, "视图初始化失败", e);
                Toast.makeText(this, "界面初始化失败，部分功能可能不可用", Toast.LENGTH_SHORT).show();
            }

            // 设置观察者 - 确保数据绑定正常
            try {
                setupObservers();
                Log.d(TAG, "观察者设置完成");
            } catch (Exception e) {
                Log.e(TAG, "观察者设置失败", e);
            }

            // 设置返回键处理
            try {
                setupBackPressedHandler();
                Log.d(TAG, "返回键处理设置完成");
            } catch (Exception e) {
                Log.e(TAG, "返回键处理设置失败", e);
            }

            // 延迟初始化非关键组件
            binding.getRoot().postDelayed(() -> {
                try {
                    // 优化渲染性能（非关键）
                    try {
                        com.example.aimusicplayer.utils.RenderingOptimizer.INSTANCE.optimizeActivityRendering(this);
                        Log.d(TAG, "渲染优化完成");
                    } catch (Exception e) {
                        Log.w(TAG, "渲染优化失败，继续运行", e);
                    }

                    // 添加屏幕触摸事件
                    try {
                        setupTouchListener();
                        Log.d(TAG, "触摸监听设置完成");
                    } catch (Exception e) {
                        Log.w(TAG, "触摸监听设置失败", e);
                    }

                    // 请求权限
                    try {
                        requestRequiredPermissions();
                        Log.d(TAG, "权限请求完成");
                    } catch (Exception e) {
                        Log.w(TAG, "权限请求失败", e);
                    }

                    // 启动播放服务
                    try {
                        startPlaybackService();
                        Log.d(TAG, "播放服务启动完成");
                    } catch (Exception e) {
                        Log.w(TAG, "播放服务启动失败", e);
                    }

                    // 检查登录状态
                    try {
                        checkLoginStatus();
                        Log.d(TAG, "登录状态检查完成");
                    } catch (Exception e) {
                        Log.w(TAG, "登录状态检查失败", e);
                    }

                } catch (Exception e) {
                    Log.e(TAG, "延迟初始化失败", e);
                }
            }, 200); // 增加延迟时间，确保主要组件初始化完成

            Log.d(TAG, "MainActivity onCreate 完成");
        } catch (Exception e) {
            Log.e(TAG, "MainActivity onCreate 发生严重错误", e);
            Toast.makeText(this, "应用初始化失败: " + e.getMessage(), Toast.LENGTH_LONG).show();

            // 尝试基本的恢复操作
            try {
                if (binding == null) {
                    binding = ActivityMainBinding.inflate(getLayoutInflater());
                    setContentView(binding.getRoot());
                }
                PerformanceUtils.setFullscreen(this, true);
            } catch (Exception recoveryException) {
                Log.e(TAG, "恢复操作也失败", recoveryException);
                // 最后的手段：显示错误并关闭应用
                finish();
            }
        }
    }

    /**
     * 设置现代化的返回键处理
     */
    private void setupBackPressedHandler() {
        OnBackPressedCallback callback = new OnBackPressedCallback(true) {
            @Override
            public void handleOnBackPressed() {
                // 处理返回键逻辑
                if (navController != null && navController.getCurrentDestination() != null) {
                    int currentDestinationId = navController.getCurrentDestination().getId();
                    if (currentDestinationId == R.id.playerFragment) {
                        // 如果当前是播放器Fragment，按返回键会弹出退出确认
                        new AlertDialog.Builder(MainActivity.this)
                            .setTitle("退出")
                            .setMessage("确定要退出轻聆音乐播放器吗？")
                            .setPositiveButton("确定", (dialog, which) -> finish())
                            .setNegativeButton("取消", null)
                            .show();
                    } else {
                        // 如果当前不是播放器Fragment，按返回键返回到播放器
                        if (viewModel != null) {
                            viewModel.setSelectedNavItem(0);
                        } else {
                            Log.e(TAG, "handleOnBackPressed: viewModel为null，无法返回到播放器");
                            // 如果ViewModel为null，直接调用finish()
                            finish();
                        }
                    }
                } else {
                    Log.e(TAG, "handleOnBackPressed: navController为null，无法处理返回事件");
                    // 使用默认行为
                    setEnabled(false);
                    getOnBackPressedDispatcher().onBackPressed();
                }
            }
        };
        getOnBackPressedDispatcher().addCallback(this, callback);
    }

    /**
     * 设置观察者
     */
    private void setupObservers() {
        // 检查ViewModel是否已正确初始化
        if (viewModel == null) {
            Log.e(TAG, "setupObservers: viewModel为null，无法设置观察者");
            Toast.makeText(this, "初始化失败，请重启应用", Toast.LENGTH_LONG).show();
            return;
        }

        try {
            // 观察侧边栏状态
            viewModel.getDrawerOpenState().observe(this, isOpen -> {
                isSidebarVisible = isOpen;
            });

            // 观察当前选中的导航项
            viewModel.getSelectedNavItemState().observe(this, position -> {
                if (navController == null) {
                    Log.e(TAG, "navController为null，无法导航");
                    return;
                }

                // 重置导航栏选中状态
                resetNavSelection();

                // 创建导航选项
                NavOptions navOptions = NavigationUtils.INSTANCE.standardNavOptions();

                switch (position) {
                    case 0:
                        // 播放器
                        navController.navigate(R.id.playerFragment, null, navOptions);
                        setNavItemSelected((android.widget.ImageView)navPlayer, navPlayerIndicator);
                        break;
                    case 1:
                        // 音乐库
                        navController.navigate(R.id.musicLibraryFragment, null, navOptions);
                        setNavItemSelected((android.widget.ImageView)navLibrary, navLibraryIndicator);
                        break;
                    case 2:
                        // 发现
                        navController.navigate(R.id.discoveryFragment, null, navOptions);
                        setNavItemSelected((android.widget.ImageView)navDiscovery, navDiscoveryIndicator);
                        break;
                    case 3:
                        // 驾驶模式
                        navController.navigate(R.id.drivingModeFragment, null, navOptions);
                        setNavItemSelected((android.widget.ImageView)navDriving, navDrivingIndicator);
                        break;
                    case 4:
                        // 用户资料
                        // 暂时导航到设置页面，因为用户资料页面尚未添加到导航图
                        navController.navigate(R.id.settingsFragment, null, navOptions);
                        setNavItemSelected((android.widget.ImageView)navProfile, navProfileIndicator);
                        break;
                    case 5:
                        // 设置
                        navController.navigate(R.id.settingsFragment, null, navOptions);
                        setNavItemSelected((android.widget.ImageView)navSettings, navSettingsIndicator);
                        break;
                }
            });

            Log.d(TAG, "观察者设置成功");
        } catch (Exception e) {
            Log.e(TAG, "设置观察者时发生错误", e);
            Toast.makeText(this, "初始化失败: " + e.getMessage(), Toast.LENGTH_LONG).show();
        }
    }

    /**
     * 设置菜单按钮和触摸事件
     */
    private void setupTouchListener() {
        // 设置侧边栏右侧菜单按钮点击事件
        android.widget.ImageView btnMenuRight = binding.btnMenuRight;
        if (btnMenuRight != null) {
            btnMenuRight.setVisibility(View.VISIBLE);

            // 使用更高效的点击监听器
            btnMenuRight.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    if (viewModel != null) {
                        viewModel.toggleDrawer();
                    }
                }
            });
        }
    }

    /**
     * 初始化UI控件
     */
    private void initializeViews() {
        // 使用ViewBinding获取UI组件
        View sidebarNav = binding.sidebarNav;
        this.navPlayer = binding.navPlayer;
        this.navLibrary = binding.navLibrary;
        this.navDiscovery = binding.navDiscovery;
        this.navDriving = binding.navDriving;
        this.navProfile = binding.navProfile;
        this.navSettings = binding.navSettings;

        // 初始化指示器
        navPlayerIndicator = binding.navPlayerIndicator;
        navLibraryIndicator = binding.navLibraryIndicator;
        navDiscoveryIndicator = binding.navDiscoveryIndicator;
        navDrivingIndicator = binding.navDrivingIndicator;
        navProfileIndicator = binding.navProfileIndicator;
        navSettingsIndicator = binding.navSettingsIndicator;

        // 设置点击监听器并添加触摸反馈
        View[] navButtons = {navPlayer, navLibrary, navDiscovery, navDriving, navProfile, navSettings};
        for (View button : navButtons) {
            button.setOnClickListener(this);
            ButtonAnimationUtils.INSTANCE.addScaleAnimation(button, 0.9f, 150, true, true, false);
        }

        // 实现渐进式导航策略
        initializeProgressiveNavigation();

        // 初始化侧边栏控制器
        android.widget.ImageView btnMenuRight = binding.btnMenuRight;
        android.widget.FrameLayout fragmentContainer = binding.fragmentContainer;

        // 使用ViewModel中的SidebarController初始化侧边栏
        viewModel.initializeSidebarController((android.widget.LinearLayout) sidebarNav, btnMenuRight, fragmentContainer, navButtons);
    }

    /**
     * 优化的播放页面导航初始化
     * 直接导航到播放页面，但延迟执行以确保Fragment完全初始化
     */
    private void initializeProgressiveNavigation() {
        try {
            if (navController == null) {
                Log.e(TAG, "navController为null，无法进行导航");
                return;
            }

            // 直接导航到播放页面，但稍作延迟确保MainActivity完全初始化
            Log.d(TAG, "准备导航到播放页面");

            // 先设置选中状态
            setNavItemSelected((android.widget.ImageView)navPlayer, navPlayerIndicator);

            // 使用View.post确保UI完全初始化后再导航，避免Handler嵌套
            binding.getRoot().post(() -> {
                try {
                    if (!isFinishing() && !isDestroyed() && navController != null) {
                        Log.d(TAG, "执行导航到播放页面");
                        navController.navigate(R.id.playerFragment);
                        Log.d(TAG, "导航到播放页面完成");
                    }
                } catch (Exception e) {
                    Log.e(TAG, "导航到播放页面失败", e);
                    // 降级处理：确保播放器按钮保持选中状态
                    setNavItemSelected((android.widget.ImageView)navPlayer, navPlayerIndicator);
                }
            });

        } catch (Exception e) {
            Log.e(TAG, "导航初始化失败", e);
            // 降级处理：立即导航到播放页面
            try {
                navController.navigate(R.id.playerFragment);
                setNavItemSelected((android.widget.ImageView)navPlayer, navPlayerIndicator);
            } catch (Exception ex) {
                Log.e(TAG, "降级导航也失败", ex);
            }
        }
    }

    /**
     * 检查登录状态
     */
    private void checkLoginStatus() {
        try {
            Log.d(TAG, "开始检查登录状态");

            // 显示加载动画
            com.example.aimusicplayer.ui.widget.LottieLoadingView loadingView = binding.loadingView;
            if (loadingView != null) {
                loadingView.setLoadingMessage("正在加载...");
                loadingView.setVisibility(View.VISIBLE);
            }

            // 检查登录状态
            // 注意：checkLoginStatus()方法可能返回void，需要修改为返回LiveData
            // 暂时注释掉，避免编译错误
            // viewModel.checkLoginStatus().observe(this, loginStatus -> {

            // 隐藏加载动画
            if (loadingView != null) {
                loadingView.setVisibility(View.GONE);
            }

            // 暂时跳过登录状态检查，直接显示游客状态
            updateUserInfoUI(null, null);

            // 移除自动播放初始歌曲功能
            Log.d(TAG, "跳过自动播放新歌速递，用户可手动搜索和播放音乐");
        } catch (Exception e) {
            Log.e(TAG, "检查登录状态失败", e);
            Toast.makeText(this, "登录状态检查失败", Toast.LENGTH_SHORT).show();
        }
    }

    /**
     * 获取用户详情
     * @param userId 用户ID
     */
    private void getUserDetail(String userId) {
        // 注意：getUserDetail()方法可能返回void，需要修改为返回LiveData
        // 暂时注释掉，避免编译错误
        /*
        viewModel.getUserDetail(userId).observe(this, userDetail -> {
            if (userDetail != null && userDetail.getProfile() != null) {
                // 更新UI显示用户信息
                String nickname = userDetail.getProfile().getNickname();
                String avatarUrl = userDetail.getProfile().getAvatarUrl();
                updateUserInfoUI(nickname, avatarUrl);
            } else {
                // 获取用户详情失败，显示默认信息
                updateUserInfoUI(null, null);
            }
        });
        */

        // 暂时显示默认信息
        updateUserInfoUI(null, null);
    }

    /**
     * 更新用户信息UI
     * @param nickname 用户昵称
     * @param avatarUrl 用户头像URL
     */
    private void updateUserInfoUI(String nickname, String avatarUrl) {
        // 侧边栏头部已移除，不需要更新UI
        // 保留此方法以避免修改其他调用此方法的代码
        Log.d(TAG, "用户信息更新: " + (nickname != null ? nickname : "游客模式"));
    }



    /**
     * 重置导航栏选中状态
     */
    private void resetNavSelection() {
        // 重置图标颜色 - 使用现代API
        int grayColor = ContextCompat.getColor(this, android.R.color.darker_gray);
        ((android.widget.ImageView)navPlayer).setColorFilter(grayColor);
        ((android.widget.ImageView)navLibrary).setColorFilter(grayColor);
        ((android.widget.ImageView)navDiscovery).setColorFilter(grayColor);
        ((android.widget.ImageView)navDriving).setColorFilter(grayColor);
        ((android.widget.ImageView)navProfile).setColorFilter(grayColor);
        ((android.widget.ImageView)navSettings).setColorFilter(grayColor);

        // 隐藏所有指示器
        navPlayerIndicator.setVisibility(View.GONE);
        navLibraryIndicator.setVisibility(View.GONE);
        navDiscoveryIndicator.setVisibility(View.GONE);
        navDrivingIndicator.setVisibility(View.GONE);
        navProfileIndicator.setVisibility(View.GONE);
        navSettingsIndicator.setVisibility(View.GONE);
    }

    /**
     * 设置导航项选中状态
     * @param navItem 导航项图标
     * @param indicator 导航项指示器
     */
    private void setNavItemSelected(android.widget.ImageView navItem, View indicator) {
        // 重置所有导航项
        resetNavSelection();

        // 设置选中状态 - 使用现代API
        navItem.setColorFilter(ContextCompat.getColor(this, R.color.colorPrimary));
        indicator.setVisibility(View.VISIBLE);
    }

    /**
     * 请求应用所需权限
     */
    private void requestRequiredPermissions() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            // 使用PermissionUtils获取音乐播放所需权限
            String[] musicPermissions = PermissionUtils.getMusicPlaybackPermissions();

            // 使用PermissionUtils检查权限并请求
            if (!PermissionUtils.hasPermissions(this, musicPermissions)) {
                PermissionUtils.requestPermissions(this, PERMISSIONS_REQUEST_CODE, musicPermissions);
            }

            // 语音相关权限（可选，用于特定功能）
            String[] voicePermissions = PermissionUtils.getVoicePermissions();
            if (!PermissionUtils.hasPermissions(this, voicePermissions)) {
                // 根据需要可以分开请求，或者与音乐播放权限一起请求
                // 这里暂时不请求，当用户使用语音功能时再请求
            }
        }
    }

    /**
     * 启动统一播放服务（简化版本）
     */
    private void startPlaybackService() {
        try {
            Log.d(TAG, "开始启动播放服务");
            Intent serviceIntent = new Intent(this, UnifiedPlaybackService.class);

            // 添加标志，确保服务能够正确启动
            serviceIntent.setAction("START_PLAYBACK_SERVICE");

            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                startForegroundService(serviceIntent);
                Log.d(TAG, "使用startForegroundService启动服务");
            } else {
                startService(serviceIntent);
                Log.d(TAG, "使用startService启动服务");
            }
            Log.d(TAG, "播放服务启动请求已发送");
        } catch (Exception e) {
            Log.e(TAG, "启动播放服务失败", e);
            Toast.makeText(this, "启动播放服务失败，部分功能可能不可用", Toast.LENGTH_SHORT).show();
        }
    }

    /**
     * 导航按钮点击事件处理
     */
    @Override
    public void onClick(View v) {
        // 先重置自动隐藏计时器
        scheduleNavbarHide();

        // 检查ViewModel是否已正确初始化
        if (viewModel == null) {
            Log.e(TAG, "onClick: viewModel为null，无法处理导航点击事件");
            Toast.makeText(this, "应用未正确初始化，请重启应用", Toast.LENGTH_SHORT).show();
            return;
        }

        int id = v.getId();

        try {
            if (id == R.id.nav_player) {
                // 切换到播放器界面
                viewModel.setSelectedNavItem(0);
            } else if (id == R.id.nav_library) {
                // 切换到音乐库界面
                viewModel.setSelectedNavItem(1);
            } else if (id == R.id.nav_discovery) {
                // 切换到发现界面
                viewModel.setSelectedNavItem(2);
            } else if (id == R.id.nav_driving) {
                // 切换到驾驶模式界面
                viewModel.setSelectedNavItem(3);
            } else if (id == R.id.nav_profile) {
                // 切换到用户界面
                viewModel.setSelectedNavItem(4);
            } else if (id == R.id.nav_settings) {
                // 切换到设置界面
                viewModel.setSelectedNavItem(5);
            }
        } catch (Exception e) {
            Log.e(TAG, "处理导航点击事件时发生错误", e);
            Toast.makeText(this, "导航失败: " + e.getMessage(), Toast.LENGTH_SHORT).show();
        }
    }

    /**
     * 安排侧边栏自动隐藏
     */
    private void scheduleNavbarHide() {
        // 使用ViewModel中的方法安排自动隐藏
        if (viewModel != null) {
            viewModel.scheduleSidebarAutoHide();
        }
    }



    @Override
    public void onWindowFocusChanged(boolean hasFocus) {
        super.onWindowFocusChanged(hasFocus);
        if (hasFocus) {
            // 当窗口获得焦点时，再次设置全屏模式
            PerformanceUtils.setFullscreen(this, true);

            // 当重新获得焦点时，重设自动隐藏计时器
            scheduleNavbarHide();
        }
    }

    @Override
    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions, @NonNull int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);

        if (requestCode == PERMISSIONS_REQUEST_CODE) {
            boolean allGranted = true;

            // 检查是否所有权限都被授予
            for (int result : grantResults) {
                if (result != PackageManager.PERMISSION_GRANTED) {
                    allGranted = false;
                    break;
                }
            }

            if (allGranted) {
                Toast.makeText(this, "所有权限已授予", Toast.LENGTH_SHORT).show();
                // 权限授予后，刷新界面
                recreate();
            } else {
                // 一些关键权限被拒绝，显示提示
                Toast.makeText(this, "一些功能可能受限，请授予应用必要的权限", Toast.LENGTH_LONG).show();
            }
        }
    }

    @Override
    protected void onDestroy() {
        // 记录Activity销毁
        Log.d(TAG, "MainActivity onDestroy");

        // 停止GPU性能监控
        com.example.aimusicplayer.utils.GPUPerformanceMonitor.INSTANCE.stopMonitoring();
        Log.d(TAG, "GPU性能监控已停止");

        super.onDestroy();
    }


}