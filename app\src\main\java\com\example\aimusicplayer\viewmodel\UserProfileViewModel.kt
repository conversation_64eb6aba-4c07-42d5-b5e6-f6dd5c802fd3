package com.example.aimusicplayer.viewmodel

import android.util.Log
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.example.aimusicplayer.data.model.PlayList
import com.example.aimusicplayer.data.model.Song
import com.example.aimusicplayer.data.model.UserDetailResponse.UserProfile
import com.example.aimusicplayer.data.repository.UserRepository
import com.example.aimusicplayer.utils.toUnMutable
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * 用户资料ViewModel
 * 严格按照ponymusic项目标准重构
 */
@HiltViewModel
class UserProfileViewModel @Inject constructor(
    private val userRepository: UserRepository,
) : ViewModel() {

    companion object {
        private const val TAG = "UserProfileViewModel"
    }

    // 严格按照ponymusic项目标准的StateFlow使用
    private val _userProfileFlow = MutableStateFlow<UserProfile?>(null)
    val userProfileFlow = _userProfileFlow.toUnMutable()

    private val _userPlaylistsFlow = MutableStateFlow<List<PlayList>>(emptyList())
    val userPlaylistsFlow = _userPlaylistsFlow.toUnMutable()

    private val _likedSongsFlow = MutableStateFlow<List<Song>>(emptyList())
    val likedSongsFlow = _likedSongsFlow.toUnMutable()

    private val _recentPlayedFlow = MutableStateFlow<List<Song>>(emptyList())
    val recentPlayedFlow = _recentPlayedFlow.toUnMutable()

    private val _isLoggedInFlow = MutableStateFlow(false)
    val isLoggedInFlow = _isLoggedInFlow.toUnMutable()

    // 加载状态
    private val _isLoading = MutableStateFlow(false)
    val isLoading = _isLoading.toUnMutable()

    // 错误信息
    private val _errorMessage = MutableStateFlow<String?>(null)
    val errorMessage = _errorMessage.toUnMutable()

    // UI兼容属性
    val userData = userProfileFlow

    init {
        // 检查登录状态
        checkLoginStatus()
    }

    /**
     * 检查登录状态
     */
    private fun checkLoginStatus() {
        viewModelScope.launch {
            try {
                val isLoggedIn = userRepository.isLoggedIn()
                _isLoggedInFlow.value = isLoggedIn

                if (isLoggedIn) {
                    loadUserProfile()
                }
            } catch (e: Exception) {
                Log.e(TAG, "检查登录状态失败", e)
                _isLoggedInFlow.value = false
            }
        }
    }

    /**
     * 加载用户资料
     */
    fun loadUserProfile() {
        viewModelScope.launch {
            try {
                val profile = userRepository.getUserProfile()
                _userProfileFlow.value = profile
                Log.d(TAG, "用户资料加载成功")
            } catch (e: Exception) {
                Log.e(TAG, "加载用户资料失败", e)
            }
        }
    }

    /**
     * 加载用户歌单
     */
    fun loadUserPlaylists(userId: String) {
        viewModelScope.launch {
            try {
                val playlists = userRepository.getUserPlaylists(userId)
                _userPlaylistsFlow.value = playlists
                Log.d(TAG, "用户歌单加载成功: ${playlists.size}个歌单")
            } catch (e: Exception) {
                Log.e(TAG, "加载用户歌单失败", e)
            }
        }
    }

    /**
     * 退出登录
     */
    fun logout() {
        viewModelScope.launch {
            try {
                userRepository.logout()
                _isLoggedInFlow.value = false
                _userProfileFlow.value = null
                _userPlaylistsFlow.value = emptyList()
                _likedSongsFlow.value = emptyList()
                _recentPlayedFlow.value = emptyList()
                Log.d(TAG, "退出登录成功")
            } catch (e: Exception) {
                Log.e(TAG, "退出登录失败", e)
            }
        }
    }

    /**
     * 加载用户数据
     */
    fun loadUserData() {
        _isLoading.value = true
        _errorMessage.value = null

        viewModelScope.launch {
            try {
                loadUserProfile()
            } catch (e: Exception) {
                Log.e(TAG, "加载用户数据失败", e)
                _errorMessage.value = e.message ?: "加载用户数据失败"
            } finally {
                _isLoading.value = false
            }
        }
    }

    /**
     * 重试加载用户数据
     */
    fun retryLoadUserData() {
        loadUserData()
    }

    /**
     * 同步退出登录
     */
    fun logoutSync() {
        logout()
    }
}
