package com.example.aimusicplayer.data.model

import com.google.gson.annotations.SerializedName

/**
 * 榜单API响应模型
 * 严格对应网易云音乐API /toplist 接口
 * 参考：NeteaseCloudMusicApiBackup-main/module/toplist.js
 */
data class ToplistResponse(
    val list: List<ToplistData>? = null,
) : BaseResponse()

/**
 * 榜单数据项
 * 对应API返回的list数组中的每个榜单对象
 */
data class ToplistData(
    val id: Long = 0,
    val name: String = "",

    @SerializedName("coverImgUrl")
    val coverImgUrl: String = "",

    @SerializedName("description")
    val description: String = "",

    @SerializedName("playCount")
    val playCount: Long = 0,

    @SerializedName("trackCount")
    val trackCount: Int = 0,

    @SerializedName("updateFrequency")
    val updateFrequency: String = "",

    @SerializedName("subscribedCount")
    val subscribedCount: Long = 0,

    @SerializedName("cloudTrackCount")
    val cloudTrackCount: Int = 0,

    @SerializedName("userId")
    val userId: Long = 0,

    @SerializedName("createTime")
    val createTime: Long = 0,

    @SerializedName("updateTime")
    val updateTime: Long = 0,

    @SerializedName("coverImgId")
    val coverImgId: Long = 0,

    @SerializedName("newImported")
    val newImported: Boolean = false,

    @SerializedName("anonimous")
    val anonimous: Boolean = false,

    @SerializedName("totalDuration")
    val totalDuration: Long = 0,

    @SerializedName("creator")
    val creator: ToplistCreator? = null,

    @SerializedName("tracks")
    val tracks: List<ToplistTrack>? = null,

    @SerializedName("tags")
    val tags: List<String>? = null,

    @SerializedName("ToplistType")
    val toplistType: String? = null,
)

/**
 * 榜单创建者信息
 */
data class ToplistCreator(
    @SerializedName("userId")
    val userId: Long = 0,

    @SerializedName("nickname")
    val nickname: String = "",

    @SerializedName("avatarUrl")
    val avatarUrl: String = "",
)

/**
 * 榜单歌曲信息（简要）
 */
data class ToplistTrack(
    val id: Long = 0,
    val name: String = "",

    @SerializedName("ar")
    val artists: List<ToplistArtist>? = null,

    @SerializedName("al")
    val album: ToplistAlbum? = null,
)

/**
 * 榜单歌手信息（简要）
 */
data class ToplistArtist(
    val id: Long = 0,
    val name: String = "",
)

/**
 * 榜单专辑信息（简要）
 */
data class ToplistAlbum(
    val id: Long = 0,
    val name: String = "",

    @SerializedName("picUrl")
    val picUrl: String = "",
)

/**
 * 数据转换扩展方法
 * 将API响应数据转换为应用内部的PlayList模型
 */
fun ToplistData.toPlayList(): PlayList {
    return PlayList(
        id = this.id.toString(),
        name = this.name,
        coverImgUrl = this.coverImgUrl,
        description = this.description,
        creatorId = this.creator?.userId?.toString() ?: "",
        creatorName = this.creator?.nickname ?: "",
        songCount = this.trackCount,
        playCount = this.playCount.toInt(),
        subscribed = false,
        songs = mutableListOf(),
    )
}

/**
 * 批量转换方法
 */
fun List<ToplistData>.toPlayLists(): List<PlayList> {
    return this.map { it.toPlayList() }
}
