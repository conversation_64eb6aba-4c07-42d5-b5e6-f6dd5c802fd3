package com.example.aimusicplayer.network

import android.content.Context
import android.net.ConnectivityManager
import android.net.Network
import android.net.NetworkCapabilities
import android.net.NetworkRequest
import android.util.Log
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 网络状态管理器
 * 监听网络状态变化并智能调整请求策略
 */
@Singleton
class NetworkStateManager @Inject constructor(
    @ApplicationContext private val context: Context,
) {

    companion object {
        private const val TAG = "NetworkStateManager"
    }

    // 网络状态
    data class NetworkState(
        val isConnected: Boolean = false,
        val isWifi: Boolean = false,
        val isCellular: Boolean = false,
        val isMetered: Boolean = false,
        val connectionQuality: ConnectionQuality = ConnectionQuality.UNKNOWN,
    )

    // 连接质量枚举
    enum class ConnectionQuality {
        EXCELLENT, // 优秀 - WiFi或高速移动网络
        GOOD, // 良好 - 4G网络
        FAIR, // 一般 - 3G网络
        POOR, // 较差 - 2G网络
        UNKNOWN, // 未知
    }

    // 请求策略
    data class RequestStrategy(
        val enableHighQualityImages: Boolean = true,
        val enableImageCache: Boolean = true,
        val maxConcurrentRequests: Int = 5,
        val requestTimeout: Long = 30000L,
        val retryCount: Int = 3,
        val enablePreloading: Boolean = true,
    )

    private val connectivityManager = context.getSystemService(Context.CONNECTIVITY_SERVICE) as ConnectivityManager

    // 网络状态流
    private val _networkState = MutableStateFlow(getCurrentNetworkState())
    val networkState: StateFlow<NetworkState> = _networkState.asStateFlow()

    // 请求策略流
    private val _requestStrategy = MutableStateFlow(getOptimalStrategy(_networkState.value))
    val requestStrategy: StateFlow<RequestStrategy> = _requestStrategy.asStateFlow()

    // 网络回调
    private val networkCallback = object : ConnectivityManager.NetworkCallback() {
        override fun onAvailable(network: Network) {
            super.onAvailable(network)
            Log.d(TAG, "网络连接可用: $network")
            updateNetworkState()
        }

        override fun onLost(network: Network) {
            super.onLost(network)
            Log.d(TAG, "网络连接丢失: $network")
            updateNetworkState()
        }

        override fun onCapabilitiesChanged(network: Network, networkCapabilities: NetworkCapabilities) {
            super.onCapabilitiesChanged(network, networkCapabilities)
            Log.d(TAG, "网络能力变化: $networkCapabilities")
            updateNetworkState()
        }
    }

    init {
        registerNetworkCallback()
    }

    /**
     * 注册网络状态监听
     */
    private fun registerNetworkCallback() {
        try {
            val networkRequest = NetworkRequest.Builder()
                .addCapability(NetworkCapabilities.NET_CAPABILITY_INTERNET)
                .addCapability(NetworkCapabilities.NET_CAPABILITY_VALIDATED)
                .build()

            connectivityManager.registerNetworkCallback(networkRequest, networkCallback)
            Log.d(TAG, "网络状态监听器注册成功")
        } catch (e: Exception) {
            Log.e(TAG, "注册网络状态监听器失败", e)
        }
    }

    /**
     * 取消注册网络状态监听
     */
    fun unregisterNetworkCallback() {
        try {
            connectivityManager.unregisterNetworkCallback(networkCallback)
            Log.d(TAG, "网络状态监听器取消注册成功")
        } catch (e: Exception) {
            Log.e(TAG, "取消注册网络状态监听器失败", e)
        }
    }

    /**
     * 更新网络状态
     */
    private fun updateNetworkState() {
        val newState = getCurrentNetworkState()
        _networkState.value = newState
        _requestStrategy.value = getOptimalStrategy(newState)

        Log.d(TAG, "网络状态更新: $newState")
        Log.d(TAG, "请求策略更新: ${_requestStrategy.value}")
    }

    /**
     * 获取当前网络状态
     */
    private fun getCurrentNetworkState(): NetworkState {
        return try {
            val network = connectivityManager.activeNetwork
            val capabilities = connectivityManager.getNetworkCapabilities(network)

            if (network == null || capabilities == null) {
                NetworkState()
            } else {
                val isConnected = capabilities.hasCapability(NetworkCapabilities.NET_CAPABILITY_INTERNET) &&
                    capabilities.hasCapability(NetworkCapabilities.NET_CAPABILITY_VALIDATED)

                val isWifi = capabilities.hasTransport(NetworkCapabilities.TRANSPORT_WIFI)
                val isCellular = capabilities.hasTransport(NetworkCapabilities.TRANSPORT_CELLULAR)
                val isMetered = !capabilities.hasCapability(NetworkCapabilities.NET_CAPABILITY_NOT_METERED)

                val quality = determineConnectionQuality(capabilities)

                NetworkState(
                    isConnected = isConnected,
                    isWifi = isWifi,
                    isCellular = isCellular,
                    isMetered = isMetered,
                    connectionQuality = quality,
                )
            }
        } catch (e: Exception) {
            Log.e(TAG, "获取网络状态失败", e)
            NetworkState()
        }
    }

    /**
     * 确定连接质量
     */
    private fun determineConnectionQuality(capabilities: NetworkCapabilities): ConnectionQuality {
        return when {
            capabilities.hasTransport(NetworkCapabilities.TRANSPORT_WIFI) -> {
                // WiFi连接通常质量较好
                if (capabilities.hasCapability(NetworkCapabilities.NET_CAPABILITY_NOT_METERED)) {
                    ConnectionQuality.EXCELLENT
                } else {
                    ConnectionQuality.GOOD
                }
            }
            capabilities.hasTransport(NetworkCapabilities.TRANSPORT_CELLULAR) -> {
                // 根据移动网络类型判断质量
                when {
                    capabilities.linkDownstreamBandwidthKbps > 10000 -> ConnectionQuality.EXCELLENT // >10Mbps
                    capabilities.linkDownstreamBandwidthKbps > 5000 -> ConnectionQuality.GOOD // >5Mbps
                    capabilities.linkDownstreamBandwidthKbps > 1000 -> ConnectionQuality.FAIR // >1Mbps
                    capabilities.linkDownstreamBandwidthKbps > 0 -> ConnectionQuality.POOR // >0Mbps
                    else -> ConnectionQuality.UNKNOWN
                }
            }
            capabilities.hasTransport(NetworkCapabilities.TRANSPORT_ETHERNET) -> {
                ConnectionQuality.EXCELLENT
            }
            else -> ConnectionQuality.UNKNOWN
        }
    }

    /**
     * 根据网络状态获取最优请求策略
     */
    private fun getOptimalStrategy(networkState: NetworkState): RequestStrategy {
        return when {
            !networkState.isConnected -> {
                // 无网络连接
                RequestStrategy(
                    enableHighQualityImages = false,
                    enableImageCache = true,
                    maxConcurrentRequests = 0,
                    requestTimeout = 5000L,
                    retryCount = 0,
                    enablePreloading = false,
                )
            }
            networkState.isWifi && networkState.connectionQuality == ConnectionQuality.EXCELLENT -> {
                // WiFi优秀连接
                RequestStrategy(
                    enableHighQualityImages = true,
                    enableImageCache = true,
                    maxConcurrentRequests = 8,
                    requestTimeout = 30000L,
                    retryCount = 3,
                    enablePreloading = true,
                )
            }
            networkState.isWifi -> {
                // WiFi一般连接
                RequestStrategy(
                    enableHighQualityImages = true,
                    enableImageCache = true,
                    maxConcurrentRequests = 5,
                    requestTimeout = 25000L,
                    retryCount = 3,
                    enablePreloading = true,
                )
            }
            networkState.isCellular && !networkState.isMetered &&
                networkState.connectionQuality == ConnectionQuality.EXCELLENT -> {
                // 无限流量高速移动网络
                RequestStrategy(
                    enableHighQualityImages = true,
                    enableImageCache = true,
                    maxConcurrentRequests = 5,
                    requestTimeout = 25000L,
                    retryCount = 3,
                    enablePreloading = true,
                )
            }
            networkState.isCellular && networkState.connectionQuality == ConnectionQuality.GOOD -> {
                // 4G网络
                RequestStrategy(
                    enableHighQualityImages = false,
                    enableImageCache = true,
                    maxConcurrentRequests = 3,
                    requestTimeout = 20000L,
                    retryCount = 2,
                    enablePreloading = false,
                )
            }
            networkState.isCellular -> {
                // 较慢的移动网络
                RequestStrategy(
                    enableHighQualityImages = false,
                    enableImageCache = true,
                    maxConcurrentRequests = 2,
                    requestTimeout = 15000L,
                    retryCount = 1,
                    enablePreloading = false,
                )
            }
            else -> {
                // 默认策略
                RequestStrategy()
            }
        }
    }

    /**
     * 检查是否应该使用高质量图片
     */
    fun shouldUseHighQualityImages(): Boolean {
        return _requestStrategy.value.enableHighQualityImages
    }

    /**
     * 检查是否应该启用预加载
     */
    fun shouldEnablePreloading(): Boolean {
        return _requestStrategy.value.enablePreloading
    }

    /**
     * 获取最大并发请求数
     */
    fun getMaxConcurrentRequests(): Int {
        return _requestStrategy.value.maxConcurrentRequests
    }

    /**
     * 获取请求超时时间
     */
    fun getRequestTimeout(): Long {
        return _requestStrategy.value.requestTimeout
    }

    /**
     * 获取重试次数
     */
    fun getRetryCount(): Int {
        return _requestStrategy.value.retryCount
    }
}
