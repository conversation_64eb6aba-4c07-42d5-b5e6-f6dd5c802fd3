package com.example.aimusicplayer.utils

import android.util.Log
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.async
import kotlinx.coroutines.awaitAll
import kotlinx.coroutines.withContext

/**
 * 高性能数据转换工具类
 * 实现并发数据转换，优化大数据量处理性能
 * 严格按照ponymusic-master项目性能标准
 */
object DataTransformUtils {

    private const val TAG = "DataTransformUtils"

    // 性能标准常量
    private const val MAX_TRANSFORM_TIME_MS = 100L // 数据转换操作<100ms
    private const val MAX_PARSE_TIME_MS = 200L // API响应解析<200ms
    private const val BATCH_SIZE = 50 // 批量处理大小

    // 性能监控
    private var totalTransforms = 0
    private var totalTransformTime = 0L
    private var slowTransforms = 0

    /**
     * 并发批量数据转换
     * @param data 原始数据列表
     * @param transform 转换函数
     * @param batchSize 批量大小
     * @return 转换后的数据列表
     */
    suspend fun <T, R> concurrentTransform(
        data: List<T>,
        transform: suspend (T) -> R,
        batchSize: Int = BATCH_SIZE,
    ): List<R> = withContext(Dispatchers.Default) {
        val startTime = System.currentTimeMillis()

        try {
            if (data.isEmpty()) {
                return@withContext emptyList()
            }

            // 小数据量直接处理
            if (data.size <= batchSize) {
                val result = data.map { transform(it) }
                recordTransformTime(System.currentTimeMillis() - startTime, data.size)
                return@withContext result
            }

            // 大数据量分批并发处理
            val result = data.chunked(batchSize).map { batch ->
                async {
                    batch.map { transform(it) }
                }
            }.awaitAll().flatten()

            recordTransformTime(System.currentTimeMillis() - startTime, data.size)
            result
        } catch (e: Exception) {
            Log.e(TAG, "并发数据转换失败", e)
            // 降级到串行处理
            val result = data.map { transform(it) }
            recordTransformTime(System.currentTimeMillis() - startTime, data.size)
            result
        }
    }

    /**
     * 高性能批量数据转换（针对简单转换）
     * @param data 原始数据列表
     * @param transform 转换函数（非挂起）
     * @return 转换后的数据列表
     */
    suspend fun <T, R> fastTransform(
        data: List<T>,
        transform: (T) -> R,
    ): List<R> = withContext(Dispatchers.Default) {
        val startTime = System.currentTimeMillis()

        try {
            val result = if (data.size > BATCH_SIZE) {
                // 大数据量并发处理
                data.chunked(BATCH_SIZE).map { batch ->
                    async {
                        batch.map(transform)
                    }
                }.awaitAll().flatten()
            } else {
                // 小数据量直接处理
                data.map(transform)
            }

            recordTransformTime(System.currentTimeMillis() - startTime, data.size)
            result
        } catch (e: Exception) {
            Log.e(TAG, "快速数据转换失败", e)
            val result = data.map(transform)
            recordTransformTime(System.currentTimeMillis() - startTime, data.size)
            result
        }
    }

    /**
     * 智能数据预加载
     * 基于用户行为预测，智能预加载相关数据
     * @param primaryData 主要数据
     * @param relatedDataLoader 相关数据加载器
     * @return 预加载的相关数据
     */
    suspend fun <T, R> smartPreload(
        primaryData: T,
        relatedDataLoader: suspend (T) -> List<R>,
    ): List<R> = withContext(Dispatchers.IO) {
        try {
            val startTime = System.currentTimeMillis()
            val result = relatedDataLoader(primaryData)
            val endTime = System.currentTimeMillis()

            Log.d(TAG, "智能预加载完成: ${result.size}项数据, 耗时${endTime - startTime}ms")
            result
        } catch (e: Exception) {
            Log.w(TAG, "智能预加载失败", e)
            emptyList()
        }
    }

    /**
     * 分页数据加载优化
     * @param pageSize 页面大小
     * @param pageLoader 页面加载器
     * @param maxPages 最大页数
     * @return 分页数据
     */
    suspend fun <T> optimizedPagingLoad(
        pageSize: Int = 20,
        maxPages: Int = 10,
        pageLoader: suspend (page: Int, size: Int) -> List<T>,
    ): List<T> = withContext(Dispatchers.IO) {
        val allData = mutableListOf<T>()
        var currentPage = 0

        try {
            while (currentPage < maxPages) {
                val pageData = pageLoader(currentPage, pageSize)

                if (pageData.isEmpty()) {
                    break // 没有更多数据
                }

                allData.addAll(pageData)
                currentPage++

                // 如果返回的数据少于页面大小，说明是最后一页
                if (pageData.size < pageSize) {
                    break
                }
            }

            Log.d(TAG, "分页加载完成: ${allData.size}项数据, 共${currentPage}页")
            allData.toList()
        } catch (e: Exception) {
            Log.e(TAG, "分页加载失败", e)
            allData.toList()
        }
    }

    /**
     * 内存优化的大数据处理
     * @param data 大数据列表
     * @param processor 数据处理器
     * @param chunkSize 块大小
     * @return 处理结果
     */
    suspend fun <T, R> memoryOptimizedProcess(
        data: List<T>,
        processor: suspend (List<T>) -> List<R>,
        chunkSize: Int = 100,
    ): List<R> = withContext(Dispatchers.Default) {
        val results = mutableListOf<R>()

        try {
            data.chunked(chunkSize).forEach { chunk ->
                val chunkResult = processor(chunk)
                results.addAll(chunkResult)

                // 强制垃圾回收，释放内存
                if (results.size % (chunkSize * 5) == 0) {
                    System.gc()
                }
            }

            Log.d(TAG, "内存优化处理完成: ${results.size}项数据")
            results.toList()
        } catch (e: Exception) {
            Log.e(TAG, "内存优化处理失败", e)
            results.toList()
        }
    }

    /**
     * 记录转换性能
     */
    private fun recordTransformTime(timeMs: Long, dataSize: Int) {
        totalTransforms++
        totalTransformTime += timeMs

        if (timeMs > MAX_TRANSFORM_TIME_MS) {
            slowTransforms++
            Log.w(TAG, "数据转换较慢: ${timeMs}ms for $dataSize items")
        }

        Log.d(TAG, "数据转换完成: ${dataSize}项数据, 耗时${timeMs}ms")
    }

    /**
     * 获取性能统计
     */
    fun getPerformanceStats(): PerformanceStats {
        val avgTime = if (totalTransforms > 0) totalTransformTime / totalTransforms else 0L
        val slowRate = if (totalTransforms > 0) slowTransforms.toDouble() / totalTransforms else 0.0

        return PerformanceStats(
            totalTransforms = totalTransforms,
            averageTimeMs = avgTime,
            slowTransformRate = slowRate,
            totalTimeMs = totalTransformTime,
        )
    }

    /**
     * 重置性能统计
     */
    fun resetPerformanceStats() {
        totalTransforms = 0
        totalTransformTime = 0L
        slowTransforms = 0
    }

    /**
     * 性能统计数据类
     */
    data class PerformanceStats(
        val totalTransforms: Int,
        val averageTimeMs: Long,
        val slowTransformRate: Double,
        val totalTimeMs: Long,
    ) {
        fun getFormattedStats(): String {
            return """
                数据转换性能统计:
                - 总转换次数: $totalTransforms
                - 平均耗时: ${averageTimeMs}ms
                - 慢转换比例: ${String.format("%.2f", slowTransformRate * 100)}%
                - 总耗时: ${totalTimeMs}ms
            """.trimIndent()
        }
    }
}

/**
 * 扩展函数：为List提供高性能并发转换
 */
suspend fun <T, R> List<T>.concurrentMap(transform: suspend (T) -> R): List<R> {
    return DataTransformUtils.concurrentTransform(this, transform)
}

/**
 * 扩展函数：为List提供快速转换
 */
suspend fun <T, R> List<T>.fastMap(transform: (T) -> R): List<R> {
    return DataTransformUtils.fastTransform(this, transform)
}
