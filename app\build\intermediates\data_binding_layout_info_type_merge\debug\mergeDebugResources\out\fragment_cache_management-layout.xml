<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="fragment_cache_management" modulePackage="com.example.aimusicplayer" filePath="app\src\main\res\layout\fragment_cache_management.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.coordinatorlayout.widget.CoordinatorLayout"><Targets><Target tag="layout/fragment_cache_management_0" view="androidx.coordinatorlayout.widget.CoordinatorLayout"><Expressions/><location startLine="1" startOffset="0" endLine="305" endOffset="53"/></Target><Target id="@+id/toolbar" view="com.google.android.material.appbar.MaterialToolbar"><Expressions/><location startLine="14" startOffset="8" endLine="20" endOffset="55"/></Target><Target id="@+id/swipeRefreshLayout" view="androidx.swiperefreshlayout.widget.SwipeRefreshLayout"><Expressions/><location startLine="25" startOffset="4" endLine="295" endOffset="59"/></Target><Target id="@+id/textCacheSize" view="TextView"><Expressions/><location startLine="76" startOffset="32" endLine="83" endOffset="64"/></Target><Target id="@+id/textCacheCount" view="TextView"><Expressions/><location startLine="85" startOffset="32" endLine="92" endOffset="57"/></Target><Target id="@+id/textCacheUsage" view="TextView"><Expressions/><location startLine="102" startOffset="32" endLine="109" endOffset="54"/></Target><Target id="@+id/progressBarCacheUsage" view="ProgressBar"><Expressions/><location startLine="123" startOffset="24" endLine="131" endOffset="49"/></Target><Target id="@+id/textMaxCacheSize" view="TextView"><Expressions/><location startLine="174" startOffset="28" endLine="180" endOffset="52"/></Target><Target id="@+id/switchWifiOnly" view="com.google.android.material.switchmaterial.SwitchMaterial"><Expressions/><location startLine="199" startOffset="28" endLine="202" endOffset="70"/></Target><Target id="@+id/switchAutoCache" view="com.google.android.material.switchmaterial.SwitchMaterial"><Expressions/><location startLine="221" startOffset="28" endLine="224" endOffset="70"/></Target><Target id="@+id/buttonCacheSettings" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="229" startOffset="24" endLine="236" endOffset="83"/></Target><Target id="@+id/buttonBatchDelete" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="249" startOffset="20" endLine="257" endOffset="79"/></Target><Target id="@+id/buttonClearAll" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="259" startOffset="20" endLine="267" endOffset="79"/></Target><Target id="@+id/recyclerViewCachedSongs" view="androidx.recyclerview.widget.RecyclerView"><Expressions/><location startLine="272" startOffset="16" endLine="277" endOffset="63"/></Target><Target id="@+id/textEmptyState" view="TextView"><Expressions/><location startLine="280" startOffset="16" endLine="289" endOffset="47"/></Target><Target id="@+id/progressBar" view="ProgressBar"><Expressions/><location startLine="298" startOffset="4" endLine="303" endOffset="35"/></Target></Targets></Layout>