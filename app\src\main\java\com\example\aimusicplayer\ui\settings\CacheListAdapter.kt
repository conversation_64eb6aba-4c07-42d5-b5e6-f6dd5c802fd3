package com.example.aimusicplayer.ui.settings

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.ListAdapter
import androidx.recyclerview.widget.RecyclerView
import com.example.aimusicplayer.data.cache.MusicFileCache
import com.example.aimusicplayer.databinding.ItemCachedSongBinding

/**
 * 缓存歌曲列表适配器
 */
class CacheListAdapter(
    private val onItemClick: (MusicFileCache.CacheInfo) -> Unit,
    private val onDeleteClick: (MusicFileCache.CacheInfo) -> Unit,
) : ListAdapter<MusicFileCache.CacheInfo, CacheListAdapter.CacheViewHolder>(CacheDiffCallback()) {

    private val selectedItems = mutableSetOf<MusicFileCache.CacheInfo>()
    private var isSelectionMode = false

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): CacheViewHolder {
        val binding = ItemCachedSongBinding.inflate(
            LayoutInflater.from(parent.context),
            parent,
            false,
        )
        return CacheViewHolder(binding)
    }

    override fun onBindViewHolder(holder: CacheViewHolder, position: Int) {
        val item = getItem(position)
        holder.bind(item)
    }

    inner class CacheViewHolder(
        private val binding: ItemCachedSongBinding,
    ) : RecyclerView.ViewHolder(binding.root) {

        fun bind(cacheInfo: MusicFileCache.CacheInfo) {
            binding.apply {
                // 歌曲信息
                textSongName.text = extractSongName(cacheInfo.fileName)
                textFileSize.text = formatFileSize(cacheInfo.fileSize)
                textCacheTime.text = formatTime(cacheInfo.cacheTime)
                textLastAccess.text = "最后播放: ${formatTime(cacheInfo.lastAccessTime)}"

                // 选择状态
                checkboxSelect.visibility = if (isSelectionMode) View.VISIBLE else View.GONE
                checkboxSelect.isChecked = selectedItems.contains(cacheInfo)

                // 点击事件
                root.setOnClickListener {
                    if (isSelectionMode) {
                        toggleSelection(cacheInfo)
                    } else {
                        onItemClick(cacheInfo)
                    }
                }

                // 长按进入选择模式
                root.setOnLongClickListener {
                    if (!isSelectionMode) {
                        enterSelectionMode()
                        toggleSelection(cacheInfo)
                    }
                    true
                }

                // 删除按钮
                buttonDelete.setOnClickListener {
                    onDeleteClick(cacheInfo)
                }

                // 选择框点击
                checkboxSelect.setOnClickListener {
                    toggleSelection(cacheInfo)
                }

                // 设置选中状态的背景
                root.isSelected = selectedItems.contains(cacheInfo)
            }
        }

        private fun extractSongName(fileName: String): String {
            // 从文件名中提取歌曲名（移除ID前缀和扩展名）
            val nameWithoutExtension = fileName.substringBeforeLast('.')
            val parts = nameWithoutExtension.split('_', limit = 2)
            return if (parts.size > 1) {
                parts[1].replace('_', ' ')
            } else {
                nameWithoutExtension
            }
        }

        private fun formatFileSize(bytes: Long): String {
            return when {
                bytes < 1024 -> "${bytes}B"
                bytes < 1024 * 1024 -> "${bytes / 1024}KB"
                bytes < 1024 * 1024 * 1024 -> "${bytes / 1024 / 1024}MB"
                else -> "${bytes / 1024 / 1024 / 1024}GB"
            }
        }

        private fun formatTime(timestamp: Long): String {
            val now = System.currentTimeMillis()
            val diff = now - timestamp

            return when {
                diff < 60 * 1000 -> "刚刚"
                diff < 60 * 60 * 1000 -> "${diff / (60 * 1000)}分钟前"
                diff < 24 * 60 * 60 * 1000 -> "${diff / (60 * 60 * 1000)}小时前"
                diff < 7 * 24 * 60 * 60 * 1000 -> "${diff / (24 * 60 * 60 * 1000)}天前"
                else -> {
                    val date = java.util.Date(timestamp)
                    java.text.SimpleDateFormat("MM-dd HH:mm", java.util.Locale.getDefault()).format(date)
                }
            }
        }
    }

    private fun toggleSelection(cacheInfo: MusicFileCache.CacheInfo) {
        if (selectedItems.contains(cacheInfo)) {
            selectedItems.remove(cacheInfo)
        } else {
            selectedItems.add(cacheInfo)
        }

        // 如果没有选中项，退出选择模式
        if (selectedItems.isEmpty()) {
            exitSelectionMode()
        }

        notifyDataSetChanged()
    }

    private fun enterSelectionMode() {
        isSelectionMode = true
        notifyDataSetChanged()
    }

    private fun exitSelectionMode() {
        isSelectionMode = false
        selectedItems.clear()
        notifyDataSetChanged()
    }

    /**
     * 获取选中的项目
     */
    fun getSelectedItems(): List<MusicFileCache.CacheInfo> {
        return selectedItems.toList()
    }

    /**
     * 获取选中项目数量
     */
    fun getSelectedCount(): Int {
        return selectedItems.size
    }

    /**
     * 是否处于选择模式
     */
    fun isInSelectionMode(): Boolean {
        return isSelectionMode
    }

    /**
     * 全选
     */
    fun selectAll() {
        selectedItems.clear()
        selectedItems.addAll(currentList)
        notifyDataSetChanged()
    }

    /**
     * 取消全选
     */
    fun clearSelection() {
        selectedItems.clear()
        exitSelectionMode()
    }

    /**
     * 手动退出选择模式
     */
    fun exitSelectionModeManually() {
        exitSelectionMode()
    }

    class CacheDiffCallback : DiffUtil.ItemCallback<MusicFileCache.CacheInfo>() {
        override fun areItemsTheSame(
            oldItem: MusicFileCache.CacheInfo,
            newItem: MusicFileCache.CacheInfo,
        ): Boolean {
            return oldItem.songId == newItem.songId
        }

        override fun areContentsTheSame(
            oldItem: MusicFileCache.CacheInfo,
            newItem: MusicFileCache.CacheInfo,
        ): Boolean {
            return oldItem == newItem
        }
    }
}
